"""SOP (Serial Optical Polarization) 客户端模块。

本模块提供SOP系统的客户端接口，用于连接SOP服务器并控制光学设备。
支持光开关(OSW)和SOP偏振控制器的远程操作，提供完整的设备控制API。

客户端功能特性：
- 自动连接管理和错误恢复
- 光开关资源申请和释放
- SOP设备的SCPI命令控制
- 完善的错误处理和日志记录

典型使用示例：
    client = SOP_Client("*************", 8000)
    try:
        client.acquire_osw()
        client.switch_in_out(1, 2)
        client.acquire_sop()
        client.sop_tri_state(True)
    finally:
        client.close()
"""

import socket
import time
from typing import Optional, Tuple, Union
from contextlib import contextmanager

from Tools.mylog import logger
from Config.config import config


class SOP_Client:
    """SOP系统客户端。

    提供与SOP服务器通信的高级接口，封装了所有设备控制命令。
    支持连接管理、资源控制和设备操作的完整功能。
    """
    # 支持的命令列表
    VALID_COMMANDS = {
        "ACQUIRE_OSW_IN", "RELEASE_OSW_IN",
        "ACQUIRE_OSW_OUT", "RELEASE_OSW_OUT",
        "ACQUIRE_SOP", "RELEASE_SOP",
        "SWITCH_IN", "SWITCH_OUT",
        "SOP_CMD", "ACQUIRE_INPUT_OSW_STATUS",
        "ACQUIRE_OUTPUT_OSW_STATUS", "ACQUIRE_SOP_STATUS"
    }

    def __init__(self, ip: str, port: Union[int, str], timeout: float = 30.0) -> None:
        """初始化SOP客户端。

        Args:
            ip: 服务器IP地址。
            port: 服务器端口号。
            timeout: socket超时时间（秒），默认30秒。

        Raises:
            ValueError: 当参数无效时抛出异常。
            ConnectionError: 当连接服务器失败时抛出异常。
            socket.error: 当socket操作失败时抛出异常。
        """
        self.logger = logger

        # 参数验证
        if not ip or not isinstance(ip, str):
            raise ValueError("服务器IP地址不能为空且必须为字符串")

        try:
            self.server_port = int(port)
            if not (1 <= self.server_port <= 65535):
                raise ValueError(f"端口号必须在1-65535范围内，当前值: {port}")
        except (ValueError, TypeError) as e:
            raise ValueError(f"无效的端口号: {port}") from e

        if timeout <= 0:
            raise ValueError(f"超时时间必须为正数，当前值: {timeout}")

        self.server_ip = ip
        self.timeout = timeout
        self.sock: Optional[socket.socket] = None
        self.is_connected = False

        # 建立连接
        self._connect()

    def _connect(self) -> None:
        """建立与服务器的连接。

        Raises:
            ConnectionError: 当连接失败时抛出异常。
        """
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(self.timeout)
            self.sock.connect((self.server_ip, self.server_port))
            self.is_connected = True
            self.logger.info(f"成功连接到SOP服务器: {self.server_ip}:{self.server_port}")

        except socket.timeout:
            error_msg = f"连接服务器超时: {self.server_ip}:{self.server_port}"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)
        except socket.error as e:
            error_msg = f"连接服务器失败: {self.server_ip}:{self.server_port} - {e}"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg) from e
        except Exception as e:
            error_msg = f"连接过程中发生未知错误: {e}"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg) from e

    def send_command(self, cmd: str, retry_count: int = 1) -> Optional[str]:
        """向服务器发送命令并接收响应。

        Args:
            cmd: 要发送的命令字符串。
            retry_count: 重试次数，默认为1次。

        Returns:
            服务器响应字符串，失败时返回None。

        Raises:
            无异常抛出，所有错误通过返回None表示。
        """
        if not cmd or not isinstance(cmd, str):
            self.logger.error("命令不能为空且必须为字符串")
            return None

        # 验证命令格式
        cmd_parts = cmd.split()
        if not cmd_parts or cmd_parts[0] not in self.VALID_COMMANDS:
            self.logger.error(f"无效的命令: {cmd}")
            return None

        # 检查连接状态
        if not self.is_connected or not self.sock:
            self.logger.error("客户端未连接到服务器")
            return None

        for attempt in range(retry_count + 1):
            try:
                # 发送命令
                message = f"{cmd}\n"
                self.sock.sendall(message.encode('utf-8'))

                # 接收响应
                response = self.sock.recv(1024).decode('utf-8').strip()

                if not response:
                    raise socket.error("服务器返回空响应")

                # 检查响应中的错误信息
                if any(keyword in response for keyword in ["失败", "错误", "Error", "error"]):
                    self.logger.warning(f"命令执行失败: {cmd} -> {response}")
                else:
                    self.logger.info(f"命令执行成功: {cmd} -> {response}")

                return response

            except socket.timeout:
                self.logger.error(f"命令发送超时 (尝试 {attempt + 1}/{retry_count + 1}): {cmd}")
            except (socket.error, ConnectionResetError, BrokenPipeError) as e:
                self.logger.error(f"网络连接错误 (尝试 {attempt + 1}/{retry_count + 1}): {e}")
                self.is_connected = False
            except UnicodeDecodeError as e:
                self.logger.error(f"响应解码失败: {e}")
            except Exception as e:
                self.logger.error(f"发送命令时发生未知错误: {e}", exc_info=True)

            # 如果不是最后一次尝试，等待后重试
            if attempt < retry_count:
                self.logger.info(f"等待1秒后重试命令: {cmd}")
                time.sleep(1)

        self.logger.error(f"命令发送失败，已重试 {retry_count} 次: {cmd}")
        return None

    def get_osw_status(self) -> Tuple[Optional[str], Optional[str]]:
        """获取所有光开关的状态。

        Returns:
            元组(输入光开关状态, 输出光开关状态)。
        """
        input_status = self.get_input_osw_status()
        output_status = self.get_output_osw_status()
        return input_status, output_status

    def get_input_osw_status(self) -> Optional[str]:
        """获取输入光开关状态。

        Returns:
            输入光开关状态字符串，失败时返回None。
        """
        return self.send_command("ACQUIRE_INPUT_OSW_STATUS")

    def get_output_osw_status(self) -> Optional[str]:
        """获取输出光开关状态。

        Returns:
            输出光开关状态字符串，失败时返回None。
        """
        return self.send_command("ACQUIRE_OUTPUT_OSW_STATUS")

    def get_sop_status(self) -> Optional[str]:
        """获取SOP设备状态。

        Returns:
            SOP设备状态字符串，失败时返回None。
        """
        return self.send_command("ACQUIRE_SOP_STATUS")

    def close(self) -> None:
        """关闭与服务器的连接。

        安全关闭socket连接并清理资源。
        """
        if self.sock:
            try:
                self.sock.close()
                self.logger.info(f"已断开与服务器的连接: {self.server_ip}:{self.server_port}")
            except Exception as e:
                self.logger.error(f"关闭连接时出错: {e}")
            finally:
                self.sock = None
                self.is_connected = False

    @contextmanager
    def connection(self):
        """连接上下文管理器。

        确保在使用完毕后自动关闭连接。

        使用示例:
            with SOP_Client("*************", 8000).connection():
                client.acquire_sop()
                client.sop_tri_state(True)
        """
        try:
            yield self
        finally:
            self.close()

    # ==================== 光开关资源管理方法 ====================

    def acquire_osw(self) -> bool:
        """申请所有光开关资源。

        Returns:
            True表示成功申请所有资源，False表示申请失败。
        """
        input_result = self.acquire_osw_in()
        output_result = self.acquire_osw_out()

        success = input_result is not None and output_result is not None
        if success:
            self.logger.info("成功申请所有光开关资源")
        else:
            self.logger.error("申请光开关资源失败")
        return success

    def release_osw(self) -> bool:
        """释放所有光开关资源。

        Returns:
            True表示成功释放所有资源，False表示释放失败。
        """
        input_result = self.release_osw_in()
        output_result = self.release_osw_out()

        success = input_result is not None and output_result is not None
        if success:
            self.logger.info("成功释放所有光开关资源")
        else:
            self.logger.error("释放光开关资源失败")
        return success

    def acquire_osw_in(self) -> Optional[str]:
        """申请输入光开关资源。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        return self.send_command("ACQUIRE_OSW_IN")

    def release_osw_in(self) -> Optional[str]:
        """释放输入光开关资源。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        return self.send_command("RELEASE_OSW_IN")

    def acquire_osw_out(self) -> Optional[str]:
        """申请输出光开关资源。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        return self.send_command("ACQUIRE_OSW_OUT")

    def release_osw_out(self) -> Optional[str]:
        """释放输出光开关资源。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        return self.send_command("RELEASE_OSW_OUT")

    # ==================== SOP资源管理方法 ====================

    def acquire_sop(self) -> Optional[str]:
        """申请SOP设备资源。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        return self.send_command("ACQUIRE_SOP")

    def release_sop(self) -> Optional[str]:
        """释放SOP设备资源。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        return self.send_command("RELEASE_SOP")

    # ==================== 光开关控制方法 ====================

    def switch_in(self, channel: Union[int, str]) -> Optional[str]:
        """切换输入光开关到指定通道。

        Args:
            channel: 目标通道号。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        if not self._validate_channel(channel):
            return None
        return self.send_command(f"SWITCH_IN {channel}")

    def switch_out(self, channel: Union[int, str]) -> Optional[str]:
        """切换输出光开关到指定通道。

        Args:
            channel: 目标通道号。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        if not self._validate_channel(channel):
            return None
        return self.send_command(f"SWITCH_OUT {channel}")

    def switch_in_out(self, in_channel: Union[int, str], out_channel: Union[int, str]) -> bool:
        """同时切换输入和输出光开关。

        Args:
            in_channel: 输入光开关目标通道号。
            out_channel: 输出光开关目标通道号。

        Returns:
            True表示两个光开关都切换成功，False表示至少一个失败。
        """
        input_result = self.switch_in(in_channel)
        output_result = self.switch_out(out_channel)

        success = input_result is not None and output_result is not None
        if success:
            self.logger.info(f"成功切换光开关: 输入通道{in_channel}, 输出通道{out_channel}")
        else:
            self.logger.error(f"光开关切换失败: 输入通道{in_channel}, 输出通道{out_channel}")
        return success

    def _validate_channel(self, channel: Union[int, str]) -> bool:
        """验证通道号的有效性。

        Args:
            channel: 要验证的通道号。

        Returns:
            True表示通道号有效，False表示无效。
        """
        try:
            channel_num = int(channel)
            if channel_num <= 0:
                self.logger.error(f"通道号必须为正整数: {channel}")
                return False
            return True
        except (ValueError, TypeError):
            self.logger.error(f"通道号必须为数字: {channel}")
            return False

    # ==================== SOP设备控制方法 ====================

    def sop_reset(self) -> Optional[str]:
        """重置SOP设备。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        return self.send_command("SOP_CMD RESET")

    def sop_tri_state(self, enable: bool) -> Optional[str]:
        """设置SOP三角扰乱模式。

        Args:
            enable: True启用三角扰乱模式，False禁用。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        state = "ON" if enable else "OFF"
        return self.send_command(f"SOP_CMD SET_TRI_STATE {state}")

    def sop_tri_rate(self, rate: Union[int, str]) -> Optional[str]:
        """设置SOP三角扰乱速率。

        Args:
            rate: 扰乱速率值。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        if not self._validate_positive_number(rate, "扰乱速率"):
            return None
        return self.send_command(f"SOP_CMD SET_TRI_RATE {rate}")

    def sop_set_baudrate(self, baudrate: Union[int, str]) -> Optional[str]:
        """设置SOP串口波特率。

        Args:
            baudrate: 波特率值。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        if not self._validate_positive_number(baudrate, "波特率"):
            return None
        return self.send_command(f"SOP_CMD SET_BAUDRATE {baudrate}")

    def sop_remote_control(self, enable: bool) -> Optional[str]:
        """设置SOP远程控制模式。

        Args:
            enable: True启用远程控制，False禁用。

        Returns:
            服务器响应字符串，失败时返回None。
        """
        state = "ON" if enable else "OFF"
        return self.send_command(f"SOP_CMD SET_REMOTE_CONTROL {state}")

    def sop_idn(self) -> Optional[str]:
        """获取SOP设备标识信息。

        Returns:
            设备标识字符串，失败时返回None。
        """
        return self.send_command("SOP_CMD GET_IDN")

    def sop_version(self) -> Optional[str]:
        """获取SOP设备版本信息。

        Returns:
            设备版本字符串，失败时返回None。
        """
        return self.send_command("SOP_CMD GET_VERSION")

    def _validate_positive_number(self, value: Union[int, str], name: str) -> bool:
        """验证正数参数的有效性。

        Args:
            value: 要验证的值。
            name: 参数名称，用于错误消息。

        Returns:
            True表示参数有效，False表示无效。
        """
        try:
            num_value = int(value)
            if num_value <= 0:
                self.logger.error(f"{name}必须为正整数: {value}")
                return False
            return True
        except (ValueError, TypeError):
            self.logger.error(f"{name}必须为数字: {value}")
            return False


def demo_client_usage() -> None:
    """演示SOP客户端的基本使用方法。

    展示光开关控制和SOP设备操作的完整流程。
    """
    # 从配置文件读取服务器地址
    try:
        server_ip = config.get_value("server", "server_ip", "***********")
        server_port = int(config.get_value("server", "server_port", "8000"))
    except Exception as e:
        logger.error(f"读取配置失败，使用默认值: {e}")
        server_ip = "***********"
        server_port = 8000

    client = None
    try:
        # 创建客户端连接
        client = SOP_Client(server_ip, server_port)
        logger.info("开始演示SOP客户端功能...")

        # 1. 查询设备状态
        logger.info("=== 查询设备状态 ===")
        client.get_osw_status()
        client.get_sop_status()
        time.sleep(1)

        # 2. 光开关操作演示
        logger.info("=== 光开关操作演示 ===")
        if client.acquire_osw():
            client.switch_in_out(6, 4)
            time.sleep(2)
            client.release_osw()
        time.sleep(1)

        # 3. SOP设备操作演示
        logger.info("=== SOP设备操作演示 ===")
        if client.acquire_sop():
            # 获取设备信息
            client.sop_idn()
            client.sop_version()

            # 配置设备参数
            client.sop_tri_state(True)
            client.sop_tri_rate(500)

            # 远程控制演示
            client.sop_remote_control(True)
            time.sleep(2)
            client.sop_remote_control(False)
            time.sleep(1)

            # 设置波特率
            client.sop_set_baudrate(6)

            # 释放资源
            client.release_sop()

        # 4. 最终状态查询
        logger.info("=== 最终状态查询 ===")
        time.sleep(2)
        client.get_osw_status()
        client.get_sop_status()

        logger.info("SOP客户端演示完成")

    except Exception as e:
        logger.error(f"客户端演示过程中发生错误: {e}", exc_info=True)
    finally:
        if client:
            client.close()


if __name__ == "__main__":
    """主程序入口。

    运行SOP客户端功能演示。
    """
    demo_client_usage()