import socket
import time
from Tools.mylog import logger
from Config.config import config


class SOP_Client:
    def __init__(self, ip, port):
        self.logger = logger
        self.server_ip = ip
        self.server_port = int(port)
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.connect((self.server_ip, self.server_port))
        logger.info(f"连接到服务器 {self.server_ip}:{self.server_port}")

    def send_command(self, cmd):
        valid_commands = ["ACQUIRE_OSW_IN", "RELEASE_OSW_IN",
                          "ACQUIRE_OSW_OUT", "RELEASE_OSW_OUT",
                          "ACQUIRE_SOP", "RELEASE_SOP",
                          "SWITCH_IN", "SWITCH_OUT",
                          "SOP_CMD", "ACQUIRE_INPUT_OSW_STATUS",
                          "ACQUIRE_OUTPUT_OSW_STATUS", "ACQUIRE_SOP_STATUS"
                          ]
        if cmd.split()[0] not in valid_commands:
            logger.error(f"无效的命令：{cmd}")
            return None
        try:
            message = f"{cmd}\n"
            self.sock.sendall(message.encode())
            time.sleep(1)
            response = self.sock.recv(1024).decode()
            if "失败" in response or "正被占用" in response:
                raise Exception(f"Command {cmd} failed: {response}")
            logger.info(f"发送命令: {cmd} 收到响应: {response}")
            return response
        except Exception as e:
            logger.error(f"发送命令失败: {str(e)}")
            return None

    def get_osw_status(self):
        return self.get_input_osw_status(), self.get_output_osw_status()

    def get_input_osw_status(self):
        return self.send_command("ACQUIRE_INPUT_OSW_STATUS")

    def get_output_osw_status(self):
        return self.send_command("ACQUIRE_OUTPUT_OSW_STATUS")

    def get_sop_status(self):
        return self.send_command("ACQUIRE_SOP_STATUS")

    def close(self):
        self.sock.close()
        logger.info("断开服务器连接")

    def acquire_osw(self):
        self.acquire_osw_in()
        return self.acquire_osw_out()

    def switch_in_out(self, in_channel, out_channel):
        self.switch_in(in_channel)
        return self.switch_out(out_channel)

    def release_osw(self):
        self.release_osw_in()
        return self.release_osw_out()

    def acquire_osw_in(self):
        return self.send_command("ACQUIRE_OSW_IN")

    def release_osw_in(self):
        return self.send_command("RELEASE_OSW_IN")

    def acquire_osw_out(self):
        return self.send_command("ACQUIRE_OSW_OUT")

    def release_osw_out(self):
        return self.send_command("RELEASE_OSW_OUT")

    def acquire_sop(self):
        return self.send_command("ACQUIRE_SOP")

    def release_sop(self):
        return self.send_command("RELEASE_SOP")

    def switch_in(self, channel):
        return self.send_command(f"SWITCH_IN {channel}")

    def switch_out(self, channel):
        return self.send_command(f"SWITCH_OUT {channel}")

    def sop_reset(self):
        return self.send_command("SOP_CMD RESET")

    def sop_tri_state(self, enable: bool):
        """根据布尔值启用或禁用远程控制模式"""
        state = "ON" if enable else "OFF"
        return self.send_command(f"SOP_CMD SET_TRI_STATE {state}")

    def sop_tri_rate(self, cmd):
        return self.send_command(f"SOP_CMD SET_TRI_RATE {cmd}")

    def sop_idn(self):
        return self.send_command("SOP_CMD GET_IDN")

    def sop_version(self):
        return self.send_command("SOP_CMD GET_VERSION")

    def sop_set_baudrate(self, cmd):
        return self.send_command(f"SOP_CMD SET_BAUDRATE {cmd}")

    def sop_remote_control(self, enable: bool):
        state = "ON" if enable else "OFF"
        return self.send_command(f"SOP_CMD SET_REMOTE_CONTROL {state}")


if __name__ == "__main__":
    client = SOP_Client("10.231.47.8", 8000)
    #while True:
    client.get_osw_status()
    time.sleep(2)
    client.acquire_osw()
    client.switch_in_out(6, 4)
    client.release_osw()

    time.sleep(2)
    client.get_osw_status()

    client.get_sop_status()
    client.acquire_sop()
    client.sop_idn()
    client.sop_version()
    client.sop_tri_state(True)
    client.sop_tri_rate(500)
    client.sop_remote_control(True)
    time.sleep(3)
    client.sop_remote_control(False)
    time.sleep(3)
    client.sop_set_baudrate(6)
    client.release_sop()
    time.sleep(6)
    client.get_sop_status()