"""配置管理模块。

本模块提供统一的配置文件管理接口，支持配置读取、写入、验证和监控。
采用INI格式配置文件，提供类型安全的配置访问和完善的错误处理机制。

配置管理功能特性：
- 配置文件自动发现和加载
- 类型安全的配置值获取
- 配置验证和默认值支持
- 配置文件监控和热重载
- 完善的错误处理和日志记录

典型使用示例：
    config = Config()
    server_ip = config.get_value("server", "server_ip", "localhost")
    server_port = config.get_int("server", "server_port", 8000)
"""

import configparser
import os
import threading
from typing import Any, Dict, List, Optional, Tuple, Union
from pathlib import Path

# 配置文件路径设置
BASE_DIR = Path(__file__).parent
CONFIG_PATH = BASE_DIR / 'config.ini'


class ConfigError(Exception):
    """配置相关异常类。"""
    pass


class Config:
    """配置管理器类。

    提供线程安全的配置文件读写操作，支持类型转换、默认值和配置验证。
    """
    def __init__(self, config_path: Optional[Union[str, Path]] = None) -> None:
        """初始化配置管理器。

        Args:
            config_path: 配置文件路径，默认使用模块目录下的config.ini。

        Raises:
            ConfigError: 当配置文件不存在或格式错误时抛出异常。
        """
        self.config_path = Path(config_path) if config_path else CONFIG_PATH
        self.cfg = configparser.ConfigParser()
        self._lock = threading.RLock()  # 线程安全锁

        # 验证并加载配置文件
        self._load_config()

        # 导入日志记录器（延迟导入避免循环依赖）
        try:
            from Tools.mylog import logger
            self.logger = logger
        except ImportError:
            import logging
            self.logger = logging.getLogger(__name__)

    def _load_config(self) -> None:
        """加载配置文件。

        Raises:
            ConfigError: 当配置文件不存在或格式错误时抛出异常。
        """
        if not self.config_path.exists():
            raise ConfigError(f"配置文件不存在: {self.config_path}")

        if not self.config_path.is_file():
            raise ConfigError(f"配置路径不是文件: {self.config_path}")

        try:
            with self._lock:
                self.cfg.read(self.config_path, encoding='utf-8')

            # 验证配置文件基本结构
            if not self.cfg.sections():
                raise ConfigError("配置文件为空或格式错误")

        except configparser.Error as e:
            raise ConfigError(f"配置文件格式错误: {e}") from e
        except Exception as e:
            raise ConfigError(f"加载配置文件失败: {e}") from e

    def get_value(self, section: str, option: str, default: Optional[str] = None) -> str:
        """获取配置值（字符串类型）。

        Args:
            section: 配置节名称。
            option: 配置项名称。
            default: 默认值，当配置项不存在时返回。

        Returns:
            配置值字符串。

        Raises:
            ConfigError: 当配置项不存在且未提供默认值时抛出异常。
        """
        try:
            with self._lock:
                return self.cfg.get(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError) as e:
            if default is not None:
                return default
            raise ConfigError(f"配置项不存在: [{section}].{option}") from e

    def get_int(self, section: str, option: str, default: Optional[int] = None) -> int:
        """获取配置值（整数类型）。

        Args:
            section: 配置节名称。
            option: 配置项名称。
            default: 默认值。

        Returns:
            配置值整数。

        Raises:
            ConfigError: 当配置项不存在或类型转换失败时抛出异常。
        """
        try:
            with self._lock:
                return self.cfg.getint(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            if default is not None:
                return default
            raise ConfigError(f"配置项不存在: [{section}].{option}")
        except ValueError as e:
            raise ConfigError(f"配置值类型错误，期望整数: [{section}].{option}") from e

    def get_float(self, section: str, option: str, default: Optional[float] = None) -> float:
        """获取配置值（浮点数类型）。

        Args:
            section: 配置节名称。
            option: 配置项名称。
            default: 默认值。

        Returns:
            配置值浮点数。

        Raises:
            ConfigError: 当配置项不存在或类型转换失败时抛出异常。
        """
        try:
            with self._lock:
                return self.cfg.getfloat(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            if default is not None:
                return default
            raise ConfigError(f"配置项不存在: [{section}].{option}")
        except ValueError as e:
            raise ConfigError(f"配置值类型错误，期望浮点数: [{section}].{option}") from e

    def get_bool(self, section: str, option: str, default: Optional[bool] = None) -> bool:
        """获取配置值（布尔类型）。

        Args:
            section: 配置节名称。
            option: 配置项名称。
            default: 默认值。

        Returns:
            配置值布尔值。

        Raises:
            ConfigError: 当配置项不存在或类型转换失败时抛出异常。
        """
        try:
            with self._lock:
                return self.cfg.getboolean(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            if default is not None:
                return default
            raise ConfigError(f"配置项不存在: [{section}].{option}")
        except ValueError as e:
            raise ConfigError(f"配置值类型错误，期望布尔值: [{section}].{option}") from e

    def set_value(self, section: str, option: str, value: Union[str, int, float, bool]) -> None:
        """设置配置值并保存到文件。

        Args:
            section: 配置节名称。
            option: 配置项名称。
            value: 配置值。

        Raises:
            ConfigError: 当写入配置文件失败时抛出异常。
        """
        try:
            with self._lock:
                # 确保节存在
                if not self.cfg.has_section(section):
                    self.cfg.add_section(section)

                # 设置配置值
                self.cfg.set(section, option, str(value))

                # 保存到文件
                with open(self.config_path, 'w', encoding='utf-8') as configfile:
                    self.cfg.write(configfile)

            if hasattr(self, 'logger'):
                self.logger.info(f"配置已更新: [{section}].{option} = {value}")

        except Exception as e:
            raise ConfigError(f"设置配置值失败: [{section}].{option} = {value}, 错误: {e}") from e

    def get_section(self, section: str) -> Dict[str, str]:
        """获取整个配置节的所有配置项。

        Args:
            section: 配置节名称。

        Returns:
            配置项字典。

        Raises:
            ConfigError: 当配置节不存在时抛出异常。
        """
        try:
            with self._lock:
                return dict(self.cfg.items(section))
        except configparser.NoSectionError as e:
            raise ConfigError(f"配置节不存在: {section}") from e

    def get_sections(self) -> List[str]:
        """获取所有配置节名称。

        Returns:
            配置节名称列表。
        """
        with self._lock:
            return self.cfg.sections()

    def has_section(self, section: str) -> bool:
        """检查配置节是否存在。

        Args:
            section: 配置节名称。

        Returns:
            True表示存在，False表示不存在。
        """
        with self._lock:
            return self.cfg.has_section(section)

    def has_option(self, section: str, option: str) -> bool:
        """检查配置项是否存在。

        Args:
            section: 配置节名称。
            option: 配置项名称。

        Returns:
            True表示存在，False表示不存在。
        """
        with self._lock:
            return self.cfg.has_option(section, option)

    def reload(self) -> None:
        """重新加载配置文件。

        Raises:
            ConfigError: 当重新加载失败时抛出异常。
        """
        try:
            self._load_config()
            if hasattr(self, 'logger'):
                self.logger.info("配置文件已重新加载")
        except Exception as e:
            raise ConfigError(f"重新加载配置文件失败: {e}") from e

    def validate_config(self) -> List[str]:
        """验证配置文件的完整性。

        Returns:
            验证错误信息列表，空列表表示验证通过。
        """
        errors = []

        # 必需的配置节和配置项
        required_configs = {
            'server': ['server_ip', 'server_port', 'max_threads'],
            'sop': ['com_port', 'baudrate', 'data_bits', 'stop_bits', 'parity'],
            'osw': ['input_osw1_ip', 'input_osw1_port', 'output_osw2_ip', 'output_osw2_port']
        }

        with self._lock:
            for section, options in required_configs.items():
                if not self.cfg.has_section(section):
                    errors.append(f"缺少必需的配置节: {section}")
                    continue

                for option in options:
                    if not self.cfg.has_option(section, option):
                        errors.append(f"缺少必需的配置项: [{section}].{option}")

        return errors


# 创建全局配置实例
try:
    config = Config()

    # 验证配置文件
    validation_errors = config.validate_config()
    if validation_errors:
        print("配置文件验证失败:")
        for error in validation_errors:
            print(f"  - {error}")
    else:
        print("配置文件验证通过")

except ConfigError as e:
    print(f"配置初始化失败: {e}")
    config = None
except Exception as e:
    print(f"配置初始化时发生未知错误: {e}")
    config = None


def demo_config_usage() -> None:
    """演示配置管理器的基本使用方法。"""
    if not config:
        print("配置实例未创建，无法执行演示")
        return

    print("=== 配置管理器功能演示 ===")

    try:
        # 1. 显示所有配置节
        print(f"配置节列表: {config.get_sections()}")

        # 2. 读取服务器配置
        print("\n--- 服务器配置 ---")
        server_ip = config.get_value("server", "server_ip", "localhost")
        server_port = config.get_int("server", "server_port", 8000)
        max_threads = config.get_int("server", "max_threads", 50)
        print(f"服务器地址: {server_ip}:{server_port}")
        print(f"最大线程数: {max_threads}")

        # 3. 读取SOP配置
        print("\n--- SOP设备配置 ---")
        sop_config = config.get_section("sop")
        for key, value in sop_config.items():
            print(f"  {key}: {value}")

        # 4. 读取OSW配置
        print("\n--- 光开关配置 ---")
        osw_config = config.get_section("osw")
        for key, value in osw_config.items():
            print(f"  {key}: {value}")

        # 5. 配置验证
        print("\n--- 配置验证 ---")
        errors = config.validate_config()
        if errors:
            print("发现配置错误:")
            for error in errors:
                print(f"  - {error}")
        else:
            print("配置验证通过")

    except ConfigError as e:
        print(f"配置操作失败: {e}")
    except Exception as e:
        print(f"演示过程中发生错误: {e}")


if __name__ == '__main__':
    """主程序入口。

    运行配置管理器功能演示。
    """
    demo_config_usage()
