import configparser
import os

# BASE_DIR = os.path.dirname(os.path.abspath(__file__))
# configPath = os.path.join(BASE_DIR, 'config_wcb.ini')
# configPath = 'config_tmp.ini'

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
configPath = os.path.join(BASE_DIR, 'config.ini')

import configparser
import os

class Config:
    def __init__(self):
        self.cfg = configparser.ConfigParser()
        self.configPath = configPath
        if not os.path.exists(configPath):
            raise FileNotFoundError(f"Config file {configPath} not found")
        self.cfg.read(configPath)

    def get_value(self, section, option, default=None):
        """
        支持默认值的配置获取
        """
        try:
            return self.cfg.get(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            if default is not None:
                return default
            else:
                raise

    def set_value(self, section, option, value):
        self.cfg.set(section, option, value)
        with open(self.configPath, 'w') as configfile:
            self.cfg.write(configfile)

    def get_options(self, section):
        return self.cfg.items(section)


config = Config()

if __name__ == '__main__':
    config = Config()
    print(config.cfg.sections())
