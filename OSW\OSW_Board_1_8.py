from OSW.OSW_base import OSW_base
from Ulits.ProtocolFramework import UDPFramework
from Ulits.mylog import logger
from Ulits.MySignal import my_sig
import time
import threading


class OSW_Board_1_8(UDPFramework, OSW_base):
    """
    基于OSP机框的1x8光开关，对应园类名为 OSW18_Board
    """

    def __init__(self, ip, port, sn="0x0003D018603E"):
        UDPFramework.__init__(self, ip, port)

        if type(sn) == str:
            sn = int(sn, 16)
        else:
            if '0x' not in str(sn):
                sn = hex(sn)
                sn = int(sn, 16)
            else:
                pass
        self.sn = int(hex(sn), 16).to_bytes(6, 'big')
        logger.info(f"1x8光开关 {ip}:{port} 初始化成功")


    # def switch_channel(self, channel_no):
    #     """
    #     :param channel_no: 希望切换光开关的编号
    #     :return: bool True切换成功 Flase切换失败
    #     """
    #     try:
    #         if type(channel_no) != int:
    #             channel_no = int(channel_no)
    #         if 1 <= channel_no <= 8:
    #             cmd_created = self.generate_cmd(channel_no)
    #             reply = self.send_recv(cmd_created)
    #             l = len(reply)
    #             if reply[-3:-2] == channel_no.to_bytes(1, 'big'):
    #                 for i in range(l):
    #                     if reply[i:i + 1] == b'\x92' and reply[i + 1:i + 2] == b'\x92':
    #                         logger.info(f'1x8光开光{self.ip}:{self.port} 通道切换为 {channel_no} 成功.')
    #                         time.sleep(0.1)
    #             return True
    #     except Exception as e:
    #         my_sig.infoSignal.emit(-1, f' {self.ip}:{self.port} switch_channel exception: {channel_no}')
    #         logger.error(f' {self.ip}:{self.port} switch_channel exception : {e}')
    #         return False

    def switch_channel(self, channel_no):
        """切换光开关通道，添加五次重试机制"""
        try:
            if type(channel_no) != int:
                channel_no = int(channel_no)
            if 1 <= channel_no <= 8:
                # 重试逻辑开始
                max_retries = 5
                for attempt in range(max_retries):
                    cmd_created = self.generate_cmd(channel_no)
                    reply = self.send_recv(cmd_created)
                    l = len(reply)
                    if reply[-3:-2] == channel_no.to_bytes(1, 'big'):
                        for i in range(l):
                            if reply[i:i + 1] == b'\x92' and reply[i + 1:i + 2] == b'\x92':
                                logger.info(f'1x8光开关{self.ip}:{self.port} 通道切换为 {channel_no} 成功.')
                                time.sleep(0.1)
                                return True  # 成功时立即返回
                    # 如果未触发return True，则继续重试或最终返回False
                # 五次重试后仍未成功
                logger.warning(f"1x8光开关{self.ip}:{self.port} 切换通道{channel_no} 重试{max_retries}次失败")
                return False
            else:
                logger.error(f"无效通道号: {channel_no}")
                return False
        except Exception as e:
            my_sig.infoSignal.emit(-1, f' {self.ip}:{self.port} switch_channel exception: {channel_no}')
            logger.error(f' {self.ip}:{self.port} switch_channel exception : {e}')
            return False

    def generate_cmd(self, num: bytes):
        ADD = b'\xEF\xEF\x00\x00\x23\x11\x00\xFF\x02\x00\x0C\x1F\x01\x00\x18\x00\x03\xD0\x11\x40\x02\x00\x00\x01\x00\x0D'
        FLAG1 = b'\xEF\xEF'  # 命令标识 (0xFEFE) 	2 bytes
        LEN1 = b'\x0a'  # len1后包括res数据长度：LEN1+RES*256
        RES = b'\x00'  # LEN高字节部分
        COMMAND = b'\x92'  # 命令码
        OptType = b'\x01'  # 操作类型：0-读取；1-设置
        AGREEMENT = b'\x0A\x0A'
        OswType = b'\x01'
        Channel = num.to_bytes(1, 'big')
        LEN2 = (len(AGREEMENT) + len(OswType) + len(Channel)).to_bytes(2, 'big')
        DATA = LEN2 + AGREEMENT + OswType + Channel
        cmd_bytes = FLAG1 + LEN1 + RES + COMMAND + OptType + DATA
        SUM = (sum(cmd_bytes) & 0xff).to_bytes(1, 'big')
        cmd_bytes += SUM

        final_bytes = ADD + cmd_bytes
        sum_total = (sum(final_bytes) & 0xff).to_bytes(1, 'big')
        final_bytes += sum_total
        return final_bytes


    def get_channel_num(self):
        """
        获取光开关通道数
        :return: int:光开关通道数
        """
        return 8


    def get_current_channel(self):
        """
        获取当前光开关所在通道
        :return: int:当前光开关所在通道
        """
        return 1

    # def acquire(self, client_id):
    #     with self.osw_lock:
    #         if self.locked_by is None:
    #             self.locked_by = client_id
    #             logger.info(f"光开关被客户端{client_id}锁定")
    #             return True
    #         return False
    #
    #
    # def release(self, client_id):
    #     with self.osw_lock:
    #         if self.locked_by == client_id:
    #             self.locked_by = None
    #             logger.info(f"光开关被客户端{client_id}释放")
    #             return True
    #         return False


if __name__ == '__main__':
    while True:
        osw_1 = OSW_Board_1_8('192.168.35.131', 9000)
        print(osw_1.switch_channel(1))
        time.sleep(1)

        osw_2 = OSW_Board_1_8('192.168.4.164', 9000)
        print(osw_2.switch_channel(2))

        osw_3 = OSW_Board_1_8('192.168.35.131', 9000)
        print(osw_3.switch_channel(3))

        time.sleep(1)
        osw_4 = OSW_Board_1_8('192.168.4.164', 9000)
        print(osw_4.switch_channel(4))
