from device_interface.osw.OSW_base import OSW_base, COMFramework,my_sig
from device_interface.utils.bytes_helper import BytesHelper
from device_interface.utils.mylog import logger


class OSW_COM(COMFramework, OSW_base):
    """
    基于串口的光开关，对应园类名为OSW_COM
    """

    def __init__(self, com, boudrate):
        COMFramework.__init__(self, com, boudrate)
        self.num = int(boudrate)
        self.get_osw()
        logger.info(f"{self.__class__.__name__}-{com} 初始化成功")


    def switch_channel(self,  state, num=1):
        b = BytesHelper()
        b.add_int_l(0xaa, 1)  # 起始标志
        b.add_int_d(0x4, 2)  # 命令帧长
        b.add_int_l(0x1, 1)  # 协议类型
        b.add_int_l(self.num, num)  # 光开关号
        b.add_int_l(state, num)  # 切换通道号
        b.add_int_l(b.check_sum(0, b.position) & 0xff, 1)  # 校验和
        cmd = b.get_packet()
        self.send(cmd)
        reply = self.recv()
        if reply[6] == 1:
            logger.info(f'{self.__class__.__name__}: switch_channel {state} ok')
        else:
            raise ValueError('fail')


    def get_osw(self):
        b = BytesHelper()
        b.add_int_l(0xaa, 1)  # 起始标志
        b.add_int_d(0x2, 2)  # 命令帧长
        b.add_int_l(0x2, 1)  # 协议类型
        b.add_int_l(b.check_sum(0, b.position) & 0xff, 1)  # 校验和
        cmd = b.get_packet()
        self.send(cmd)
        reply = self.recv()
        self.status = reply[4:8]

if __name__ == '__main__':
    osw = OSW_COM('COM1',192000)
    osw.switch_channel(8)