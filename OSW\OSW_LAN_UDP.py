from device_interface.osw.OSW_base import OSW_base, UDPFramework,logger,my_sig
import time


class OSW_LAN_UDP(UDPFramework, OSW_base):
    """
    基于UDP通信协议的光开关，对应园类名为OLP_Board
    """

    def __init__(self, ip, port, sn="0x0003D0114002"):
        UDPFramework.__init__(self, ip, port)
        if type(sn) == str:
            sn = int(sn, 16)
        else:
            if '0x' not in str(sn):
                sn = hex(sn)
                sn = int(sn, 16)
            else:
                pass
        self.sn = int(hex(sn), 16).to_bytes(6, 'big')
        logger.info(f"{self.__class__.__name__}-{ip}:{port} 初始化成功")


    def switch_channel(self, channel_no):
        """
        :param channel_no: 希望切换光开关的编号
        :return: bool True切换成功 Flase切换失败
        """
        try:
            cmd_created = self.generate_cmd(channel_no)
            self.send(cmd_created)
            reply = self.recv()
            if reply[-3:-2] == channel_no.to_bytes(1, 'big'):
                for i in range(len(reply)):
                    if reply[i:i + 1] == b'\x92' and reply[i + 1:i + 2] == b'\x92':
                        logger.info('OSW通道设置为 %d 成功' % channel_no)
                        time.sleep(0.5)
            return True
        except Exception as e:
            logger.error(f'{self.__class__.__name__}: switch_channel exception: {e}')
            return False


    def set_mode(self, mode):
        try:
            cmd = b'\x03'
            flag = b'\xef'
            Len = b'\x1e'
            code = b'\x20'
            rest = b'\x15\x00\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\x00\x00\x00\x00\x00'
            SUM = (sum(flag + Len + code + mode + rest) & 0xff).to_bytes(1, 'big')
            cmd_send = cmd + flag + Len + code + mode + rest + SUM
            cmd_created = self.generate_cmd(cmd_send)
            self.send(cmd_created)
            reply = self.recv()
            cmd_return = self.parse_data(reply)
            if cmd_return == b'\x03\x53':
                logger.info('%s(%s)设置为%s 成功' % (self.__class__.__name__, self.ip, self.mode_disp_dict[mode]))
                return True
            elif cmd_return == b'\x03\x46':
                logger.error('%s(%s)设置为%s 失败' % (self.__class__.__name__, self.ip, self.mode_disp_dict[mode]))
                my_sig.sig_show_log_info.emit('%s(%s)设置为%s 失败' % (self.__class__.__name__, self.ip, self.mode_disp_dict[mode]))
            else:
                logger.error('%s(%s)设置为%s 返回值异常' % (self.__class__.__name__, self.ip, self.mode_disp_dict[mode]))
                my_sig.sig_show_log_info.emit('%s(%s)设置为%s 返回值异常' % (self.__class__.__name__, self.ip, self.mode_disp_dict[mode]))
            return False
        except Exception as e:
            logger.error(f'{self.__class__.__name__}: set_mode exception: {e}')
            return False


    def generate_cmd(self, CMD: bytes):
        CMD1_LEN = len(CMD).to_bytes(2, 'big')
        CMDNUM = b'\x01'
        ModuleData = CMDNUM + CMD1_LEN + CMD

        RES = b'\x00\x00'
        Slot_SN = self.sn
        SlotLen = (len(ModuleData) + len(RES) + len(Slot_SN)).to_bytes(2, 'big')
        SLOT1DATA = SlotLen + Slot_SN + RES + ModuleData

        SlotNum = b'\x01'
        DATA = SlotNum + SLOT1DATA

        FLAG1 = b'\xef\xef\x00'
        VER = b'\x11'
        MFLAG = b'\x00'
        COMM = b'\xff\x02'
        TCOMM = b'\x00\x06'
        HSUM = b'\x00'
        SUM = b'\x00'
        LEN = (len(VER) + len(MFLAG) + len(COMM) + len(TCOMM) + len(HSUM) + len(DATA) + len(SUM)).to_bytes(2, 'big')

        header = FLAG1 + LEN + VER + MFLAG + COMM + TCOMM
        HSUM = (sum(header) & 0xff).to_bytes(1, 'big')
        SUM = (sum(header + HSUM + DATA) & 0xff).to_bytes(1, 'big')
        cmd_created = header + HSUM + DATA + SUM
        return cmd_created

    def parse_data(self, reply: bytes):
        FLAG2 = b'\xfe\xfe\x00'
        l = len(reply)
        flag_found = False
        for i in range(l):
            flag = reply[i:(i + 3)]
            if flag == FLAG2:
                flag_found = True
                LEN = (reply[(i + 3)] << 8) + reply[(i + 4)]
                CMD_return = reply[(i):(i + 5 + LEN)]
                Reply_Data = CMD_return[12:(4 + LEN)]
                if Reply_Data[0] != 0xff:
                    SLOT1DATA = Reply_Data[1:]
                    SlotLen = (SLOT1DATA[0] << 8) + SLOT1DATA[1]
                    Slot_SN = SLOT1DATA[2:8]
                    SLOT_SNXX_CMDREly = SLOT1DATA[10:]
                    if SLOT_SNXX_CMDREly[0] != 0xff:
                        CMDNUM = SLOT_SNXX_CMDREly[0]
                        CMD1_LEN = (SLOT_SNXX_CMDREly[1] << 8) + SLOT_SNXX_CMDREly[2]
                        CMD1_DATA = SLOT_SNXX_CMDREly[3:]
                        return CMD1_DATA
                    else:
                        return b''
                else:
                    return b''
        if not flag_found:
            return b''

    def get_channel_num(self):
        """
        获取光开关通道数
        :return: int:光开关通道数
        """
        return 8

    def get_current_channel(self):
        """
        获取当前光开关所在通道
        :return: int:当前光开关所在通道
        """
        logger.error(f'{self.__class__.__name__}: get_current_channel not implement.')
        return 1

if __name__ == '__main__':
    osw = OSW_LAN_UDP('192.168.241.22',9000)
    osw.switch_channel(8)