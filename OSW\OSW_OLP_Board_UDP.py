import socket
import time
from device_interface.osw.OSW_base import OSW_base, UDPFramework, my_sig, logger


class OSW_OLP_Board_UDP(UDPFramework, OSW_base):
    """
    基于OLP单盘的光开关类,UDP通信协议，对应原类名为OSW18_Board
    """
    mode_dict = {0: b'\x70', 1: b'\x80'}
    mode_disp_dict = {b'\x50': '手动', b'\x70': '主路', b'\x80': '从路'}

    def __init__(self, ip, port, sn='0x0003D018603E'):
        UDPFramework.__init__(self, ip, port)
        if type(sn) == str:
            sn = int(sn, 16)
        else:
            if '0x' not in str(sn):
                sn = hex(sn)
                sn = int(sn, 16)
            else:
                pass
        self.sn = int(hex(sn), 16).to_bytes(6, 'big')
        self.status = 0
        if not self.set_mode(b'\x50'):
            logger.error(f'{ip}:{port} 初始化时设置模式失败')
            my_sig.errorSingal.emit(-1, f'{ip}:{port} 初始化时设置模式失败')
        else:
            logger.info(f"{ip}:{port} 初始化成功")


    def switch_channel(self, channel_no):
        """
        :param channel_no: 希望切换光开关的编号
        :return: bool True切换成功 Flase切换失败
        """
        try:
            if channel_no < 1 or channel_no > 8:
                my_sig.infoSignal.emit(-1, f' {self.ip} switch_channel invalid channel_no: {channel_no}')
                logger.error(f' {self.ip} switch_channel invalid channel_no: {channel_no}')
                return False

            channel_no = int(channel_no) - 1
            status = [channel_no]
            if len(status) != 0:
                mode = self.mode_dict[status[0]]
            else:
                i = 1 - self.status
                mode = self.mode_dict[i]
            if self.set_mode(mode):
                if len(status) != 0:
                    self.status = status[0]
                else:
                    self.status = 1 - self.status
            time.sleep(0.1)
            return True
        except Exception as e:
            logger.error(f' {self.ip} switch_channel {channel_no} exception: {e}')
            my_sig.infoSignal.emit(-1, f' {self.ip}:{self.port} switch_channel exception: {channel_no}')
            return False

    def set_mode(self, mode):
        cmd = b'\x03'
        flag = b'\xef'
        Len = b'\x1e'
        code = b'\x20'

        rest = b'\x00\x00\xFE\x0C\xFF\xA6\xFF\x9C\xFF\x4C\xFF\x60\xFF\x4C\xFF\x42\xFF\x92\x00\x00\x00\xB2\x00\x00\x00\x00\x00'
        SUM = (sum(flag + Len + code + mode + rest) & 0xff).to_bytes(1, 'big')
        cmd_send = cmd + flag + Len + code + mode + rest + SUM
        cmd_created = self.generate_cmd(cmd_send)
        reply = self.send_recv(cmd_created)
        cmd_return = self.parse_data(reply)
        if cmd_return == b'\x03\x53':
            logger.info(f'{self.ip} 通道设置为{mode} {self.mode_disp_dict[mode]} 成功')
            return True
        elif cmd_return == b'\x03\x46':
            logger.error(f'{self.ip} 通道设置为{mode} {self.mode_disp_dict[mode]} 失败')
        else:
            logger.error(f'{self.ip} 通道设置为{mode} {self.mode_disp_dict[mode]} 异常')
        return False

    def generate_cmd(self, CMD: bytes):
        CMD1_LEN = len(CMD).to_bytes(2, 'big')
        CMDNUM = b'\x01'
        ModuleData = CMDNUM + CMD1_LEN + CMD

        RES = b'\x00\x00'
        Slot_SN = self.sn
        SlotLen = (len(ModuleData) + len(RES) + len(Slot_SN)).to_bytes(2, 'big')
        SLOT1DATA = SlotLen + Slot_SN + RES + ModuleData

        SlotNum = b'\x01'
        DATA = SlotNum + SLOT1DATA

        FLAG1 = b'\xef\xef\x00'
        VER = b'\x11'
        MFLAG = b'\x00'
        COMM = b'\xff\x02'
        TCOMM = b'\x00\x06'
        HSUM = b'\x00'
        SUM = b'\x00'
        LEN = (len(VER) + len(MFLAG) + len(COMM) + len(TCOMM) + len(HSUM) + len(DATA) + len(SUM)).to_bytes(2, 'big')

        header = FLAG1 + LEN + VER + MFLAG + COMM + TCOMM
        HSUM = (sum(header) & 0xff).to_bytes(1, 'big')
        SUM = (sum(header + HSUM + DATA) & 0xff).to_bytes(1, 'big')
        cmd_created = header + HSUM + DATA + SUM
        return cmd_created

    def parse_data(self, reply: bytes):
        FLAG2 = b'\xfe\xfe\x00'
        l = len(reply)
        flag_found = False
        for i in range(l):
            flag = reply[i:(i + 3)]
            if flag == FLAG2:
                flag_found = True
                LEN = (reply[(i + 3)] << 8) + reply[(i + 4)]
                CMD_return = reply[(i):(i + 5 + LEN)]
                Reply_Data = CMD_return[12:(4 + LEN)]
                if Reply_Data[0] != 0xff:
                    SLOT1DATA = Reply_Data[1:]
                    SlotLen = (SLOT1DATA[0] << 8) + SLOT1DATA[1]
                    Slot_SN = SLOT1DATA[2:8]
                    SLOT_SNXX_CMDREly = SLOT1DATA[10:]
                    if SLOT_SNXX_CMDREly[0] != 0xff:
                        CMDNUM = SLOT_SNXX_CMDREly[0]
                        CMD1_LEN = (SLOT_SNXX_CMDREly[1] << 8) + SLOT_SNXX_CMDREly[2]
                        CMD1_DATA = SLOT_SNXX_CMDREly[3:]
                        return CMD1_DATA
                    else:
                        return b''
                else:
                    return b''
        if not flag_found:
            return b''

    def get_channel_num(self):
        """
        获取光开关通道数
        :return: int:光开关通道数
        """
        return 2

    def get_current_channel(self):
        """
        获取当前光开关所在通道
        :return: int:当前光开关所在通道
        """
        logger.error(f' get_current_channel not implement.')
        return 1


if __name__ == '__main__':
    osw = OSW_OLP_Board_UDP('192.168.25.102', 9000)
    #osw.switch_channel(2)
