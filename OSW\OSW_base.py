import struct

from Ulits.mylog import logger
import time
from Ulits.ProtocolFramework import TCPFramework,UDPFramework,COMFramework
from Ulits.MySignal import my_sig



class OSW_base:
    """
    光开关基类
    """
    def __init__(self,sn):
        pass

    def switch_channel(self, channel_no):
        """
        :param channel_no: 希望切换光开关的编号
        :return: bool True切换成功 Flase切换失败
        """
        pass

    def set_mode(self, mode):
        pass

    def generate_cmd(self, CMD: bytes):
        pass

    def parse_data(self, reply: bytes):
        pass

    def get_channel_num(self):
        """
        获取光开关通道数
        :return: int:光开关通道数
        """
        pass

    def get_current_channel(self):
        """
        获取当前光开关所在通道
        :return: int:当前光开关所在通道
        """
        pass


