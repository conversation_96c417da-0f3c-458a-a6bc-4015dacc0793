# -*- conding: utf-8 -*-
# @Author: WangYi
# #Time: 2025/6/6 10:52

import serial
import time
from Config.config import config
from Tools.mylog import logger


class SOPController:
    def __init__(self):
        self.ser = None
        self.logger = logger
        self.init_serial()
        self.init_eos_mapping()
        self.init_baud_rates()
        self.logger.info("SOP设备初始化完成")

    def init_serial(self):
        try:
            self.logger.info("初始化SOP串口，请稍后")
            # 处理校验位（PARITY）的配置
            parity_config = config.get_value('sop', 'parity').upper()  # 转为大写处理
            self.parity = serial.PARITY_NONE  # 默认值
            parity_map = {
                'N': serial.PARITY_NONE,
                'E': serial.PARITY_EVEN,
                'O': serial.PARITY_ODD,
                'M': serial.PARITY_MARK,
                'S': serial.PARITY_SPACE
            }
            self.parity = parity_map.get(parity_config, serial.PARITY_NONE)
            self.ser = serial.Serial(
                port=config.get_value('sop', 'com_port'),
                baudrate=int(config.get_value('sop', 'baudrate')),
                bytesize=int(config.get_value('sop', 'data_bits')),
                stopbits=int(config.get_value('sop', 'stop_bits')),
                parity=self.parity,
                timeout=1
            )
            self.logger.info("SOP串口初始化成功")
        except Exception as e:
            self.logger.error(f"串口初始化失败: {str(e)}")
            raise

    def init_eos_mapping(self):
        self.eos_mapping = {
            'LF': '\n',
            'CR': '\r',
            'CR+LF': '\r\n'
        }

    def init_baud_rates(self):
        self.baud_rates = {
            0: 110, 1: 300, 2: 600, 3: 1200, 4: 2400, 5: 4800,
            6: 9600, 7: 19200, 8: 28800, 9: 38400, 10: 43000,
            11: 56000, 12: 57600
        }

    def add_eos(self, command):
        eos = self.eos_mapping.get(config.get_value('sop', 'eos'), '\r\n')
        return command + eos

    def send_command(self, command):
        if not self.ser or not self.ser.is_open:
            logger.error("SOP设备未连接")
            return None
        try:
            formatted_cmd = self.add_eos(command)
            self.ser.write(formatted_cmd.encode())
            time.sleep(1)
            response = self.ser.read_all().decode().strip()
            if not response:
                response = "OK"
                self.logger.info(f"发送命令: {command} 收到响应: {response}")
                return response if response else "OK"
            else:
                self.logger.info(f"发送命令: {command} 收到响应: {response}")
                return response if response else "OK"
        except Exception as e:
            self.logger.error(f"发送SOP命令失败: {str(e)}")
            return f"ERROR: {str(e)}"

    # SCPI 命令集合
    def get_idn(self):
        ret = self.send_command("*IDN?")
        return ret

    def get_version(self):
        ret = self.send_command(":SYSTem:VERSion?")
        return ret

    #:SYST:SER:BAUD 6
    def set_baudrate(self, rate_code):
        if type(rate_code) != int:
            rate_code = int(rate_code)
        if rate_code not in self.baud_rates:
            self.logger.error(f"无效波特率代码: {rate_code}")
            return "错误: 无效波特率代码"
        ret = self.send_command(f":SYST:SER:BAUD {rate_code}")
        return ret

    def get_baudrate(self):
        ret = self.send_command(":SYST:SER:BAUD?")
        return ret

    def set_tri_rate(self, rate):
        if type(rate) != int:
            rate = int(rate)
        ret = self.send_command(f":TRI:RATE {rate}")
        return ret

    def get_tri_rate(self):
        ret = self.send_command(":TRI:RATE?")
        time.sleep(2)
        return ret

    def set_tri_state(self, state):
        cmd = ":TRI:RATE ON" if state else ":TRI:RATE OFF"
        ret = self.send_command(cmd)
        return ret

    def set_remote_control_state(self, state):
        cmd = ":DISP:LOCK ON" if state else ":DISP:LOCK OFF"
        ret = self.send_command(cmd)
        return ret

    def reset_device(self):
        ret = self.send_command("*RST")
        return ret

    def get_mode(self):
        ret = self.send_command(":STATus:MODE?")
        return ret


    def initialize_sop(self):
        self.logger.info("SOP设备初始化相关步骤：重置SOP，开启远程控制，开启扰乱模式，设置扰乱模式速率，关闭远程控制")
        results = []
        results.append(self.reset_device())
        time.sleep(2)
        results.append(self.set_tri_state(True))
        time.sleep(2)
        results.append(self.set_tri_rate(500))
        time.sleep(2)
        return results

sop_controller = SOPController()

if __name__ == "__main__":
    #:DISP:LOCK ON
    #:TRI:STAT ON
    #:TRI:RATE 500
    #:DISP:LOCK OFF

    time.sleep(2)
    print("reset_device:", sop_controller.reset_device())
    time.sleep(5)
    print("IDN:", sop_controller.get_idn())
    time.sleep(5)
    print("Version:", sop_controller.get_version())
    time.sleep(5)
    print("set_display_lock_on:", sop_controller.set_remote_control_state(True))
    time.sleep(5)
    print("get_baudrate:", sop_controller.get_baudrate())
    time.sleep(5)
    print("set_baudrate:", sop_controller.set_baudrate(6))
    time.sleep(5)
    print("set_tri_state:", sop_controller.set_tri_state(False))
    time.sleep(5)
    print("set_tri_rate:", sop_controller.set_tri_rate(500))
    time.sleep(5)
    print("set_tri_state", sop_controller.set_tri_state(True))
    time.sleep(5)
    print("set_display_lock_off:", sop_controller.set_remote_control_state(False))
    time.sleep(5)
    print("get_mode:", sop_controller.get_mode())
    time.sleep(5)
    print("get_baudrate:", sop_controller.get_baudrate())
    time.sleep(5)
    print("get_tri_rate:", sop_controller.get_tri_rate())

