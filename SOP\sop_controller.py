"""SOP (Serial Optical Polarization) 控制器模块。

本模块提供SOP偏振控制器的串口通信和SCPI命令控制接口。
支持设备初始化、参数配置、状态查询和远程控制等完整功能。

SOP控制器功能特性：
- 串口通信管理和自动重连
- 完整的SCPI命令集支持
- 设备状态监控和错误恢复
- 三角扰乱模式控制
- 远程控制模式管理

典型使用示例：
    controller = SOPController()
    controller.initialize_sop()
    controller.set_tri_state(True)
    controller.set_tri_rate(500)

作者: WangYi
创建时间: 2025/6/6 10:52
"""

import serial
import time
from typing import Optional, Dict, Union, Any
from enum import Enum

from Config.config import config
from Tools.mylog import logger


class SOPController:
    """SOP偏振控制器类。

    负责管理SOP设备的串口通信和SCPI命令执行。
    提供设备初始化、参数配置和状态查询的完整接口。
    """
    class ParityType(Enum):
        """串口校验位类型枚举。"""
        NONE = 'N'
        EVEN = 'E'
        ODD = 'O'
        MARK = 'M'
        SPACE = 'S'

    def __init__(self) -> None:
        """初始化SOP控制器。

        从配置文件读取串口参数，建立与SOP设备的连接，
        初始化命令映射表和波特率映射表。

        Raises:
            serial.SerialException: 当串口初始化失败时抛出异常。
            ValueError: 当配置参数无效时抛出异常。
        """
        self.ser: Optional[serial.Serial] = None
        self.logger = logger
        self.is_connected = False

        # 初始化配置映射表
        self._init_eos_mapping()
        self._init_baud_rates()
        self._init_parity_mapping()

        # 初始化串口连接
        self._init_serial()

        self.logger.info("SOP控制器初始化完成")

    def _init_serial(self) -> None:
        """初始化串口连接。

        从配置文件读取串口参数并建立连接。

        Raises:
            serial.SerialException: 当串口操作失败时抛出异常。
            ValueError: 当配置参数无效时抛出异常。
        """
        try:
            self.logger.info("正在初始化SOP串口连接...")

            # 读取并验证配置参数
            com_port = config.get_value('sop', 'com_port')
            baudrate = int(config.get_value('sop', 'baudrate'))
            data_bits = int(config.get_value('sop', 'data_bits'))
            stop_bits = int(config.get_value('sop', 'stop_bits'))
            parity_config = config.get_value('sop', 'parity', 'N').upper()

            # 验证参数有效性
            if not com_port:
                raise ValueError("串口端口不能为空")
            if baudrate <= 0:
                raise ValueError(f"波特率必须为正数: {baudrate}")
            if data_bits not in [5, 6, 7, 8]:
                raise ValueError(f"数据位必须为5-8: {data_bits}")
            if stop_bits not in [1, 1.5, 2]:
                raise ValueError(f"停止位必须为1, 1.5或2: {stop_bits}")

            # 获取校验位设置
            parity = self.parity_mapping.get(parity_config, serial.PARITY_NONE)

            # 创建串口连接
            self.ser = serial.Serial(
                port=com_port,
                baudrate=baudrate,
                bytesize=data_bits,
                stopbits=stop_bits,
                parity=parity,
                timeout=2.0,  # 增加超时时间
                write_timeout=2.0
            )

            self.is_connected = True
            self.logger.info(f"SOP串口初始化成功: {com_port}@{baudrate}")

        except serial.SerialException as e:
            error_msg = f"串口连接失败: {e}"
            self.logger.error(error_msg)
            raise serial.SerialException(error_msg) from e
        except (ValueError, TypeError) as e:
            error_msg = f"串口配置参数无效: {e}"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e
        except Exception as e:
            error_msg = f"串口初始化过程中发生未知错误: {e}"
            self.logger.error(error_msg)
            raise

    def _init_eos_mapping(self) -> None:
        """初始化行结束符映射表。

        定义SCPI命令的行结束符格式，支持LF、CR和CR+LF三种格式。
        """
        self.eos_mapping: Dict[str, str] = {
            'LF': '\n',
            'CR': '\r',
            'CR+LF': '\r\n'
        }

    def _init_baud_rates(self) -> None:
        """初始化波特率代码映射表。

        定义SOP设备支持的波特率代码与实际波特率的对应关系。
        """
        self.baud_rates: Dict[int, int] = {
            0: 110, 1: 300, 2: 600, 3: 1200, 4: 2400, 5: 4800,
            6: 9600, 7: 19200, 8: 28800, 9: 38400, 10: 43000,
            11: 56000, 12: 57600
        }

    def _init_parity_mapping(self) -> None:
        """初始化校验位映射表。

        定义配置文件中的校验位字符与pyserial库常量的对应关系。
        """
        self.parity_mapping: Dict[str, int] = {
            'N': serial.PARITY_NONE,
            'E': serial.PARITY_EVEN,
            'O': serial.PARITY_ODD,
            'M': serial.PARITY_MARK,
            'S': serial.PARITY_SPACE
        }

    def _add_eos(self, command: str) -> str:
        """为命令添加行结束符。

        Args:
            command: 原始SCPI命令字符串。

        Returns:
            添加了行结束符的命令字符串。
        """
        eos_config = config.get_value('sop', 'eos', 'CR+LF')
        eos = self.eos_mapping.get(eos_config, '\r\n')
        return command + eos

    def send_command(self, command: str, timeout: float = 3.0) -> Optional[str]:
        """向SOP设备发送SCPI命令并接收响应。

        Args:
            command: 要发送的SCPI命令字符串。
            timeout: 命令超时时间（秒），默认3秒。

        Returns:
            设备响应字符串，失败时返回None。

        Raises:
            无异常抛出，所有错误通过返回None表示。
        """
        if not command or not isinstance(command, str):
            self.logger.error("命令不能为空且必须为字符串")
            return None

        # 检查连接状态
        if not self._check_connection():
            return None

        try:
            # 准备命令
            formatted_cmd = self._add_eos(command.strip())

            # 清空输入缓冲区
            if self.ser.in_waiting > 0:
                self.ser.reset_input_buffer()

            # 发送命令
            self.ser.write(formatted_cmd.encode('utf-8'))
            self.ser.flush()  # 确保数据发送完成

            # 等待响应
            time.sleep(0.1)  # 给设备处理时间

            # 读取响应
            response = ""
            start_time = time.time()

            while time.time() - start_time < timeout:
                if self.ser.in_waiting > 0:
                    data = self.ser.read_all().decode('utf-8', errors='ignore')
                    response += data
                    break
                time.sleep(0.01)

            # 处理响应
            response = response.strip()
            if not response:
                response = "OK"  # 某些命令无响应时默认为成功

            self.logger.info(f"SCPI命令执行: {command} -> {response}")
            return response

        except serial.SerialTimeoutException:
            self.logger.error(f"SCPI命令超时: {command}")
            return None
        except serial.SerialException as e:
            self.logger.error(f"串口通信错误: {e}")
            self.is_connected = False
            return None
        except UnicodeDecodeError as e:
            self.logger.error(f"响应解码失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"发送SCPI命令时发生未知错误: {e}", exc_info=True)
            return None

    def _check_connection(self) -> bool:
        """检查串口连接状态。

        Returns:
            True表示连接正常，False表示连接异常。
        """
        if not self.ser:
            self.logger.error("串口对象未初始化")
            return False

        if not self.ser.is_open:
            self.logger.error("串口连接已关闭")
            self.is_connected = False
            return False

        return True

    # ==================== SCPI命令接口 ====================

    def get_idn(self) -> Optional[str]:
        """获取设备标识信息。

        发送*IDN?命令获取设备的制造商、型号、序列号和固件版本信息。

        Returns:
            设备标识字符串，失败时返回None。
        """
        return self.send_command("*IDN?")

    def get_version(self) -> Optional[str]:
        """获取设备固件版本信息。

        Returns:
            固件版本字符串，失败时返回None。
        """
        return self.send_command(":SYSTem:VERSion?")

    def set_baudrate(self, rate_code: Union[int, str]) -> Optional[str]:
        """设置串口波特率。

        Args:
            rate_code: 波特率代码（0-12），对应不同的波特率值。

        Returns:
            命令执行结果，失败时返回None。

        波特率代码对应表：
            0: 110, 1: 300, 2: 600, 3: 1200, 4: 2400, 5: 4800,
            6: 9600, 7: 19200, 8: 28800, 9: 38400, 10: 43000,
            11: 56000, 12: 57600
        """
        try:
            rate_code_int = int(rate_code)
            if rate_code_int not in self.baud_rates:
                self.logger.error(f"无效的波特率代码: {rate_code_int}, 有效范围: 0-12")
                return None

            actual_baudrate = self.baud_rates[rate_code_int]
            self.logger.info(f"设置波特率: 代码{rate_code_int} -> {actual_baudrate}bps")
            return self.send_command(f":SYST:SER:BAUD {rate_code_int}")

        except (ValueError, TypeError):
            self.logger.error(f"波特率代码必须为数字: {rate_code}")
            return None

    def get_baudrate(self) -> Optional[str]:
        """获取当前串口波特率代码。

        Returns:
            当前波特率代码字符串，失败时返回None。
        """
        return self.send_command(":SYST:SER:BAUD?")

    def set_tri_rate(self, rate: Union[int, str]) -> Optional[str]:
        """设置三角扰乱速率。

        Args:
            rate: 扰乱速率值，单位通常为Hz。

        Returns:
            命令执行结果，失败时返回None。
        """
        try:
            rate_int = int(rate)
            if rate_int <= 0:
                self.logger.error(f"扰乱速率必须为正数: {rate}")
                return None

            return self.send_command(f":TRI:RATE {rate_int}")
        except (ValueError, TypeError):
            self.logger.error(f"扰乱速率必须为数字: {rate}")
            return None

    def get_tri_rate(self) -> Optional[str]:
        """获取当前三角扰乱速率。

        Returns:
            当前扰乱速率字符串，失败时返回None。
        """
        result = self.send_command(":TRI:RATE?")
        if result:
            time.sleep(0.1)  # 给设备处理时间
        return result

    def set_tri_state(self, state: bool) -> Optional[str]:
        """设置三角扰乱模式开关状态。

        Args:
            state: True启用三角扰乱模式，False禁用。

        Returns:
            命令执行结果，失败时返回None。

        注意：原代码中的命令有误，应该是:TRI:STAT而不是:TRI:RATE
        """
        cmd = ":TRI:STAT ON" if state else ":TRI:STAT OFF"
        status = "启用" if state else "禁用"
        self.logger.info(f"设置三角扰乱模式: {status}")
        return self.send_command(cmd)

    def set_remote_control_state(self, state: bool) -> Optional[str]:
        """设置远程控制模式开关状态。

        Args:
            state: True启用远程控制，False禁用。

        Returns:
            命令执行结果，失败时返回None。
        """
        cmd = ":DISP:LOCK ON" if state else ":DISP:LOCK OFF"
        status = "启用" if state else "禁用"
        self.logger.info(f"设置远程控制模式: {status}")
        return self.send_command(cmd)

    def reset_device(self) -> Optional[str]:
        """重置SOP设备到默认状态。

        Returns:
            命令执行结果，失败时返回None。
        """
        self.logger.info("正在重置SOP设备...")
        result = self.send_command("*RST")
        if result:
            time.sleep(2)  # 重置后等待设备稳定
            self.logger.info("SOP设备重置完成")
        return result

    def get_mode(self) -> Optional[str]:
        """获取设备当前工作模式。

        Returns:
            设备工作模式字符串，失败时返回None。
        """
        return self.send_command(":STATus:MODE?")


    def initialize_sop(self) -> bool:
        """初始化SOP设备到工作状态。

        执行完整的设备初始化序列：
        1. 重置设备到默认状态
        2. 启用三角扰乱模式
        3. 设置扰乱速率为500Hz

        Returns:
            True表示初始化成功，False表示初始化失败。
        """
        self.logger.info("开始SOP设备初始化序列...")

        try:
            # 步骤1: 重置设备
            self.logger.info("步骤1: 重置SOP设备")
            if not self.reset_device():
                self.logger.error("设备重置失败")
                return False
            time.sleep(2)  # 等待设备重置完成

            # 步骤2: 启用三角扰乱模式
            self.logger.info("步骤2: 启用三角扰乱模式")
            if not self.set_tri_state(True):
                self.logger.error("启用三角扰乱模式失败")
                return False
            time.sleep(1)

            # 步骤3: 设置扰乱速率
            self.logger.info("步骤3: 设置三角扰乱速率为500Hz")
            if not self.set_tri_rate(500):
                self.logger.error("设置三角扰乱速率失败")
                return False
            time.sleep(1)

            self.logger.info("SOP设备初始化序列完成")
            return True

        except Exception as e:
            self.logger.error(f"SOP设备初始化过程中发生错误: {e}", exc_info=True)
            return False

    def close(self) -> None:
        """关闭串口连接并清理资源。"""
        if self.ser and self.ser.is_open:
            try:
                self.ser.close()
                self.logger.info("SOP串口连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭串口时出错: {e}")
            finally:
                self.ser = None
                self.is_connected = False

    def reconnect(self) -> bool:
        """重新连接SOP设备。

        Returns:
            True表示重连成功，False表示重连失败。
        """
        self.logger.info("尝试重新连接SOP设备...")

        # 关闭现有连接
        self.close()

        try:
            # 重新初始化串口
            self._init_serial()
            self.logger.info("SOP设备重连成功")
            return True
        except Exception as e:
            self.logger.error(f"SOP设备重连失败: {e}")
            return False

# 创建全局SOP控制器实例
# 注意：在生产环境中建议使用单例模式或依赖注入来管理实例
try:
    sop_controller = SOPController()
except Exception as e:
    logger.error(f"创建SOP控制器实例失败: {e}")
    sop_controller = None


def demo_sop_controller() -> None:
    """演示SOP控制器的基本功能。

    展示设备信息查询、参数配置和模式控制的完整流程。
    """
    if not sop_controller:
        logger.error("SOP控制器实例未创建，无法执行演示")
        return

    logger.info("开始SOP控制器功能演示...")

    try:
        # 1. 设备信息查询
        logger.info("=== 设备信息查询 ===")
        print(f"设备标识: {sop_controller.get_idn()}")
        time.sleep(1)
        print(f"固件版本: {sop_controller.get_version()}")
        time.sleep(1)
        print(f"当前波特率代码: {sop_controller.get_baudrate()}")
        time.sleep(1)
        print(f"当前工作模式: {sop_controller.get_mode()}")
        time.sleep(1)

        # 2. 远程控制模式演示
        logger.info("=== 远程控制模式演示 ===")
        print(f"启用远程控制: {sop_controller.set_remote_control_state(True)}")
        time.sleep(2)
        print(f"禁用远程控制: {sop_controller.set_remote_control_state(False)}")
        time.sleep(1)

        # 3. 三角扰乱模式演示
        logger.info("=== 三角扰乱模式演示 ===")
        print(f"禁用三角扰乱: {sop_controller.set_tri_state(False)}")
        time.sleep(1)
        print(f"设置扰乱速率: {sop_controller.set_tri_rate(500)}")
        time.sleep(1)
        print(f"启用三角扰乱: {sop_controller.set_tri_state(True)}")
        time.sleep(1)
        print(f"当前扰乱速率: {sop_controller.get_tri_rate()}")
        time.sleep(1)

        # 4. 波特率配置演示
        logger.info("=== 波特率配置演示 ===")
        print(f"设置波特率代码为6(9600bps): {sop_controller.set_baudrate(6)}")
        time.sleep(1)
        print(f"当前波特率代码: {sop_controller.get_baudrate()}")
        time.sleep(1)

        # 5. 设备重置演示
        logger.info("=== 设备重置演示 ===")
        print(f"重置设备: {sop_controller.reset_device()}")
        time.sleep(3)  # 重置后需要更长等待时间

        logger.info("SOP控制器功能演示完成")

    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}", exc_info=True)
    finally:
        # 确保设备处于安全状态
        try:
            sop_controller.set_remote_control_state(False)
        except:
            pass


if __name__ == "__main__":
    """主程序入口。

    运行SOP控制器功能演示。
    """
    demo_sop_controller()

