import socket
import threading
from Tools.mylog import logger
from Config.config import config
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor
from SOP.sop_controller import sop_controller
from OSW.OSW_Board_1_8 import OSW_Board_1_8


class ServerHandler:
    # 使用枚举类型管理资源（可动态扩展）
    from enum import Enum
    class ResourceType(Enum):
        INPUT_OSW = "input_osw"
        OUTPUT_OSW = "output_osw"
        SOP = "sop"

    # 动态注册资源（可从配置文件读取）
    VALID_RESOURCES = {rt.value for rt in ResourceType}

    resource_lock = {}  # 格式： {资源名: 客户端ID}
    client_resources = defaultdict(set)  # 格式： {客户端ID: {资源名集合}} # 客户端资源映射（客户端ID → 资源类型集合）
    lock = threading.Lock()  # 全局锁，确保所有操作线程安全

    def __init__(self, sop_controller):  # 接收共享实例
        self.sop = sop_controller  # 共享实例
        self.sop.initialize_sop()
        self.logger = logger

        self.input_osw = OSW_Board_1_8(
            config.get_value('osw', 'input_osw1_ip'),
            int(config.get_value('osw', 'input_osw1_port'))
        )
        self.output_osw = OSW_Board_1_8(
            config.get_value('osw', 'output_osw2_ip'),
            int(config.get_value('osw', 'output_osw2_port'))
        )

        # 客户端socket和地址由外部注入
        self.client_sock = None
        self.client_address = None

    def set_connection(self, sock, address):
        """设置客户端连接信息"""
        self.client_sock = sock
        self.client_address = address

    def handle_client(self):
        '''
        接收并处理客户端的请求
        管理客户端占用的资源
        在客户端断开时释放资源并清理记录
        '''
        try:
            # self.client_sock.settimeout(30)  # 防止 recv 阻塞过久
            while True:
                self.recv_buffer = ""  # 初始化缓冲区
                try:
                    #data = self.client_sock.recv(1024).decode().strip()
                    data = self.client_sock.recv(1024)
                    if not data:
                        break
                    self.recv_buffer += data.decode()  # 累积数据到缓冲区

                    # 按行拆分命令（以换行符为边界）
                    while '\n' in self.recv_buffer:
                        line, self.recv_buffer = self.recv_buffer.split('\n', 1)
                        line = line.strip()  # 去除多余空格
                        if line:  # 跳过空行
                            self.logger.info(f"收到客户端{self.client_address}的请求：{line}")
                            self.process_command(line)
                except ConnectionResetError as cre:  # 显式捕获异常断开
                    self.logger.error(f"客户端 {self.client_address} 异常断开：{str(cre)}")
                    break
                except socket.timeout:
                    self.logger.warning("客户端请求超时")
                    break
                except Exception as e:
                    self.logger.error(f"处理请求时出错：{str(e)}", exc_info=True)
                    self.send_response(f"错误：{str(e)}")
        finally:
            # 关闭套接字 确保释放与客户端的 TCP 连接。
            # 安全关闭套接字
            try:
                self.client_sock.shutdown(socket.SHUT_RDWR)
                self.client_sock.close()
            except (AttributeError, socket.error) as e:
                self.logger.error(f"关闭套接字失败: {str(e)}")
            finally:
                self.client_sock = None
            self.logger.info("服务端主动关闭客户端连接")
            client_id = f"{self.client_address[0]}:{self.client_address[1]}"
            with ServerHandler.lock:
                for resource in list(ServerHandler.client_resources.get(client_id, set())):
                    # 仅当资源确实由该客户端持有时才释放
                    if ServerHandler.resource_lock.get(resource) == client_id:
                        del ServerHandler.resource_lock[resource]
                        self.logger.info(f"强制释放资源：{resource}")
                    ServerHandler.client_resources[client_id].discard(resource)  # 清理客户端记录
                # 移除客户端资源记录（可选）
                ServerHandler.client_resources.pop(client_id, None)
                self.logger.info(f"客户端 {client_id} 异常断开，资源清理完成")

    def process_command(self, cmd):
        # 分解命令
        parts = cmd.split()
        if not parts:
            return
        action = parts[0]
        # 资源申请与释放处理
        if action == "ACQUIRE_OSW_IN":
            self.acquire_resource('input_osw')
        elif action == "ACQUIRE_OSW_OUT":
            self.acquire_resource('output_osw')
        elif action == "ACQUIRE_SOP":
            self.acquire_resource('sop')
        elif action == "RELEASE_OSW_IN":
            self.release_resource('input_osw')
        elif action == "RELEASE_OSW_OUT":
            self.release_resource('output_osw')
        elif action == "RELEASE_SOP":
            self.release_resource('sop')
        elif action == "ACQUIRE_INPUT_OSW_STATUS":
            status = self.get_resource_status("input_osw")
            self.send_response(status)
        elif action == "ACQUIRE_OUTPUT_OSW_STATUS":
            status = self.get_resource_status("output_osw")
            self.send_response(status)
        elif action == "ACQUIRE_SOP_STATUS":
            status = self.get_resource_status("sop")
            self.send_response(status)

        # 其他操作命令
        elif action == "SWITCH_IN":
            self.switch_in(parts[1])

        elif action == "SWITCH_OUT":
            self.switch_out(parts[1])

        elif action.startswith("SOP_CMD"):
            self.handle_sop_command(cmd)
        else:
            self.send_response("命令无效，请检查格式")
            self.logger.error("命令无效，请检查格式")

    def get_resource_status(self, resource_name):
        if resource_name not in ServerHandler.VALID_RESOURCES:
            self.send_response(f"错误：无效资源类型 {resource_name}")
            return

        with ServerHandler.lock:
            current_owner = ServerHandler.resource_lock.get(resource_name)
            if current_owner:
                self.send_response(f"***状态：{resource_name} {current_owner} 正被占用")
                return False, f"***状态：{resource_name} {current_owner} 正被占用"
            else:
                self.send_response(f"***状态：{resource_name} 当前未被占用")
                return True, f"***状态：{resource_name} 当前未被占用"

    def acquire_resource(self, resource_name):
        client_id = f"{self.client_address[0]}:{self.client_address[1]}"
        status = self.get_resource_status(resource_name)
        if resource_name not in ServerHandler.VALID_RESOURCES:
            self.send_response(f"错误：无效的资源类型：{resource_name}")
            return

        # 新增双重检查机制
        if f"被 {client_id} 占用" in status:
            self.send_response(f"警告：您当前已持有资源{resource_name}，请释放后重试")
            return

        with ServerHandler.lock:
            current_owner = ServerHandler.resource_lock.get(resource_name)

            # 新增自检逻辑：判断是否重复申请
            if current_owner == client_id:
                self.send_response(f"警告：资源 {resource_name} 已被您占用，请先释放或断开连接")
                return

            if current_owner is None:
                # 正常申请流程
                ServerHandler.resource_lock[resource_name] = client_id
                ServerHandler.client_resources[client_id].add(resource_name)
                self.send_response(f"成功申请资源{resource_name},{resource_name} 已分配给您")
                self.logger.info(f"客户端{client_id}成功申请{resource_name}")
            else:
                self.send_response(f"拒绝：资源{resource_name}正被{current_owner}占用")

    def release_resource(self, resource_name):
        client_id = f"{self.client_address[0]}:{self.client_address[1]}"
        if resource_name not in ServerHandler.VALID_RESOURCES:
            self.send_response(f"无效的资源类型：{resource_name}")
            return

        with ServerHandler.lock:
            if ServerHandler.resource_lock.get(resource_name) == client_id:
                del ServerHandler.resource_lock[resource_name]
                ServerHandler.client_resources[client_id].discard(resource_name)
                self.send_response(f"成功:已释放资源{resource_name}")
                self.logger.info(f"客户端{client_id}释放资源{resource_name}")
            else:
                self.send_response(f"您未拥有资源{resource_name}")

    def switch_in(self, channel):
        client_id = f"{self.client_address[0]}:{self.client_address[1]}"
        if self.resource_lock['input_osw'] != client_id:
            self.send_response("请先申请进光光开关资源")
            return
        try:
            self.input_osw.switch_channel(int(channel))
            self.send_response(f"进光光开关切换到通道{channel}")
        except Exception as e:
            self.send_response(f"切换进光光开关失败：{str(e)}")

    def switch_out(self, channel):
        client_id = f"{self.client_address[0]}:{self.client_address[1]}"
        if self.resource_lock['output_osw'] != client_id:
            self.send_response("请先申请出光光开关资源")
            return
        try:
            self.output_osw.switch_channel(int(channel))
            self.send_response(f"出光光开关切换到通道{channel}")
        except Exception as e:
            self.send_response(f"切换出光光开关失败：{str(e)}")

    # def send_response(self, message):
    #     """添加发送失败保护"""
    #     try:
    #         if self.client_sock:
    #             self.client_sock.sendall(message.encode('utf-8'))
    #     except (socket.error, AttributeError) as e:
    #         self.logger.error(f"发送响应失败: {str(e)}")

    def send_response(self, message):
        try:
            self.client_sock.sendall(f"{message}\n".encode())
        except Exception as e:
            self.logger.error(f"发送响应失败：{str(e)}")

    def handle_sop_command(self, sop_cmd):
        client_id = f"{self.client_address[0]}:{self.client_address[1]}"
        if self.resource_lock['sop'] != client_id:
            self.send_response("请先申请SOP设备资源")
            return
        _, sub_cmd, *args = sop_cmd.split()
        if sub_cmd == "SET_REMOTE_CONTROL":
            try:
                state = args[0]
                if state == "ON":
                    self.sop.set_remote_control_state(True)
                    self.send_response("SOP已启动远程控制模式模式")
                if state == "OFF":
                    self.sop.set_remote_control_state(False)
                    self.send_response("SOP已关闭远程控制模式模式")
            except Exception as e:
                self.send_response(f"设置SOP三角扰乱模式失败：{str(e)}")
        elif sub_cmd == "SET_TRI_STATE":
            try:
                state = args[0]
                if state == "ON":
                    self.sop.set_tri_state(True)
                    self.send_response("SOP已启动三角扰乱模式")
                if state == "OFF":
                    self.sop.set_tri_state(False)
                    self.send_response("SOP已关闭三角扰乱模式")
            except Exception as e:
                self.send_response(f"设置SOP三角扰乱模式失败：{str(e)}")
        elif sub_cmd == "SET_TRI_RATE":
            try:
                rate = int(args[0])
                self.sop.set_tri_rate(rate)
                self.send_response(f"SOP三角速率设置为{rate}")
            except Exception as e:
                self.send_response(f"设置SOP三角速率失败：{str(e)}")
        elif sub_cmd == "SET_BAUDRATE":
            try:
                baurate = int(args[0])
                self.sop.set_baudrate(baurate)
                self.send_response(f"SOP三角波特率代表值设置为: {baurate}")
            except Exception as e:
                self.send_response(f"设置SOP三角波特率失败：{str(e)}")
        elif sub_cmd == "RESET":
            self.sop.reset_device()
            self.send_response("SOP已重启")
        elif sub_cmd == "GET_IDN":
            ret = self.sop.get_idn()
            self.send_response(f"SOP返回的idn为{ret}")
        elif sub_cmd == "GET_VERSION":
            ret = self.sop.get_version()
            self.send_response(f"SOP返回版本为{ret}")
        else:
            self.send_response("SOP命令无效")


class SOP_Server:

    def __init__(self):
        self.max_threads = int(config.get_value("server", "max_threads"))
        self.executor = ThreadPoolExecutor(max_workers=self.max_threads)
        self.sop_controller = sop_controller  # 只初始化一次
        self.server_socket = None

    def start(self):
        # 从配置文件获取服务器监听地址和端口
        # 从配置文件获取服务器ip以及port
        self.server_ip = config.get_value("server", "server_ip")
        self.server_port = int(config.get_value("server", "server_port"))
        self.client_address = (self.server_ip, self.server_port)

        # 创建并配置socket
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.server_socket.bind((self.server_ip, self.server_port))
        self.server_socket.listen(5)

        while True:
            client_sock, client_addr = self.server_socket.accept()
            # 在accept()循环内为每个新连接创建独立的ServerHandler实例：
            handler = ServerHandler(self.sop_controller)  # 新建实例传入
            logger.info(f"服务器已启动，监听地址：{self.server_ip}:{self.server_port}")
            handler.set_connection(client_sock, client_addr)

            # 启动新线程处理客户端请求 使用线程池代替直接创建线程
            self.executor.submit(handler.handle_client)


if __name__ == "__main__":
    server = SOP_Server()
    try:
        server.start()
    except KeyboardInterrupt:
        server.server_socket.close()
        server.executor.shutdown(wait=True)
        logger.info("服务器已关闭")
