"""SOP (Serial Optical Polarization) 服务器模块。

本模块提供TCP服务器，用于管理SOP设备和光开关(OSW)设备。
支持多客户端并发连接、基于资源的访问控制和光学设备的命令处理。

服务器功能特性：
- SOP设备和光开关的资源访问控制
- 线程池并发处理客户端连接
- 光开关操作的命令处理
- 通过SCPI命令控制SOP设备

典型使用示例：
    server = SOP_Server()
    server.start()
"""

import socket
import threading
import time
from enum import Enum
from typing import Dict, Set, Optional, Tuple, Any
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor

from Tools.mylog import logger
from Config.config import config
from SOP.sop_controller import sop_controller
from OSW.OSW_Board_1_8 import OSW_Board_1_8


class ServerHandler:
    """服务器请求处理器。

    负责处理单个客户端连接的所有请求，包括资源管理、设备控制和命令处理。
    每个客户端连接对应一个ServerHandler实例，确保线程安全的资源访问。
    """
    class ResourceType(Enum):
        """资源类型枚举。

        定义服务器管理的所有设备资源类型，支持动态扩展。
        """
        INPUT_OSW = "input_osw"    # 输入光开关
        OUTPUT_OSW = "output_osw"  # 输出光开关
        SOP = "sop"                # SOP偏振控制器

    # 类级别的资源管理变量
    VALID_RESOURCES: Set[str] = {rt.value for rt in ResourceType}
    resource_lock: Dict[str, str] = {}  # 资源锁映射：{资源名: 客户端ID}
    client_resources: Dict[str, Set[str]] = defaultdict(set)  # 客户端资源映射：{客户端ID: {资源名集合}}
    lock = threading.Lock()  # 全局锁，确保所有资源操作线程安全

    def __init__(self, sop_controller) -> None:
        """初始化服务器处理器。

        Args:
            sop_controller: 共享的SOP控制器实例，用于设备通信。

        Raises:
            Exception: 当OSW设备初始化失败时抛出异常。
        """
        self.sop = sop_controller
        self.logger = logger

        # 初始化SOP设备
        try:
            self.sop.initialize_sop()
            self.logger.info("SOP控制器初始化成功")
        except Exception as e:
            self.logger.error(f"SOP控制器初始化失败: {e}")
            raise

        # 初始化光开关设备，增强错误处理
        try:
            self.input_osw = OSW_Board_1_8(
                config.get_value('osw', 'input_osw1_ip'),
                int(config.get_value('osw', 'input_osw1_port'))
            )
            self.output_osw = OSW_Board_1_8(
                config.get_value('osw', 'output_osw2_ip'),
                int(config.get_value('osw', 'output_osw2_port'))
            )
            self.logger.info("光开关设备初始化成功")
        except Exception as e:
            self.logger.error(f"光开关设备初始化失败: {e}")
            raise

        # 客户端连接信息（由外部设置）
        self.client_sock: Optional[socket.socket] = None
        self.client_address: Optional[Tuple[str, int]] = None
        self.recv_buffer: str = ""

    def set_connection(self, sock: socket.socket, address: Tuple[str, int]) -> None:
        """设置客户端连接信息。

        Args:
            sock: 客户端socket连接。
            address: 客户端地址元组(IP, 端口)。
        """
        self.client_sock = sock
        self.client_address = address
        self.logger.info(f"设置客户端连接: {address[0]}:{address[1]}")

    def handle_client(self) -> None:
        """处理客户端连接和请求。

        主要功能：
        - 接收并处理客户端的命令请求
        - 管理客户端占用的设备资源
        - 在客户端断开时自动释放资源并清理记录
        - 提供完善的异常处理和错误恢复机制

        异常处理：
        - ConnectionResetError: 客户端异常断开
        - socket.timeout: 客户端请求超时
        - 其他异常: 记录详细错误信息并继续服务
        """
        client_id = f"{self.client_address[0]}:{self.client_address[1]}"
        self.logger.info(f"开始处理客户端连接: {client_id}")

        try:
            # 设置socket超时，防止长时间阻塞
            self.client_sock.settimeout(30.0)

            while True:
                try:
                    # 接收客户端数据
                    data = self.client_sock.recv(1024)
                    if not data:
                        self.logger.info(f"客户端 {client_id} 正常断开连接")
                        break

                    # 累积数据到接收缓冲区
                    self.recv_buffer += data.decode('utf-8')

                    # 按行处理命令（以换行符为分隔符）
                    while '\n' in self.recv_buffer:
                        line, self.recv_buffer = self.recv_buffer.split('\n', 1)
                        line = line.strip()

                        if line:  # 跳过空行
                            self.logger.info(f"收到客户端 {client_id} 的请求: {line}")
                            self.process_command(line)

                except ConnectionResetError as e:
                    self.logger.warning(f"客户端 {client_id} 异常断开连接: {e}")
                    break
                except socket.timeout:
                    self.logger.warning(f"客户端 {client_id} 请求超时")
                    break
                except UnicodeDecodeError as e:
                    self.logger.error(f"客户端 {client_id} 数据解码失败: {e}")
                    self.send_response("错误: 数据格式无效")
                except Exception as e:
                    self.logger.error(f"处理客户端 {client_id} 请求时出错: {e}", exc_info=True)
                    self.send_response(f"服务器内部错误: {str(e)}")

        finally:
            # 安全关闭客户端连接
            self._cleanup_client_connection(client_id)

    def _cleanup_client_connection(self, client_id: str) -> None:
        """清理客户端连接和相关资源。

        Args:
            client_id: 客户端标识符，格式为"IP:端口"。

        功能：
        - 安全关闭socket连接
        - 释放客户端持有的所有设备资源
        - 清理客户端资源记录
        """
        # 安全关闭socket连接
        if self.client_sock:
            try:
                self.client_sock.shutdown(socket.SHUT_RDWR)
                self.client_sock.close()
                self.logger.info(f"已关闭客户端 {client_id} 的socket连接")
            except (AttributeError, socket.error, OSError) as e:
                self.logger.warning(f"关闭客户端 {client_id} socket时出现异常: {e}")
            finally:
                self.client_sock = None

        # 释放客户端持有的所有资源
        with ServerHandler.lock:
            client_resources = ServerHandler.client_resources.get(client_id, set())
            released_resources = []

            for resource in list(client_resources):
                # 仅当资源确实由该客户端持有时才释放
                if ServerHandler.resource_lock.get(resource) == client_id:
                    del ServerHandler.resource_lock[resource]
                    released_resources.append(resource)

                # 清理客户端资源记录
                ServerHandler.client_resources[client_id].discard(resource)

            # 移除客户端资源记录
            if client_id in ServerHandler.client_resources:
                del ServerHandler.client_resources[client_id]

            if released_resources:
                self.logger.info(f"客户端 {client_id} 断开，已释放资源: {released_resources}")
            else:
                self.logger.info(f"客户端 {client_id} 断开，无需释放资源")

    def process_command(self, cmd: str) -> None:
        """处理客户端命令并路由到相应的处理器。

        Args:
            cmd: 客户端发送的命令字符串。

        支持的命令类型：
            - ACQUIRE_OSW_IN/OUT: 申请光开关资源
            - RELEASE_OSW_IN/OUT: 释放光开关资源
            - ACQUIRE_SOP/RELEASE_SOP: 申请/释放SOP设备
            - ACQUIRE_*_STATUS: 获取资源状态
            - SWITCH_IN/OUT <通道>: 切换光开关通道
            - SOP_CMD <子命令> [参数]: 执行SOP设备命令
        """
        parts = cmd.split()
        if not parts:
            self.send_response("错误: 空命令")
            return

        action = parts[0]

        try:
            # 资源申请命令
            if action == "ACQUIRE_OSW_IN":
                self.acquire_resource('input_osw')
            elif action == "ACQUIRE_OSW_OUT":
                self.acquire_resource('output_osw')
            elif action == "ACQUIRE_SOP":
                self.acquire_resource('sop')

            # 资源释放命令
            elif action == "RELEASE_OSW_IN":
                self.release_resource('input_osw')
            elif action == "RELEASE_OSW_OUT":
                self.release_resource('output_osw')
            elif action == "RELEASE_SOP":
                self.release_resource('sop')

            # 资源状态查询命令
            elif action == "ACQUIRE_INPUT_OSW_STATUS":
                self.get_resource_status("input_osw")
            elif action == "ACQUIRE_OUTPUT_OSW_STATUS":
                self.get_resource_status("output_osw")
            elif action == "ACQUIRE_SOP_STATUS":
                self.get_resource_status("sop")

            # 光开关操作命令
            elif action == "SWITCH_IN":
                if len(parts) < 2:
                    self.send_response("错误: SWITCH_IN命令缺少通道参数")
                    return
                self.switch_in(parts[1])
            elif action == "SWITCH_OUT":
                if len(parts) < 2:
                    self.send_response("错误: SWITCH_OUT命令缺少通道参数")
                    return
                self.switch_out(parts[1])

            # SOP设备命令
            elif action.startswith("SOP_CMD"):
                self.handle_sop_command(cmd)

            else:
                self.send_response(f"错误: 未知命令 '{action}'，请检查命令格式")
                self.logger.warning(f"收到未知命令: {cmd}")

        except Exception as e:
            error_msg = f"处理命令 '{cmd}' 时发生错误: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            self.send_response(f"命令执行失败: {str(e)}")

    def get_resource_status(self, resource_name: str) -> Tuple[bool, str]:
        """获取指定资源的当前状态。

        Args:
            resource_name: 资源名称，必须是有效的资源类型。

        Returns:
            元组(是否可用, 状态描述)：
            - (True, 描述): 资源可用
            - (False, 描述): 资源被占用

        Raises:
            无异常，错误通过响应消息返回给客户端。
        """
        if resource_name not in ServerHandler.VALID_RESOURCES:
            error_msg = f"错误: 无效的资源类型 '{resource_name}'"
            self.send_response(error_msg)
            return False, error_msg

        with ServerHandler.lock:
            current_owner = ServerHandler.resource_lock.get(resource_name)
            if current_owner:
                status_msg = f"状态: {resource_name} 正被 {current_owner} 占用"
                self.send_response(status_msg)
                return False, status_msg
            else:
                status_msg = f"状态: {resource_name} 当前可用"
                self.send_response(status_msg)
                return True, status_msg

    def acquire_resource(self, resource_name: str) -> None:
        """申请指定的设备资源。

        Args:
            resource_name: 要申请的资源名称。

        功能：
        - 检查资源类型有效性
        - 防止重复申请同一资源
        - 线程安全的资源分配
        - 记录资源分配状态

        响应消息：
        - 成功: "成功申请资源{resource_name}"
        - 失败: 相应的错误或警告信息
        """
        if resource_name not in ServerHandler.VALID_RESOURCES:
            self.send_response(f"错误: 无效的资源类型 '{resource_name}'")
            return

        client_id = f"{self.client_address[0]}:{self.client_address[1]}"

        with ServerHandler.lock:
            current_owner = ServerHandler.resource_lock.get(resource_name)

            # 检查是否重复申请
            if current_owner == client_id:
                self.send_response(f"警告: 您已持有资源 '{resource_name}'，无需重复申请")
                return

            # 检查资源是否被其他客户端占用
            if current_owner is not None:
                self.send_response(f"申请失败: 资源 '{resource_name}' 正被 {current_owner} 占用")
                return

            # 分配资源给客户端
            ServerHandler.resource_lock[resource_name] = client_id
            ServerHandler.client_resources[client_id].add(resource_name)

            success_msg = f"成功申请资源 '{resource_name}'"
            self.send_response(success_msg)
            self.logger.info(f"客户端 {client_id} 成功申请资源: {resource_name}")

    def release_resource(self, resource_name: str) -> None:
        """释放指定的设备资源。

        Args:
            resource_name: 要释放的资源名称。

        功能：
        - 检查资源类型有效性
        - 验证客户端是否持有该资源
        - 线程安全的资源释放
        - 清理资源分配记录

        响应消息：
        - 成功: "成功释放资源{resource_name}"
        - 失败: 相应的错误信息
        """
        if resource_name not in ServerHandler.VALID_RESOURCES:
            self.send_response(f"错误: 无效的资源类型 '{resource_name}'")
            return

        client_id = f"{self.client_address[0]}:{self.client_address[1]}"

        with ServerHandler.lock:
            current_owner = ServerHandler.resource_lock.get(resource_name)

            if current_owner != client_id:
                if current_owner is None:
                    self.send_response(f"释放失败: 资源 '{resource_name}' 当前未被占用")
                else:
                    self.send_response(f"释放失败: 资源 '{resource_name}' 被其他客户端 {current_owner} 占用")
                return

            # 释放资源
            del ServerHandler.resource_lock[resource_name]
            ServerHandler.client_resources[client_id].discard(resource_name)

            success_msg = f"成功释放资源 '{resource_name}'"
            self.send_response(success_msg)
            self.logger.info(f"客户端 {client_id} 成功释放资源: {resource_name}")

    def switch_in(self, channel: str) -> None:
        """切换输入光开关到指定通道。

        Args:
            channel: 目标通道号（字符串格式）。

        前置条件：
        - 客户端必须已申请输入光开关资源

        响应消息：
        - 成功: "输入光开关已切换到通道{channel}"
        - 失败: 相应的错误信息
        """
        client_id = f"{self.client_address[0]}:{self.client_address[1]}"

        # 检查资源权限
        if ServerHandler.resource_lock.get('input_osw') != client_id:
            self.send_response("操作失败: 请先申请输入光开关资源")
            return

        try:
            channel_num = int(channel)
            if channel_num <= 0:
                self.send_response(f"参数错误: 通道号必须为正整数，当前值: {channel}")
                return

            self.input_osw.switch_channel(channel_num)
            success_msg = f"输入光开关已切换到通道 {channel_num}"
            self.send_response(success_msg)
            self.logger.info(f"客户端 {client_id} 成功切换输入光开关到通道 {channel_num}")

        except ValueError:
            self.send_response(f"参数错误: 通道号必须为数字，当前值: '{channel}'")
        except Exception as e:
            error_msg = f"切换输入光开关失败: {str(e)}"
            self.send_response(error_msg)
            self.logger.error(f"客户端 {client_id} 切换输入光开关到通道 {channel} 失败: {e}")

    def switch_out(self, channel: str) -> None:
        """切换输出光开关到指定通道。

        Args:
            channel: 目标通道号（字符串格式）。

        前置条件：
        - 客户端必须已申请输出光开关资源

        响应消息：
        - 成功: "输出光开关已切换到通道{channel}"
        - 失败: 相应的错误信息
        """
        client_id = f"{self.client_address[0]}:{self.client_address[1]}"

        # 检查资源权限
        if ServerHandler.resource_lock.get('output_osw') != client_id:
            self.send_response("操作失败: 请先申请输出光开关资源")
            return

        try:
            channel_num = int(channel)
            if channel_num <= 0:
                self.send_response(f"参数错误: 通道号必须为正整数，当前值: {channel}")
                return

            self.output_osw.switch_channel(channel_num)
            success_msg = f"输出光开关已切换到通道 {channel_num}"
            self.send_response(success_msg)
            self.logger.info(f"客户端 {client_id} 成功切换输出光开关到通道 {channel_num}")

        except ValueError:
            self.send_response(f"参数错误: 通道号必须为数字，当前值: '{channel}'")
        except Exception as e:
            error_msg = f"切换输出光开关失败: {str(e)}"
            self.send_response(error_msg)
            self.logger.error(f"客户端 {client_id} 切换输出光开关到通道 {channel} 失败: {e}")

    def send_response(self, message: str) -> None:
        """向客户端发送响应消息。

        Args:
            message: 要发送的响应消息。

        功能：
        - 自动添加换行符确保消息完整性
        - 处理socket发送异常
        - 记录发送失败的详细信息

        异常处理：
        - socket.error: 网络连接异常
        - AttributeError: socket对象无效
        - 其他异常: 记录详细错误信息
        """
        if not self.client_sock:
            self.logger.warning("尝试发送响应但客户端socket未连接")
            return

        try:
            # 确保消息以换行符结尾，便于客户端解析
            formatted_message = f"{message}\n"
            self.client_sock.sendall(formatted_message.encode('utf-8'))

        except (socket.error, ConnectionResetError, BrokenPipeError) as e:
            self.logger.warning(f"发送响应失败，客户端可能已断开: {e}")
        except AttributeError as e:
            self.logger.error(f"Socket对象无效: {e}")
        except Exception as e:
            self.logger.error(f"发送响应时发生未知错误: {e}", exc_info=True)

    def handle_sop_command(self, sop_cmd: str) -> None:
        """处理SOP设备相关命令。

        Args:
            sop_cmd: 完整的SOP命令字符串，格式为"SOP_CMD <子命令> [参数...]"。

        前置条件：
        - 客户端必须已申请SOP设备资源

        支持的子命令：
        - SET_REMOTE_CONTROL ON/OFF: 设置远程控制模式
        - SET_TRI_STATE ON/OFF: 设置三角扰乱模式
        - SET_TRI_RATE <速率>: 设置三角扰乱速率
        - SET_BAUDRATE <波特率>: 设置串口波特率
        - RESET: 重置SOP设备
        - GET_IDN: 获取设备标识
        - GET_VERSION: 获取设备版本信息
        """
        client_id = f"{self.client_address[0]}:{self.client_address[1]}"

        # 检查SOP资源权限
        if ServerHandler.resource_lock.get('sop') != client_id:
            self.send_response("操作失败: 请先申请SOP设备资源")
            return

        try:
            parts = sop_cmd.split()
            if len(parts) < 2:
                self.send_response("SOP命令格式错误: 缺少子命令")
                return

            _, sub_cmd, *args = parts

            # 远程控制模式设置
            if sub_cmd == "SET_REMOTE_CONTROL":
                self._handle_remote_control_command(args, client_id)

            # 三角扰乱模式设置
            elif sub_cmd == "SET_TRI_STATE":
                self._handle_tri_state_command(args, client_id)

            # 三角扰乱速率设置
            elif sub_cmd == "SET_TRI_RATE":
                self._handle_tri_rate_command(args, client_id)

            # 波特率设置
            elif sub_cmd == "SET_BAUDRATE":
                self._handle_baudrate_command(args, client_id)

            # 设备重置
            elif sub_cmd == "RESET":
                self._handle_reset_command(client_id)

            # 获取设备标识
            elif sub_cmd == "GET_IDN":
                self._handle_get_idn_command(client_id)

            # 获取设备版本
            elif sub_cmd == "GET_VERSION":
                self._handle_get_version_command(client_id)

            else:
                self.send_response(f"SOP命令无效: 未知子命令 '{sub_cmd}'")
                self.logger.warning(f"客户端 {client_id} 发送了未知的SOP子命令: {sub_cmd}")

        except Exception as e:
            error_msg = f"处理SOP命令失败: {str(e)}"
            self.send_response(error_msg)
            self.logger.error(f"客户端 {client_id} SOP命令处理异常: {e}", exc_info=True)

    def _handle_remote_control_command(self, args: list, client_id: str) -> None:
        """处理远程控制模式设置命令。"""
        if not args:
            self.send_response("参数错误: SET_REMOTE_CONTROL命令缺少状态参数")
            return

        state = args[0].upper()
        if state not in ["ON", "OFF"]:
            self.send_response(f"参数错误: 状态必须为ON或OFF，当前值: {args[0]}")
            return

        try:
            enable = (state == "ON")
            self.sop.set_remote_control_state(enable)
            status = "启动" if enable else "关闭"
            self.send_response(f"SOP远程控制模式已{status}")
            self.logger.info(f"客户端 {client_id} 成功设置SOP远程控制模式: {status}")
        except Exception as e:
            self.send_response(f"设置SOP远程控制模式失败: {str(e)}")
            self.logger.error(f"客户端 {client_id} 设置SOP远程控制模式失败: {e}")

    def _handle_tri_state_command(self, args: list, client_id: str) -> None:
        """处理三角扰乱模式设置命令。"""
        if not args:
            self.send_response("参数错误: SET_TRI_STATE命令缺少状态参数")
            return

        state = args[0].upper()
        if state not in ["ON", "OFF"]:
            self.send_response(f"参数错误: 状态必须为ON或OFF，当前值: {args[0]}")
            return

        try:
            enable = (state == "ON")
            self.sop.set_tri_state(enable)
            status = "启动" if enable else "关闭"
            self.send_response(f"SOP三角扰乱模式已{status}")
            self.logger.info(f"客户端 {client_id} 成功设置SOP三角扰乱模式: {status}")
        except Exception as e:
            self.send_response(f"设置SOP三角扰乱模式失败: {str(e)}")
            self.logger.error(f"客户端 {client_id} 设置SOP三角扰乱模式失败: {e}")

    def _handle_tri_rate_command(self, args: list, client_id: str) -> None:
        """处理三角扰乱速率设置命令。"""
        if not args:
            self.send_response("参数错误: SET_TRI_RATE命令缺少速率参数")
            return

        try:
            rate = int(args[0])
            if rate <= 0:
                self.send_response(f"参数错误: 速率必须为正整数，当前值: {args[0]}")
                return

            self.sop.set_tri_rate(rate)
            self.send_response(f"SOP三角扰乱速率已设置为 {rate}")
            self.logger.info(f"客户端 {client_id} 成功设置SOP三角扰乱速率: {rate}")
        except ValueError:
            self.send_response(f"参数错误: 速率必须为数字，当前值: {args[0]}")
        except Exception as e:
            self.send_response(f"设置SOP三角扰乱速率失败: {str(e)}")
            self.logger.error(f"客户端 {client_id} 设置SOP三角扰乱速率失败: {e}")

    def _handle_baudrate_command(self, args: list, client_id: str) -> None:
        """处理波特率设置命令。"""
        if not args:
            self.send_response("参数错误: SET_BAUDRATE命令缺少波特率参数")
            return

        try:
            baudrate = int(args[0])
            if baudrate <= 0:
                self.send_response(f"参数错误: 波特率必须为正整数，当前值: {args[0]}")
                return

            self.sop.set_baudrate(baudrate)
            self.send_response(f"SOP串口波特率已设置为 {baudrate}")
            self.logger.info(f"客户端 {client_id} 成功设置SOP波特率: {baudrate}")
        except ValueError:
            self.send_response(f"参数错误: 波特率必须为数字，当前值: {args[0]}")
        except Exception as e:
            self.send_response(f"设置SOP波特率失败: {str(e)}")
            self.logger.error(f"客户端 {client_id} 设置SOP波特率失败: {e}")

    def _handle_reset_command(self, client_id: str) -> None:
        """处理设备重置命令。"""
        try:
            self.sop.reset_device()
            self.send_response("SOP设备已重置")
            self.logger.info(f"客户端 {client_id} 成功重置SOP设备")
        except Exception as e:
            self.send_response(f"重置SOP设备失败: {str(e)}")
            self.logger.error(f"客户端 {client_id} 重置SOP设备失败: {e}")

    def _handle_get_idn_command(self, client_id: str) -> None:
        """处理获取设备标识命令。"""
        try:
            idn = self.sop.get_idn()
            self.send_response(f"SOP设备标识: {idn}")
            self.logger.info(f"客户端 {client_id} 成功获取SOP设备标识")
        except Exception as e:
            self.send_response(f"获取SOP设备标识失败: {str(e)}")
            self.logger.error(f"客户端 {client_id} 获取SOP设备标识失败: {e}")

    def _handle_get_version_command(self, client_id: str) -> None:
        """处理获取设备版本命令。"""
        try:
            version = self.sop.get_version()
            self.send_response(f"SOP设备版本: {version}")
            self.logger.info(f"客户端 {client_id} 成功获取SOP设备版本")
        except Exception as e:
            self.send_response(f"获取SOP设备版本失败: {str(e)}")
            self.logger.error(f"客户端 {client_id} 获取SOP设备版本失败: {e}")


class SOP_Server:
    """SOP服务器主类。

    负责管理TCP服务器的生命周期，包括：
    - 服务器socket的创建和配置
    - 客户端连接的接受和分发
    - 线程池管理和资源清理
    - 优雅的服务器关闭处理
    """

    def __init__(self) -> None:
        """初始化SOP服务器。

        从配置文件读取服务器参数，初始化线程池和SOP控制器。

        Raises:
            ValueError: 当配置参数无效时抛出异常。
            Exception: 当SOP控制器初始化失败时抛出异常。
        """
        try:
            self.max_threads = int(config.get_value("server", "max_threads", "50"))
            self.server_ip = config.get_value("server", "server_ip")
            self.server_port = int(config.get_value("server", "server_port"))

            # 验证配置参数
            if self.max_threads <= 0:
                raise ValueError(f"线程池大小必须为正数，当前值: {self.max_threads}")
            if not self.server_ip:
                raise ValueError("服务器IP地址不能为空")
            if not (1 <= self.server_port <= 65535):
                raise ValueError(f"服务器端口必须在1-65535范围内，当前值: {self.server_port}")

        except Exception as e:
            logger.error(f"服务器配置初始化失败: {e}")
            raise

        # 初始化线程池和控制器
        self.executor = ThreadPoolExecutor(max_workers=self.max_threads)
        self.sop_controller = sop_controller
        self.server_socket: Optional[socket.socket] = None
        self.is_running = False

        logger.info(f"SOP服务器初始化完成 - 地址: {self.server_ip}:{self.server_port}, 最大线程数: {self.max_threads}")

    def start(self) -> None:
        """启动SOP服务器。

        创建并配置服务器socket，开始监听客户端连接。
        为每个新连接创建独立的处理器实例并提交到线程池。

        Raises:
            OSError: 当socket操作失败时抛出异常。
            Exception: 当服务器启动失败时抛出异常。
        """
        try:
            # 创建并配置服务器socket
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.server_ip, self.server_port))
            self.server_socket.listen(5)

            self.is_running = True
            logger.info(f"SOP服务器已启动，监听地址: {self.server_ip}:{self.server_port}")

            # 主服务循环
            while self.is_running:
                try:
                    client_sock, client_addr = self.server_socket.accept()
                    logger.info(f"接受新客户端连接: {client_addr[0]}:{client_addr[1]}")

                    # 为每个客户端创建独立的处理器实例
                    handler = ServerHandler(self.sop_controller)
                    handler.set_connection(client_sock, client_addr)

                    # 提交到线程池处理
                    self.executor.submit(handler.handle_client)

                except socket.error as e:
                    if self.is_running:  # 只有在服务器运行时才记录错误
                        logger.error(f"接受客户端连接时出错: {e}")
                except Exception as e:
                    logger.error(f"处理客户端连接时发生未知错误: {e}", exc_info=True)

        except OSError as e:
            logger.error(f"服务器socket操作失败: {e}")
            raise
        except Exception as e:
            logger.error(f"服务器启动失败: {e}")
            raise
        finally:
            self.shutdown()

    def shutdown(self) -> None:
        """优雅关闭服务器。

        关闭服务器socket，等待所有客户端处理完成，清理资源。
        """
        logger.info("开始关闭SOP服务器...")
        self.is_running = False

        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
                logger.info("服务器socket已关闭")
            except Exception as e:
                logger.error(f"关闭服务器socket时出错: {e}")

        # 关闭线程池，等待所有任务完成
        try:
            self.executor.shutdown(wait=True, timeout=30)
            logger.info("线程池已关闭，所有客户端连接已处理完成")
        except Exception as e:
            logger.error(f"关闭线程池时出错: {e}")

        logger.info("SOP服务器已完全关闭")


if __name__ == "__main__":
    """主程序入口。

    创建并启动SOP服务器，处理键盘中断信号以实现优雅关闭。
    """
    server = None
    try:
        # 创建并启动服务器
        server = SOP_Server()
        logger.info("正在启动SOP服务器...")
        server.start()

    except KeyboardInterrupt:
        logger.info("收到键盘中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器运行时发生严重错误: {e}", exc_info=True)
    finally:
        # 确保服务器被正确关闭
        if server:
            server.shutdown()
        logger.info("程序已退出")
