# -*- coding: utf-8 -*-
# @Time : 2022/1/17 14:52
# <AUTHOR> 25646
# @File : Logger.py
# @Project: 400G CFP2 Dco

import sys
import os
import time


class Logger(object):
    def __init__(self, fname='Default.log', path='./'):
        self.terminal = sys.stdout
        if not os.path.exists(path):
            os.makedirs(path)
        self.log = os.path.join(path,fname)
        with open(self.log,'a', encoding='utf-8') as log:
            log.write('开始时间：' + time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time())) + '\n')

    def write(self, message):
        self.terminal.write(message)
        with open(self.log,'a', encoding='utf-8') as log:
            log.write(message)

    def flush(self):
        pass


# if __name__ =='__main__':
#     sys.stdout = Logger('123.log')
#     printf('456')
#     printf('4789')