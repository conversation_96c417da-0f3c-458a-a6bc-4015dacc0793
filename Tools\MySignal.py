from PySide6 import QtCore


class MySignal(QtCore.QObject):
    # 鞋带参数的信号
    sig_show_log_info = QtCore.Signal(str)
    sig_show_tb_login = QtCore.Signal(str)
    sig_show_ini = QtCore.Signal(str, dict)
    sig_status_bar_info = QtCore.Signal(str, int)

    sig_set_btn_text = QtCore.Signal(str, str, str)

    sig_show_read_info = QtCore.Signal(dict)

    sig_show_FW_info = QtCore.Signal(str)
    sig_show_SN_info = QtCore.Signal(str)
    sig_show_read_default_vender_info = QtCore.Signal(str)

    sig_status_bar_show = QtCore.Signal(int)

    sig_continuous_done = QtCore.Signal()

    operation_complete = QtCore.Signal(bool)

    # MES消息信号
    # 参数1：状态码
    # 参数2：提示信息
    mesInfoSignal = QtCore.Signal(str, str)

    def __init__(self):
        super(MySignal, self).__init__()


my_sig = MySignal()

pass

