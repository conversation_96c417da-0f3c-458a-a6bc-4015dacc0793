# -*- coding: utf-8 -*-

import time
import uuid

from PySide6 import QtCore


class MySignal(QtCore.QObject):
    # 运行页面重新加载信号
    reloadSignal = QtCore.Signal()
    # 保存结果文件信号
    saveLogSignal = QtCore.Signal()
    # 步骤执行进度信号
    # 参数1：step_index,-1表示当前步骤
    # 参数2：进度 0~100
    progressSignal = QtCore.Signal(int, int)

    # 步骤执行结果信号
    # 参数1：step_index,-1表示当前步骤
    # 参数2：结果 0:成功 1：执行失败： 2：不符合预期
    # 参数3: 提示信息
    resultSignal = QtCore.Signal(int, int, str)

    # 步骤执行提示信息信号
    # 参数1：step_index,-1表示当前步骤
    # 参数2：提示信息
    infoSignal = QtCore.Signal(int, str)

    # 步骤执行错误信号
    # 参数1：step_index,-1表示当前步骤
    # 参数2：错误信息
    errorSingal = QtCore.Signal(int, str)

    # 步骤开始执行计时信号
    # 参数1：step_index,-1表示当前步骤
    startTimeSignal = QtCore.Signal(int)

    # 步骤执行完成结束计时信号
    # 参数1：step_index,-1表示当前步骤
    endTimeSignal = QtCore.Signal(int)

    # 执行步骤切换信号
    # 参数1：step_index,-1表示当前步骤
    stepIndexSingal = QtCore.Signal(int)

    # 线程执行消息信号
    # 参数1：step_index,-1表示当前步骤
    # 参数2：提示信息
    threadSingal = QtCore.Signal(int, str)

    # MES消息信号
    # 参数1：状态码
    # 参数2：提示信息
    mesInfoSignal = QtCore.Signal(str, str)

    # messageBox信号
    # 参数1： uuid
    # 参数2：类型：question，info，error，input
    # 参数3： message
    messageBoxSignal = QtCore.Signal(str, str,str)

    #监视信号，采用非模态对话框监视某个变量的值
    # 参数1： 监视窗口标题
    # 参数2：监视的变量名列表
    # 参数3：监视值类型，str,int,float,line（曲线）...
    # 参数4：监视值刷新频率，单位秒
    # 参数5：监视显示字体大小
    monitorSignal = QtCore.Signal(str,dict,int,int)

    def __init__(self):
        super(MySignal, self).__init__()
        self.message_result = {}

    def get_uuid(self):
        return uuid.uuid4()

    def set_message_result(self,uuid,result):
        self.message_result[uuid] = result

    def wait_message_result(self,uuid, time_out=3600):
        time0 = time.time()
        while time.time() - time0 < time_out:
            if uuid in self.message_result.keys():
                return self.message_result.pop(uuid)
            time.sleep(1)

        return TimeoutError

#
my_sig = MySignal()
