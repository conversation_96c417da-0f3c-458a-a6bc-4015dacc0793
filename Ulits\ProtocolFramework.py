# -*- coding: utf-8 -*-
# @Time : 2024/06/29
# <AUTHOR> 42604
# @Use: 通信接口基础类
import socket
import time

import paramiko
import serial
from Ulits.mylog import logger


class TCPFramework(object):
    bytes_mode = True

    def __init__(self, ip: str, port, endflag=None, timeout=1):
        self.connected = False
        self.ip = ip
        self.port = int(port)
        self.connected = False
        self.timeout = timeout
        self.END_FLAG = endflag
        self.s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.s.settimeout(self.timeout)
        self.__connect__()

    def __connect__(self):
        logger.debug(f' 尝试连接 {self.ip}:{self.port}')
        try:
            self.s.connect((self.ip, self.port))
            self.__login__()
            logger.debug(f' 连接 {self.ip}:{self.port} 成功 .')
        except WindowsError:
            logger.error(f' 连接 {self.ip}:{self.port} 失败 .')
            self.connected = False
            return False

    def __login__(self):
        self.connected = True

    def send(self, cmd):
        try:
            if not self.bytes_mode:
                cmd = cmd.encode('utf-8') + b'\n'
            logger.debug(b'send:' + cmd)
            self.s.send(cmd)
        except socket.timeout:
            logger.error(f' {self.ip}:{self.port} send  超时.')
            return

    def recv(self):
        buffer = []
        t0 = time.time()
        while len(buffer) == 0:
            try:
                while True:
                    data = self.s.recv(1024)
                    if data:
                        buffer.append(data)
                        if self.END_FLAG and data[-1] == '\n' and data[:-2] == '\r':
                            break
                    else:
                        break
            except:
                pass
        reply = b''.join(buffer)
        if not self.bytes_mode:
            reply = reply.decode('utf-8')
        return reply


class COMFramework(object):
    bytes_mode = True
    wait_limit = 100

    def __init__(self, com: str, boudrate: int, timeout=10):
        self.com = com
        self.boudrate = boudrate
        self.timeout = timeout
        try:
            self.s = serial.Serial(com, int(boudrate), timeout=int(timeout))
            self.connected = False
            self.__connect__()
            logger.debug(f" 连接:{com}:{boudrate} ok.")
        except Exception:
            self.connected = False
            logger.error(f" 连接 {com}:{boudrate} failed.")

    def __connect__(self):
        if not self.s.is_open:
            try:
                self.s.open()
                self.connected = True
            except Exception as e:
                logger.error(f' connect to {self.com}:{self.boudrate} exception:{e}')
                self.connected = False
                return False

    def send(self, cmd):
        try:
            if not self.connected:
                self.__connect__()
            if not self.bytes_mode:
                cmd = cmd.encode('utf-8') + b'\r'
            self.s.write(cmd)
        except Exception as e:
            logger.error(f' send to {self.com}:{self.boudrate} exception:{e}')
            self.connected = False

    def recv(self):
        try:
            if not self.connected:
                self.__connect__()
            t = 0
            while True:
                t += 1
                time.sleep(0.1)
                response = self.s.read_all()
                if response != b'':
                    logger.debug(response)
                    break
                if t > 100:
                    logger.error(f'{self.com} 无法读取返回值')
                    return "recv error"
            if not self.bytes_mode:
                response = response.decode('utf-8')
            return response
        except Exception as e:
            logger.error(f' recv from {self.com} exception:{e}')
            self.connected = False


class UDPFramework(object):
    bytes_mode = True

    def __init__(self, ip, port=9000, time_out=10):
        self.ip = ip
        self.port = int(port)
        self.s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.s.settimeout(time_out)

    def send_recv(self, cmd):
        try:
            if not self.bytes_mode:
                cmd = cmd.encode('utf-8') + b'\n'
            self.s.sendto(cmd, (self.ip, self.port))
            reply = self.s.recv(1024)
            if not self.bytes_mode:
                reply = reply.decode('utf-8')
            return reply
        except Exception as err:
            logger.error(f'{self.ip}:{self.port}  exception: {err}')
            return b''


class SSHFramework(object):

    def __init__(self, ip, port=22, username='admin', pwd='Admin_123'):
        self.ip = ip
        self.port = port
        self.username = username
        self.pwd = pwd
        self.client = paramiko.SSHClient()
        self.active = False

    def login(self):
        reply = self.send_recv('\t')
        if 'Login' in reply:
            reply = self.send_recv('admin')
            if 'Password' in reply:
                reply = self.send_recv('Admin_123')
                if "Login" in reply:
                    raise Exception('网元登录失败')

    def __connect__(self):
        try:
            self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.client.connect(self.ip, self.port, self.username, self.pwd, timeout=2)
            self.shell = self.client.invoke_shell()
            self.active = True
        except:
            self.active = False
            print(f'{self.ip}-{self.port}连接失败！')

    def send_recv(self, cmd):
        buffer = ''
        while not self.active:
            self.__connect__()
        try:
            self.shell.send(cmd + '\n')
        except socket.error:
            self.__connect__()
            self.shell.send(cmd + '\n')
        while not self.shell.recv_ready():
            time.sleep(0.0001)
        while self.shell.recv_ready():
            buffer += self.shell.recv(1024).decode()
        # print(buffer)
        return buffer

    def close(self):
        self.client.close()


class TELNETFramework(object):

    def __init__(self, ip, port=23, username='admin', pwd='Admin_123'):
        self.ip = ip
        self.port = port
        self.username = username
        self.pwd = pwd
        self.client = telnetlib.Telnet(ip, port)
        self.active = False

    def __connect__(self):
        try:
            self.active = True
        except:
            self.active = False
            print(f'{self.ip}-{self.port}连接失败！')

    def login(self):
        self.client.write(self.username.encode('utf-8') + b'\n')
        reply = self.client.read_until(b'Password:',2)
        self.client.write(self.pwd.encode('utf-8') + b'\n')
        reply = self.client.read_until(b'>', 2)
        if reply == '':
            print(f'TELNET 连接 {self.ip} 失败')

    def send_recv(self, cmd: str):
        while not self.active:
            self.__connect__()
        # _ = self.client.read_very_eager().decode('utf-8')
        self.client.write(cmd.encode('utf-8') + b'\n')
        # reply = self.client.read_until(cmd.encode('utf-8'))
        reply = self.client.read_some().decode('utf-8')
        while True:
            time.sleep(0.0001)
            buffer = self.client.read_very_eager().decode('utf-8')
            if buffer == '':
                break
            reply += buffer
        # print(reply, end='')
        return reply

    def close(self):
        self.client.close()