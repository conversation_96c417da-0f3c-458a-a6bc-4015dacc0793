#!/usr/bin/python
# -*- coding: UTF-8 -*-
import random
from ..utils.bytes_helper import By<PERSON><PERSON><PERSON><PERSON>


def compose_config_packet(board_sn: int, moudle_bytes: bytes) -> bytes:
    helper = BytesHelper()

    helper.add_int_d(0xEFEF00, 3)
    helper.add_int_d(1 + 1 + 2 + 2 + 2 + 2 + 2 + 1 + 1 + 2 + 6 + 2 + len(moudle_bytes) + 1, 2)  # len
    helper.add_int_d(0x17, 1)
    helper.add_int_d(0, 1)
    helper.add_int_d(1, 2)
    helper.add_int_d(1, 2)
    helper.add_int_d(0, 2)  # res
    helper.add_int_d(0xFF02, 2)  # comm
    helper.add_int_d(random.randint(1, 65535), 2)  # tcomm
    helper.add_int_d(helper.check_sum(0, helper.get_position()) & 0xFF, 1)
    # ----data部分开始
    helper.add_int_d(1, 1)  # slot_num
    helper.add_int_d(len(moudle_bytes) + 8, 2)
    helper.add_int_d(board_sn, 6)
    helper.add_int_d(0, 2)  # res
    helper.add_bytes(moudle_bytes, len(moudle_bytes))
    # ----data部分结束
    helper.add_int_d(helper.check_sum(0, helper.get_position()) & 0xFF, 1)

    return helper.get_packet()

def compose_mac_setting_packet(board_sn: int, adder, set_mask) -> bytes:
    helper = BytesHelper()
    helper.add_int_d(0xEFEF00, 3)
    helper.add_int_d(1 + 1 + 2 + 2 + 2 + 2 + 2 + 1 + 6 + 1 + 6 + 1, 2)  # len
    helper.add_int_d(0x17, 1)
    helper.add_int_d(0, 1)
    helper.add_int_d(1, 2)
    helper.add_int_d(1, 2)
    helper.add_int_d(0, 2)  # res
    helper.add_int_d(0xFF04, 2)  # comm
    helper.add_int_d(random.randint(1, 65535), 2)  # tcomm
    helper.add_int_d(helper.check_sum(0, helper.get_position()) & 0xFF, 1)
    # ----data部分开始
    helper.add_int_d(board_sn, 6)
    helper.add_bytes(set_mask, 1)
    helper.add_bytes(adder, 6)
    # ----data部分结束
    helper.add_int_d(helper.check_sum(0, helper.get_position()) & 0xFF, 1)
    return helper.get_packet()

def compose_ip_setting_packet(board_sn: int, addr, mask, gate, dns1, dns2,set_mask) -> bytes:
    helper = BytesHelper()
    helper.add_int_d(0xEFEF00, 3)
    helper.add_int_d(1 + 1 + 2 + 2 + 2 + 2 + 2 + 1 + 6 + 21 + 1, 2)  # len
    helper.add_int_d(0x17, 1)
    helper.add_int_d(0, 1)
    helper.add_int_d(1, 2)
    helper.add_int_d(1, 2)
    helper.add_int_d(0, 2)  # res
    helper.add_int_d(0xFF03, 2)  # comm
    helper.add_int_d(random.randint(1, 65535), 2)  # tcomm
    helper.add_int_d(helper.check_sum(0, helper.get_position()) & 0xFF, 1)
    # ----data部分开始
    helper.add_int_d(board_sn, 6)
    helper.add_bytes(set_mask, 1)
    helper.add_bytes(addr, 4)
    helper.add_bytes(mask, 4)
    helper.add_bytes(gate, 4)
    helper.add_bytes(dns1, 4)
    helper.add_bytes(dns2, 4)
    # ----data部分结束
    helper.add_int_d(helper.check_sum(0, helper.get_position()) & 0xFF, 1)
    return helper.get_packet()

# 机框SN（6）Type(1)	Res(6)
# Type(1): 0x00:实时信息轮询，0x01:基本信息轮询；0x02:制造信息轮询
def compose_poll_packet(shelf_sn: int, poll_type: int) -> bytes:
    helper = BytesHelper()

    helper.add_int_d(0xEFEF00, 3)
    helper.add_int_d(1 + 1 + 2 + 2 + 2 + 2 + 2 + 1 + 6 + 1 + 6 + 1, 2)  # len
    helper.add_int_d(0x17, 1)
    helper.add_int_d(0, 1)
    helper.add_int_d(1, 2)
    helper.add_int_d(1, 2)
    helper.add_int_d(0, 2)  # res
    helper.add_int_d(0xFF00, 2)  # comm
    helper.add_int_d(random.randint(1, 65535), 2)  # tcomm
    helper.add_int_d(helper.check_sum(0, helper.get_position()) & 0xFF, 1)
    # ----data部分开始
    helper.add_int_d(shelf_sn, 6)
    helper.add_int_d(poll_type, 1)
    helper.add_int_d(0, 6)  # res
    # ----data部分结束
    helper.add_int_d(helper.check_sum(0, helper.get_position()) & 0xFF, 1)

    return helper.get_packet()
