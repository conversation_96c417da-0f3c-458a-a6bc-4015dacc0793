#!/usr/bin/python
# -*- coding: UTF-8 -*-
from tokenize import String
import struct


class BytesHelper:
    rebytes = None
    position = None

    def __init__(self):
        self.rebytes = bytearray()
        self.position = 0

    # 大端
    def add_int_d(self, data: int, length: int):
        data_bytes = data.to_bytes(length, 'big')
        self.add_bytes(data_bytes, length)

    # 小端
    def add_int_l(self, data: int, length: int):
        data_bytes = data.to_bytes(length, 'little')
        self.add_bytes(data_bytes, length)

    # 小端
    def add_float(self, data: float, length: int):
        # data_bytes = data.encode(encoding="utf-8")
        data_bytes = struct.pack('<f', data)
        self.add_bytes(data_bytes, length)

    # 小端
    def add_string(self, data: String, length: int):
        data_bytes = data.encode(encoding="utf-8")
        self.add_bytes(data_bytes, length)

    def add_bytes(self, data: bytes, length: int):
        num = len(data) if len(data) < length else length
        data_num = data[:num]
        for i in range(num):
            self.rebytes.append(data_num[i])
        if length > num:
            for i in range(length - num):
                self.rebytes.append(0x00)
        self.position += length

    def reverse2byte(self, b: bytes):
        return b[1:] + b[:1]

    def reverse(self):
        if len(self.rebytes) % 2 != 0:
            raise ValueError
        rebytes = self.get_packet()
        self.rebytes = bytearray()
        for i in range(len(rebytes) >> 1):
            self.add_int_l(rebytes[i*2 + 1], 1)
            self.add_int_l(rebytes[i*2], 1)

    def add_bytes_l(self, data: bytes, length: int):
        # 两两字节反过来，如b'\x00\xfc\xf0\x02'变成b'\xfc\x00\x02\xf0'
        num = len(data) if len(data) < length else length
        data_num = data[:num]
        for i in range(int(num / 2)):
            self.rebytes += self.reverse2byte(data_num[i * 2:i * 2 + 2])
        if length > num:  # 长度不足的补0
            for i in range(length - num):
                self.rebytes.append(0x00)
        self.position += length

    # 指针位置
    def get_position(self) -> int:
        return self.position

    # 返回包
    def get_packet(self) -> bytes:
        return self.rebytes[:self.position]

    # 校验和
    def check_sum(self, start: int, end: int) -> int:
        isum = 0
        for i in range(start, end):
            isum += self.rebytes[i] & 0xFF
        return isum


if __name__ == "__main__":
    # pass
    helper = BytesHelper()
    helper.add_int_d(0, 1)
    helper.add_int_d(1, 1)
    helper.add_bytes_l(b'\x00\xfc\xf0\x02\x00\x00\x00\x00', 8)  # 小端？
    helper.add_int_d(4, 1)
    helper.add_int_l(256, 2)  # 小端？
    # helper.add_int_l(pData, 256)  # 小端？
    sendbytes = helper.get_packet()
    print(f"{sendbytes}")