#!/usr/bin/python
# -*- coding: UTF-8 -*-
import configparser
import logging
import random
import os
import time


mode_state_dic = {0x100: "High-Power-Down", 0x0080: "TX Turn-off",
                  0x0040: "Fault", 0x0020: "Ready", 0x0010: "TX Turn-on",
                  0x0008: "TX-Off", 0x0004: "High-Power-Up", 0x0002: "Low-Power",
                  0x0001: "Initialize state"}

def init(log_dir, log_level='INFO'):
    return init_logger(log_dir, log_level), init_data()


# 初始化日志对象
def init_logger(log_dir, log_level='INFO'):
    dco_logger = logging.getLogger("dco")
    dco_logger.setLevel(log_level)
    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(logging.Formatter('%(asctime)s %(levelname)s: %(message)s'))
    # stream_handler.setFormatter(logging.Formatter('%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s'))
    dco_logger.addHandler(stream_handler)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    start_time = time.strftime("%Y%m%d%H%M%S", time.localtime())[2:]
    file_handler = logging.FileHandler(os.path.join(log_dir, f'dco_{start_time}.log'))
    file_handler.setFormatter(logging.Formatter('%(asctime)s %(levelname)s: %(message)s'))
    # file_handler.setFormatter(logging.Formatter('%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s'))
    dco_logger.addHandler(file_handler)
    return dco_logger


# 初始化日志配置数据对象
def init_data():
    dco_config = configparser.ConfigParser()
    dco_config.read('./data.ini', encoding="utf-8-sig")
    return dco_config


# def cfg_one_para(section, option, value):
#     # 设置一项配置参数
#     cfg_path = './data.ini'
#     dco_config = configparser.ConfigParser()
#     dco_config.read(cfg_path, encoding="utf-8-sig")
#     dco_config.set(section, option, value)
#     with open(cfg_path, "w", encoding="utf-8-sig") as f:
#         dco_config.write(f)
#     return dco_config

# 修改配置文件中的源端网卡描述、MAC
def cfg_src_eth(eth, mac):
    cfg_path = './data.ini'
    dco_config = configparser.ConfigParser()
    dco_config.read(cfg_path, encoding="utf-8-sig")
    dco_config.set("winpcap", "src_eth", f'*{eth}*')
    # dco_config.set("winpcap", "src_eth", f'{eth}')  # 存网卡名的话不要*号
    dco_config.set("winpcap", "src_mac", f'0x{mac}')
    with open(cfg_path, "w", encoding="utf-8-sig") as f:
        dco_config.write(f)
    return dco_config

# # 修改配置文件中的sv电压
# def cfg_sv(val):
#     cfg_path = './data.ini'
#     dco_config = configparser.ConfigParser()
#     dco_config.read(cfg_path, encoding="utf-8-sig")
#     dco_config.set("property", "sv", f'{val}')
#     with open(cfg_path, "w", encoding="utf-8-sig") as f:
#         dco_config.write(f)
#     return dco_config


# # 修改配置文件中的lorfpd
# def cfg_lorfpd(val):
#     cfg_path = './data.ini'
#     dco_config = configparser.ConfigParser()
#     dco_config.read(cfg_path, encoding="utf-8-sig")
#     dco_config.set("property", "lorfpd", f'{val}')
#     with open(cfg_path, "w", encoding="utf-8-sig") as f:
#         dco_config.write(f)
#     return dco_config

def cfg_set_option(section, option, val):
    cfg_path = './data.ini'
    dco_config = configparser.ConfigParser()
    dco_config.read(cfg_path, encoding="utf-8-sig")
    dco_config.set(section, option, f'{val}')
    with open(cfg_path, "w", encoding="utf-8-sig") as f:
        dco_config.write(f)
    return dco_config

# 修改配置文件中的目的网卡MAC、轮询类型
def cfg_desmac_polltype(mac, poll_type):
    cfg_path = './data.ini'
    dco_config = configparser.ConfigParser()
    dco_config.read(cfg_path, encoding="utf-8-sig")
    dco_config.set("winpcap", "dest_mac", f'0x{mac}')
    dco_config.set("board", "poll_type", hex(poll_type))
    with open(cfg_path, "w", encoding="utf-8-sig") as f:
        dco_config.write(f)
    return dco_config


# 修改配置文件中的单盘sn
def cfg_boardsn(sn):
    cfg_path = './data.ini'
    dco_config = configparser.ConfigParser()
    dco_config.read(cfg_path, encoding="utf-8-sig")
    dco_config.set("board", "board_sn", f'0x{sn}')
    with open(cfg_path, "w", encoding="utf-8-sig") as f:
        dco_config.write(f)
    return dco_config

# 修改配置文件中的模块编号
# def cfg_module_seq(seq_num):
#     cfg_path = './data.ini'
#     dco_config = configparser.ConfigParser()
#     dco_config.read(cfg_path, encoding="utf-8-sig")
#     dco_config.set("module", "seq_num", f'{seq_num}')
#     with open(cfg_path, "w", encoding="utf-8-sig") as f:
#         dco_config.write(f)
#     return dco_config

# 字节数组转16进制字符串
def bytes2str(packet: bytes) -> str:
    result = ""
    for b in packet:
        result += "%02x" % b
    return result.upper()

# int list转16进制字符串(带空格)
def ints2hexstr(vals: list) -> str:
    result = ""
    for val in vals:
        result += hex(val) + " "
    return result

# 字节数组转16进制字符串
def bytes2int(packet: bytes) -> int:
    result = ""
    for b in packet:
        result += "%02x" % b
    return int(result, 16)

# 字节数组转16进制字符串(带空格)
def bytes2str2(packet: bytes) -> str:
    result = ""
    for b in packet:
        result += "%02x" % b + " "
    return result.upper()


# 生成随机命令追踪字tcomm
def gen_tcomm():
    return random.randint(1, 65535)


def get_str_args(args):
    strargs = ''
    for a in args:
        if isinstance(a, int):
            strargs += hex(a)  # int都用十六进制打印
        elif isinstance(a, bytes):
            strargs += '0x' + bytes2str(a)
        else:
            strargs += f'{a}'
        strargs += ','
    return '(' + strargs + ')'


def get_str_args_list(args):
    strargs = ''
    for a in args:
        if isinstance(a, int):
            strargs += hex(a)  # int都用十六进制打印
        elif isinstance(a, bytes):
            strargs += '0x' + bytes2str(a)
        elif isinstance(a, str):
            strargs += f'\'{a}\''
        else:
            strargs += f'{a}'
        strargs += ','
    return '[' + strargs[:-1] + ']'


def setbyteslittle(b: bytes):
    leng = len(b)
    if leng == 1:
        return b
    res = b''
    for i in range(int(leng / 2)):
        r = reverse2byte(b[i * 2:i * 2 + 2])
        res += r
    return res


def reverse2byte(b: bytes):
    return b[1:] + b[:1]


if __name__ == "__main__":
    tcomm = gen_tcomm()
    print(tcomm)
