#!/usr/bin/python
# -*- coding: UTF-8 -*-
# from Tia_reg_cal import handleDiagSetGetCmd_tia, PreDrv_cal, CurrSteBias_cal
from winpcapy import WinPcapDevices

from ..utils.module_function import *
import threading
import time
# from winpcapy import WinPcapUtils
# from dco_api import dco_utils, board_comm, module_comm  #, device
import re
import wmi
# from winpcapy import WinPcapDevices
# from PySide2 import QtCore
from ..utils.bytes_helper import BytesHelper
import xlrd
import math
import numpy
# import device
# from ..utils.device import *
from PySide6.QtWidgets import QMessageBox
from scipy.special import erfcinv
from scipy import optimize
import pickle
import os
import sys
from ..utils.MySignal import my_sig
import copy
import csv

olp85 = None
# voa = None
voa_rx = None
voa_ase = None
osw_sop = None
osw_ase = None
osw_tr = None
osw_oau = None
chamber = None

MSG_BOX_TITLE = "200G PIC Test"
# PASS = 'PASS'
# FAIL = 'FAIL'
PASS = '通过'
FAIL = '不通过'
rem_dic = {}
this = sys.modules[__name__]
tx_pwr_offset = 0
rx_pwr_offset = 0
seq_num = 0

Temp = {'cold': [float('-inf'), 30],  # 低温范围  # 改到ini里配置
        'room': [45, 70],  # 常温范围
        'hot': [80, float('inf')]  # 高温范围
        }
chamber_Temp = {'cold': -5, 'room': 25, 'hot': 50}  # 温循箱目标温度——改到ini里配置
temp_stable_th = 0.5
temp_wait_time = 10
Rx_Pow_HighGain_Range = [-26, -9]  # dBm,高增益 输入光功率范围
Rx_Pow_LowGain_Range = [-9, 0]  # dBm,低增益 输入光功率范围
RX_POW_TH_HILO = Rx_Pow_HighGain_Range[1]  # ？？？ 高低增益阈值
RX_TAP_MONITOR_RANGE = (-25, 3)
RX_TAP_MONITOR_STEP = 2
RX_TAP_MONITOR_LIST_HIGH = [x for x in range(RX_TAP_MONITOR_RANGE[0], RX_POW_TH_HILO + 1, RX_TAP_MONITOR_STEP)]
RX_TAP_MONITOR_LIST_LOW = [x for x in range(RX_POW_TH_HILO, RX_TAP_MONITOR_RANGE[-1] + 1, RX_TAP_MONITOR_STEP)]

FREQs = [191.3, 192.5, 193.7, 194.9, 196.1]  # 从191.3到196.1选5个频率

GC_MON_AVE_ADC_MIN = 1200
GC_MON_AVE_ADC_MAX = 65535
RX_CHANNEL_MONITOR = [5.0, 4.5, 4.0, 3.5, 3.0, 2.5, 2.0, 1.5, 1.0, 0.5, 0.0, -1, -2, -4, -6, -8, -10, -12, -14,
                      -16, -18, -20, -21, -22, -23, -24, -25, -26, -26.5, -27, -27.5, -28, -28.5, -29, -29.5,
                      -30, -30.5, -31, -31.5, -32]

logdir = ''
logdir_pa = os.path.join(os.path.dirname(sys.argv[0]), 'log')
logdir_debug = os.path.join(logdir_pa, 'debug')
msgbox_closed = False
msgbox_yes = False
STATE_INIT = 0
STATE_START = 1  # 测试中
STATE_PASS = 2  # 合格
STATE_FAIL = 3  # 不合格
STATE_STOP = 4  # 停止
STATE_IDLE = 5  # 在位
result_dic = {STATE_PASS: "成功", STATE_FAIL: "失败", STATE_STOP: "停止"}
BURNIN = 'burnin'
PRE_CAL = 'pre_calibration'
CAL = 'calibration'
# INVALID_PWR = -999

EDFAless = False
HDR_FLAG = True


def set_edfaless(is_less):
    global EDFAless
    EDFAless = is_less


def set_hdr_flag(is_use_hdr):
    global HDR_FLAG
    HDR_FLAG = is_use_hdr


def set_temp_info(box_temp_cold, box_temp_room, box_temp_hot, pic_temp_cold_max, pic_temp_room_min,
                      pic_temp_room_max, pic_temp_hot_min):
    global Temp
    global chamber_Temp
    chamber_Temp['cold'] = box_temp_cold
    chamber_Temp['room'] = box_temp_room
    chamber_Temp['hot'] = box_temp_hot
    Temp['cold'][1] = pic_temp_cold_max
    Temp['room'][0] = pic_temp_room_min
    Temp['room'][1] = pic_temp_room_max
    Temp['hot'][0] = pic_temp_hot_min


def on_msgbox(info):
    global msgbox_closed
    msg_box = QMessageBox()
    msgbox_closed = False
    msg_box.information(None, MSG_BOX_TITLE, info)
    msgbox_closed = True


def on_msgbox_ques(info):
    global msgbox_closed
    global msgbox_yes
    msg_box = QMessageBox()
    msgbox_closed = False
    reply = msg_box.question(None, MSG_BOX_TITLE, info)
    msgbox_yes = True if reply == QMessageBox.Yes else False
    msgbox_closed = True


my_sig.sig_msgbox.connect(on_msgbox)
my_sig.sig_msgbox_ques.connect(on_msgbox_ques)


def msgbox(info):
    # 弹出消息对话框，并阻塞等对话框关闭后才往下走
    global msgbox_closed
    my_sig.infoSignal.emit(-1,f"msgbox: {info}")
    # msg_box = QMessageBox()
    # msg_box.information(None, "400G PIC Test", info)  # 线程里直接用QMessageBox会卡死，改成用信号
    my_sig.sig_msgbox.emit(info)  #
    while not msgbox_closed:  # MessageBox需要阻塞程序，等槽函数执行完
        time.sleep(0.1)
    msgbox_closed = False
    return 0, ''


def msgbox_ques(info):
    # 弹出question对话框，并阻塞等对话框关闭后才往下走
    global msgbox_yes
    global msgbox_closed
    my_sig.infoSignal.emit(-1,f"msgbox question: {info}")
    my_sig.sig_msgbox_ques.emit(info)
    while not msgbox_closed:
        time.sleep(0.1)
    msgbox_closed = False
    return msgbox_yes


class stopException(Exception):
    def __init__(self, msg):
        self.message = msg

    def __str__(self):
        return self.message


class judgeException(Exception):
    def __init__(self, msg):
        self.message = msg

    def __str__(self):
        return self.message


def set_dco_seq(dco_seq):
    global seq_num
    seq_num = dco_seq


# 获取网卡GUID及其MAC
def get_netcard_wmi():
    # try:
    netcard_dic = {}
    w = wmi.WMI()
    for nic in w.Win32＿NetworkAdapter():
        # print(nic)
        if nic.PhysicalAdapter and nic.MACAddress:
            try:
                my_sig.infoSignal.emit(-1,f'MAC: {nic.MACAddress}, GUID: {nic.GUID}')
                mac_addr = nic.MACAddress
                mac_addr = mac_addr.replace(':', '')
                guid = nic.GUID[1:-1]
                netcard_dic[guid] = mac_addr
            except Exception as e:
                my_sig.sig_exception.emit(f"exception: {str(e)}")
    return netcard_dic
    # except Exception as err:
    #     self.write_log("exception: [%s], [%s]" % (str(err), traceback.format_exc()))


# 获取网卡名称及其描述——winpcapy获取到的描述和wmi获取到的不一样。。
def get_netcard():
    # try:
    netcard_lst = []
    nic_dic_wmi = get_netcard_wmi()
    # logger.info(f'nic_dic_wmi: {nic_dic_wmi}')
    devices = WinPcapDevices.list_devices()
    for dev_name in devices:
        # logger.info(f'dev_name: {dev_name}')
        my_sig.infoSignal.emit(-1,f'dev_name: {dev_name}')
        sobj = re.search('{(\\S+)}', dev_name)
        if sobj:
            guid = sobj.group(1)
            # logger.info(f'guid: {guid}')
            my_sig.infoSignal.emit(-1,f'get guid from dev_name: {guid}')
            if guid in nic_dic_wmi:
                netcard_lst.append((dev_name, devices[dev_name], nic_dic_wmi[guid]))
    return netcard_lst


def read_lineside_ber(seq_num):
    rc, ber = 1, '读pre_BER失败'
    for _ in range(10):  # 读到ber是nan时重试
        rc, data = read_reg_val(seq_num, 0x90C4, 2)
        ber = '读pre_BER失败'
        if rc == 0:  # 0表示成功
            ber = hex_to_float(bytes2str(data))
            my_sig.infoSignal.emit(-1,f'pre_BER: {ber}')
            if ber != 'nan':  # 不能这样判断nan?
                return rc, ber
        time.sleep(1)
    return rc, ber


def get_mode():
    rc, mode_msb = read_one_reg_retry(seq_num, 0x9001)
    if rc != 0:
        return 1, "读寄存器0x9001失败", ""
    rc, mode_lsb = read_one_reg_retry(seq_num, 0x9002)
    if rc != 0:
        return 1, "读寄存器0x9002失败", ""
    return 0, mode_msb, mode_lsb


def set_mode(mode_msb, mode_lsb):
    mode_msb = str2val(mode_msb)
    mode_lsb = str2val(mode_lsb)
    rc, mode_m_rd, mode_l_rd = get_mode()
    if mode_m_rd == mode_msb and mode_l_rd == mode_lsb:
        print('模块模式已经正确！')
    else:
        execute_set_lowpwr_enable_cmd(seq_num, True)
        time.sleep(2)
        while mode_m_rd != mode_msb or mode_l_rd != mode_lsb:
            if get_exit_flag():
                raise stopException(f'测试手动停止')
            write_one_reg(seq_num, 0x9001, mode_msb)
            write_one_reg(seq_num, 0x9002, mode_lsb)
            time.sleep(0.5)
            rc, mode_m_rd, mode_l_rd = get_mode()
        execute_set_lowpwr_enable_cmd(seq_num, False)

    # print(self.get_cfp2_regs(0x9001, 2))
    while True:
        if get_exit_flag():
            raise stopException(f'测试手动停止')
        rc, res_value = read_one_reg_ex(seq_num, 0xb016)
        if rc == 0:
            if res_value in mode_state_dic:
                my_sig.infoSignal.emit(-1,"当前模块状态: " + mode_state_dic[res_value])
            if res_value == 0x0040:
                return 1, res_value
            elif res_value == 0x0020:
                return 0, res_value
        time.sleep(5)


def get_pre_BERX(seq_num):
    rc, data = read_reg_val(seq_num, 0x9102, 2)
    ber = '读pre_BERX失败'
    if rc == 0:  # 0表示成功
        ber = hex_to_float(bytes2str(data))
        my_sig.infoSignal.emit(-1,f'pre_BERX: {ber}')
    return rc, ber


def get_pre_BERY(seq_num):
    rc, data = read_reg_val(seq_num, 0x910A, 2)
    ber = '读pre_BERY失败'
    if rc == 0:  # 0表示成功
        ber = hex_to_float(bytes2str(data))
        my_sig.infoSignal.emit(-1,f'pre_BERY: {ber}')
    return rc, ber


def read_ucb(seq_num):
    rc, data = read_reg_val(seq_num, 0x90CA, 2)
    if rc == 0:  # 0表示成功
        return 0, int(bytes2str(data), 16)
    return rc, None


# def get_ucb():
#     rc, data = read_reg_val(seq_num, 0x90CA, 2)
#     if rc == 0:  # 0表示成功
#         return 0, int(bytes2str(data), 16)
#     return 1, '查询不可纠正数据块个数失败'


def hex_to_signed_int(h: str):
    val = int(h, 16)
    if val < 0x8000:
        return val
    else:
        return val - 0x10000


def hex_to_signed_int_001(h: str):
    return round(hex_to_signed_int(h) * 0.01, 2)


def hex_to_signed_int_div256(h: str):
    return round(hex_to_signed_int(h) / 256, 2)


def hex_to_unsigned_int(h: str):
    val = int(h, 16)
    return val


def get_mod_ext_temp1(seq_num):
    rc, data = read_reg_val(seq_num, 0x9020, 1)
    if rc == 0:
        return 0, hex_to_signed_int(bytes2str(data)) / 256.0
    return rc, None


def RFPD_LOSS_OBS(rfpd, itla_power):
    # 预校准第4步得到该值，第8步依赖该值，存在文件中，方便第8步使用
    # RFPD_LOSS_OBS = [10*lg(RFPD)] -  ITLA_POWER
    rfpd = float(rfpd)
    itla_power = float(itla_power)
    rfpd_loss_obs = 10 * math.log10(rfpd) - itla_power
    config = init_data()
    config = cfg_set_option("property", "rfpd_loss_obs", rfpd_loss_obs)
    return 0, rfpd_loss_obs


# def rfpd_d_fa(rfpd_loss_fa, rfpd_loss_obs):
#     return rfpd_loss_fa - rfpd_loss_obs


# def itla_splice_loss(rfpd_d_fa):
#     return -rfpd_d_fa

def ratio(a, b):  # TXPD_RATIO':
    # 计算a/b
    a = float(a)
    b = float(b)
    return 0, a / b


# rc, func_res = rem_dic['ADC_TXPDX'] / rem_dic['ADC_TXPDY']


def subtract(a, b):
    # 计算a-b
    a = float(a)
    b = float(b)
    return 0, a - b


def average(*params):
    cnt = len(params)
    sum = 0
    for ele in params:
        sum += float(ele)
    return 0, sum / cnt


def negate(a):
    a = float(a)
    return 0, -a


def eset_tx_voa_max_volt(seq_num, val):
    return execute_mcm_cmd(seq_num, 'eset', 'VOA_TX_MAX_VOLTS', [0x0, str2mcmval(str(val - 0.1))])


def find_max_pwr_pump_volt(max_voa_volt):  # seq_num, max_voa_volt):
    # 找到最大输出光功率及对应的pump电压值
    global seq_num
    seq_num = int(seq_num)
    max_voa_volt = float(max_voa_volt)
    start_voa_volt = max_voa_volt - 2
    stop_voa_volt = max_voa_volt + 1
    max_pwr = float("-inf")
    max_pwr_volt = start_voa_volt
    my_sig.infoSignal.emit(-1,f'find_max_pwr_pump_volt, volt: {start_voa_volt}~{stop_voa_volt}')
    step = 0.1
    for ele in numpy.arange(start_voa_volt, stop_voa_volt + step, step):
        v = round(ele, 2)
        # 调整电压
        rc, info = execute_mcm_cmd(seq_num, 'set', 'VOA_TX_VOLTAGE', [0x0, float2hex(v)])  # str2mcmval(str(v))
        # rc, info = execute_mcm_cmd(seq_num, 'eset', 'VOA_TX_MAX_VOLTS', [0x0, str2mcmval(str(v))])  # [0x0, str2mcmval(str(v - 0.1))])
        if rc != 0 or 'PASS' not in info:
            return 1, 'execute_mcm_cmd set VOA_TX_VOLTAGE fail'
        pwr = get_opm_pwr()
        my_sig.infoSignal.emit(-1,f'pwr: {pwr}')
        pwr += tx_pwr_offset
        my_sig.infoSignal.emit(-1,f'pwr+att: {pwr}')
        if pwr > max_pwr:
            max_pwr = pwr
            max_pwr_volt = v
        else:  # 功率减小就停止遍历，设回上一次的volt
            rc, info = execute_mcm_cmd(seq_num, 'set', 'VOA_TX_VOLTAGE',
                                       [0x0, float2hex(v - step)])  # str2mcmval(str(v))
            if rc != 0 or 'PASS' not in info:
                return 1, 'execute_mcm_cmd set VOA_TX_VOLTAGE fail'
            break
    my_sig.infoSignal.emit(-1,f'max power: {max_pwr}, volt: {max_pwr_volt}')
    # rem_dic['POUT_MAX_VOA'] = max_pwr  # 最大光功率
    # rem_dic['TX_VOA_MAX_VOLT'] = max_pwr_volt  # 最大光功率时的电压
    rc, info = eset_tx_voa_max_volt(seq_num, max_pwr_volt)
    if rc != 0:
        return 1, 'execute_mcm_cmd eset VOA_TX_MAX_VOLTS fail'
    # if max_pwr < 5:
    #     return 1, 'max power < 5'
    return 0, (max_pwr, max_pwr_volt)


def get_rx90_coefficients():  # (seq_num, sheet_re, r, ccnt):
    global seq_num
    start_time = int(time.time())
    rx_state = -1
    info = ''
    while int(time.time()) - start_time < 65:
        rc, info = execute_mcm_cmd(seq_num, 'get', 'PICP_STORE_RX90_SETTINGS', [0x0])
        if rc != 0 or 'PASS' not in info:
            return 1, 'execute_mcm_cmd get PICP_STORE_RX90_SETTINGS fail'
        # sheet_re.write(r, ccnt + 2, info)
        sobj = re.search(r'(^[^\r\n]+)[\r\n]([^\r\n]+)[\r\n]([^\r\n]+)[\r\n]([^\r\n]+)[\r\n]([^\r\n]+)[\r\n]', info)
        if not sobj:
            return 1, '获取RX90 coefficients的mcm命令提取结果失败'
        rx_state = int(sobj.group(1))
        if rx_state == 3:
            break
    if rx_state != 3:
        return 1, '获取RX90 coefficients的mcm命令返回rx_state状态为失败'
    CurRx90X = float(sobj.group(2))
    CurRx90Y = float(sobj.group(3))
    RecRx90X = float(sobj.group(4))
    RecRx90Y = float(sobj.group(5))
    return 0, info


def rx90_temp_coeffs_cal():
    # RX90温度系数校准
    reply2 = execute_mcm_cmd(seq_num, 'eget', 'PIC_RX_RX90_COEFFS_X', [])
    if reply2[0] != 0 or 'PASS' not in reply2[1]:
        return 1, 'eget PIC_RX_RX90_COEFFS_X fail'

    RX_RX90_FCX_COEFF_T = float(reply2[1].split('\n')[0].split(',')[0])
    RX_RX90_FCX_COEFF_A = float(reply2[1].split('\n')[0].split(',')[1])
    RX_RX90_FCX_COEFF_B = float(reply2[1].split('\n')[0].split(',')[2])
    RX_RX90_FCX_COEFF_C = float(reply2[1].split('\n')[0].split(',')[3])
    RX90X_T_SLOPE = (-0.0015 * RX_RX90_FCX_COEFF_C) + 0.0082
    reply3 = execute_mcm_cmd(seq_num, 'eset', 'PIC_RX_RX90_COEFFS_X',
                             [0x0, float2hex(RX90X_T_SLOPE), float2hex(RX_RX90_FCX_COEFF_A),
                              float2hex(RX_RX90_FCX_COEFF_B), float2hex(RX_RX90_FCX_COEFF_C)])
    if reply3[0] != 0 or 'PASS' not in reply3[1]:
        return 1, 'eset PIC_RX_RX90_COEFFS_X fail'

    reply4 = execute_mcm_cmd(seq_num, 'eget', 'PIC_RX_RX90_COEFFS_Y', [])
    if reply4[0] != 0 or 'PASS' not in reply4[1]:
        return 1, 'eget PIC_RX_RX90_COEFFS_Y fail'
    RX_RX90_FCY_COEFF_T = float(reply4[1].split('\n')[0].split(',')[0])
    RX_RX90_FCY_COEFF_A = float(reply4[1].split('\n')[0].split(',')[1])
    RX_RX90_FCY_COEFF_B = float(reply4[1].split('\n')[0].split(',')[2])
    RX_RX90_FCY_COEFF_C = float(reply4[1].split('\n')[0].split(',')[3])
    RX90Y_T_SLOPE = (-0.0012 * RX_RX90_FCY_COEFF_C) + 0.0059
    reply5 = execute_mcm_cmd(seq_num, 'eset', 'PIC_RX_RX90_COEFFS_Y',
                             [0x0, float2hex(RX90Y_T_SLOPE), float2hex(RX_RX90_FCY_COEFF_A),
                              float2hex(RX_RX90_FCY_COEFF_B), float2hex(RX_RX90_FCY_COEFF_C)])
    if reply5[0] != 0 or 'PASS' not in reply5[1]:
        return 1, 'eset PIC_RX_RX90_COEFFS_Y fail'
    if HDR_FLAG:
        rc, res_value = handleDiagSetGetCmd(seq_num, "eset", "PIC_EEPROM_HDR", [0, 0xCAFEBABE])
    else:
        rc, res_value = execute_mcm_cmd(seq_num, "eset", "PIC_EEPROM_HDR", [0, 0xCAFEBABE])
    if rc != 0 or 'PASS' not in res_value:
        return 1, "eset PIC_EEPROM_HDR fail"
    return 0, ""


def check_rx_state():
    # 判断Rx state状态: 检查0x90C1=0x3F；0xB250=0；
    rc, val1 = read_one_reg_retry(seq_num, 0x90C1)
    if rc != 0:
        return rc, val1
    if val1 != 0x3f:
        return 1, f'Rx state error, value of 0x90C1 is not 0x3f'
    rc, val2 = read_one_reg_retry(seq_num, 0xB250)
    if rc != 0:
        return rc, val2
    if val2 != 0:
        return 1, f'Rx state error, value of 0xB250 is not 0'
    return 0, ''


def rxadc_output_recovery():
    # 判断Rx state状态: 检查0x90C1=0x3F；0xB250=0；
    rc, val = check_rx_state()
    if rc != 0:
        # Force the RX90 to converge to a good state
        rc, reply = execute_mcm_cmd(seq_num, "set", "PICP_FORCE_RX90_CNTL", [0, 1])
        if rc != 0 or 'PASS' not in reply:
            return 1, 'set PICP_FORCE_RX90_CNTL fail'
        time.sleep(2)
        rc, val = check_rx_state()
        if rc != 0:
            return 1, 'Rx state error after Force the RX90 to converge to a good state'
    return 0, ''


def cal_mdl(val1, val2):
    # MDL_XI=10*lg（TXPDX/（TXPDX+TXPDXI))
    try:
        my_sig.infoSignal.emit(-1,f'cal_mdl, val1: {val1}, val2: {val2}')
        val1 = float(val1)
        val2 = float(val2)
        val_temp = val1 / (val1 + val2)
        mdl = 10 * math.log10(val_temp)
        my_sig.infoSignal.emit(-1,f'mdl: {mdl}')
        return 0, mdl
    except Exception as e:
        my_sig.sig_exception.emit(f"cal_mdl Exception: " + str(e))
        return 1, str(e)


def vcc_offset(sv, mv):
    mv = float(mv)
    sv = float(sv)
    offset = abs(sv - mv)  # round(sv - mv, 8)  # 待确认是否取绝对值
    my_sig.infoSignal.emit(-1,f'sv: {sv}, mv: {mv}, vcc_offset: {offset}')
    up_lmt = sv * 0.04
    if not 0 <= offset <= up_lmt:
        return 1, f'offset:{offset} out of range 0~{up_lmt}'
    return 0, offset


def RXPD():
    if int(rem_dic['PIC_RXPDXY_HIGH_raw']) < 61440:
        res = float(rem_dic['PIC_RXPDXY_HIGH'])
    else:
        res = float(rem_dic['PIC_RXPDXY_LOW'])
    return 0, res



def TXPDXY_LOG():
    # TXPDXY_LOG = 10*lg(TXPD/1000) - ITLA_POWER
    return 0, 10 * (math.log10(rem_dic['TXPD'] / 1000.0)) - float(rem_dic['ITLA_POWER'])


def RFPD_LOSS_FA():
    # RFPD_LOSS_FA= 10*log10(LORFPD)-10
    # my_sig.infoSignal.emit(-1,f'lorfpd: {lorfpd}')
    return 0, 10 * math.log10(rem_dic['LORFPD']) - 10


def RXPD_LOSS_FA():
    # RXPD_LOSS_FA= 10*log10(RXPDXY)-5
    # my_sig.infoSignal.emit(-1,f'rxpdxy: {rxpdxy}')
    return 0, 10 * math.log10(rem_dic['RXPDXY']) - 5


def RXPD_LOSS_OBS():
    # RXPD_LOSS_OBS = 10lg（RXPD-RXPD_DARK_CURRENT）- RX POWER - 30
    return 0, 10 * math.log10(rem_dic['RXPD'] - rem_dic['RXPD_DARK_CURRENT']) - rem_dic['RX_POWER'] - 30


def assign(a):
    # 赋值，函数值为入参值
    return 0, a


def TEF(seq_num, val_lst):
    rc, res_value = execute_mcm_cmd(seq_num, "get", "ASIC_REG", val_lst)
    if rc != 0 or 'PASS' not in res_value:
        return 1, 'mcm cmd fail'
    sobj = re.search(r'(^[^\r\n]+)[\r\n]', res_value)
    if not sobj:
        return 1, f"从ASIC_REG命令返回信息中提取结果失败"
    val = int(sobj.group(1), 16)
    I = bit_cutout_signed(val, 12, 8)
    Q = bit_cutout_signed(val, 28, 24)
    return 0, (I, Q)


def str2val(strval):
    # 字符串转数值，输入必须是十六进制字符串/十进制字符串/浮点数字符串
    if not isinstance(strval, str):
        return strval
    if re.search('^0x[0-9a-f]+$', strval, re.IGNORECASE):  # 0x开头，十六进制数
        return int(strval, 16)
    elif re.search('^[0-9a-f]+h$', strval, re.IGNORECASE):  # h结尾，十六进制数
        return int(strval[:-1], 16)
    elif re.search('^[+-]?\d+$', strval):  # 整数
        return int(strval)
    else:
        if ".0" in strval and strval[-2:] == '.0':  # xlrd读excel会把整数后面加.0
            return int(strval[:-2])
        return float(strval)


def val_to_str(val):
    if isinstance(val, str):
        val = val.strip()
    else:
        val = str(val)
    return val


def get_reg_info(filepath, addrdic, addrlst):
    # 返回sheet；addrdic保存寄存器地址、所在excel的行数（从0开始计数）；addrlst按excel中的顺序保存寄存器地址
    tb = xlrd.open_workbook(filepath)
    sheet = tb.sheet_by_index(0)
    rowcnt = sheet.nrows
    addrdic.clear()
    addrlst.clear()

    i = 0
    row = -1
    while row < rowcnt - 1:
        row += 1
        val_col0 = val_to_str(sheet.cell(row, 0).value)
        if val_col0 == 'Address':
            # 在'Address'下面找寄存器地址，不是寄存器地址格式时、或是已保存的地址时，退出循环
            r = row
            while r < rowcnt - 1:
                r += 1
                val_r = val_to_str(sheet.cell(r, 0).value).upper()
                if not re.search('[0-9a-f]+h', val_r, re.IGNORECASE) or val_r in addrdic:
                    break
                addrdic[val_r] = (row, i)  # 记录寄存器地址对应的表头行号
                addrlst.append(val_r)
                i += 1
            row = r  # 后续从第r行继续往下遍历
        elif val_col0 == 'Address Range':  # 'Address Range'只看下一行是不是地址范围
            addrrange = val_to_str(sheet.cell(row + 1, 0).value)
            sobj = re.search('([0-9a-f]+)h-([0-9a-f]+)h', addrrange, re.IGNORECASE)
            if not sobj:
                print(f'can not match Address Range: {addrrange}')
                continue
            addrbegin = int(sobj.group(1), 16)
            addrend = int(sobj.group(2), 16)
            # print(f'addrbegin: {addrbegin}, addrend: {addrend}')
            for ad in range(addrbegin, addrend + 1):
                addr = hex(ad)[2:].upper() + 'H'
                if addr in addrdic:
                    break
                addrdic[addr] = (row, i)
                addrlst.append(addr)
                i += 1
            row += 1
    # print(addrdic)
    return sheet


def nextaddr(curaddr, dic, lst):
    # 下一个寄存器地址
    if curaddr not in dic:
        return None
    curi = dic[curaddr][1]
    if curi + 1 >= len(lst):
        return None
    return lst[curi + 1]


def preaddr(curaddr, dic, lst):
    # 上一个寄存器地址
    if curaddr not in dic:
        return None
    curi = dic[curaddr][1]
    if curi - 1 < 0:
        return None
    return lst[curi - 1]


def addr_noh(addr: str):
    if addr and addr[-1] in 'hH':
        addr = addr[:-1]
    return addr


def addr_h(addr: str):
    if addr and addr[-1] not in 'hH':
        addr += 'H'
    return addr


def get_workbook(filepath):
    # 返回workbook
    wb = xlrd.open_workbook(filepath)
    return wb


def get_workbook_sheet(wb, sheetname):
    # workbook中sheetname不存在时则创建
    try:
        return wb.get_sheet(sheetname)
    except Exception:
        return wb.add_sheet(sheetname)


def check_val_range(val, expect):
    # 检查val是否符合预期
    expect = val_to_str(expect)  # 统一转成字符串处理
    if not expect or expect == '/':
        return True
    my_sig.infoSignal.emit(-1,f'check_val_range: type(val): {type(val)}, val: {val}, expect:{expect}...')
    # up = float("inf")
    # low = float("-inf")
    if isinstance(val, tuple):  # 如果val是元组，只取第一个值
        val = val[0]
    # expect = val_to_str(expect)  # 统一转成字符串处理
    # if not expect or expect == '/':
    #     return True
    sobj = re.search("(\S+)~(\S+)", expect)  # 预期是个有上下限的范围，如1~5，判断val是否在范围内
    if sobj:
        if isinstance(val, str):
            val = str2val(val)
        low = str2val(sobj.group(1))
        up = str2val(sobj.group(2))
        my_sig.infoSignal.emit(-1,f'low: {low}, up: {up}')
        if low <= val <= up:
            my_sig.infoSignal.emit(-1,f'val: {val} in range [{low}, {up}]')
            return True
        my_sig.infoSignal.emit(-1,f'val: {val} out of range [{low}, {up}]')
        return False
    sobj = re.search("(\S+)±(\S+)", expect)  # 预期是个有上下限的范围，如1~5，判断val是否在范围内
    if sobj:
        if isinstance(val, str):
            val = str2val(val)
        mid = str2val(sobj.group(1))
        delta = str2val(sobj.group(2))
        low = mid - delta
        up = mid + delta
        my_sig.infoSignal.emit(-1,f'low: {low}, up: {up}')
        if low <= val <= up:
            my_sig.infoSignal.emit(-1,f'val: {val} in range [{low}, {up}]')
            return True
        my_sig.infoSignal.emit(-1,f'val: {val} out of range [{low}, {up}]')
        return False
    sobj = re.search("^>\s*(\S+)", expect)  # 预期是个有下限的范围，如>1，判断val是否在范围内
    if sobj:
        if isinstance(val, str):
            val = str2val(val)
        low = str2val(sobj.group(1))
        my_sig.infoSignal.emit(-1,f'low: {low}')
        if val >= low:
            my_sig.infoSignal.emit(-1,f'val: {val} in range [{low}, ]')
            return True
        my_sig.infoSignal.emit(-1,f'val: {val} out of range [{low}, ]')
        return False
    sobj = re.search("^<\s*(\S+)", expect)  # 预期是个有上限的范围，如<5，判断val是否在范围内
    if sobj:
        if isinstance(val, str):
            val = str2val(val)
        up = str2val(sobj.group(1))
        my_sig.infoSignal.emit(-1,f'up: {up}')
        if val <= up:
            my_sig.infoSignal.emit(-1,f'val: {val} in range [ , {up}]')
            return True
        my_sig.infoSignal.emit(-1,f'val: {val} out of range [ , {up}]')
        return False
    if re.search('^0x[0-9a-f]+$', expect, re.IGNORECASE) or re.search('^[0-9a-f]+h$', expect,
                                                                      re.IGNORECASE):  # 预期是个十六进制数，判断val与预期是否相等
        if isinstance(val, str):
            val = str2val(val)
        if val == str2val(expect):
            return True
        my_sig.infoSignal.emit(-1,f'val: {val} not equal to expect value: {expect}')
        return False
    if re.search('^[+-]?\d+(\.\d+)?$', expect):  # 预期是整数/浮点数，判断val与预期是否相等（浮点数一般不比较相等吧？）
        if isinstance(val, str):
            val = str2val(val)
        if val == str2val(expect):
            return True
        my_sig.infoSignal.emit(-1,f'val: {val} not equal to expect value: {expect}')
        return False
    if isinstance(val, str):  # 其他情况，val是字符串时，判断val与预期是否匹配
        if re.search(expect, val, re.IGNORECASE):
            return True
    return False


def bit_cutout(num, bit_h, bit_l):
    # 如bit_h: 13, bit_l: 8
    leng = bit_h - bit_l + 1
    return (num >> bit_l) & (2 ** leng - 1)


def bit_cutout_signed(num, bit_h, bit_l):
    # 如bit_h: 13, bit_l: 8
    leng = bit_h - bit_l + 1
    unsign_val = (num >> bit_l) & (2 ** leng - 1)
    if unsign_val >= 2 ** (leng - 1):  # 最高位是1
        sign_val = unsign_val - 2 ** leng
    else:
        sign_val = unsign_val
    return sign_val


def bit_cutout_unsigned(num, bit_h, bit_l):
    # 如bit_h: 13, bit_l: 8
    leng = bit_h - bit_l + 1
    unsign_val = (num >> bit_l) & (2 ** leng - 1)
    return unsign_val


def BER_to_Q(BER):
    Q_factor = 20 * math.log10(2 ** 0.5 * erfcinv(2 * BER))
    return Q_factor


def BSP_DAC_REG(seq_num, detail):
    # 读BSP_DAC_REG
    rc, res_value = execute_mcm_cmd(seq_num, "get", "BSP_DAC_REG", [0, detail])
    if rc != 0 or 'PASS' not in res_value:
        return 1, f'get BSP_DAC_REG {detail}失败'
    strs = res_value.split('\n')[0].split(',')
    current = float(strs[0])
    dac = int(strs[1])
    return 0, (current, dac)


def get_dQ():
    rc, BER_X = get_pre_BERX(seq_num)
    if rc != 0:
        return rc, BER_X
    rc, BER_Y = get_pre_BERY(seq_num)
    if rc != 0:
        return rc, BER_Y
    dQ = BER_to_Q(BER_X) - BER_to_Q(BER_Y)
    my_sig.infoSignal.emit(-1,"dQ: {dQ}")
    return 0, dQ


def get_tx_freq():
    rc, val1 = read_one_reg_ex(seq_num, 0xB450)
    if rc != 0:
        return 1, val1
    rc, val2 = read_one_reg_ex(seq_num, 0xB460)
    if rc != 0:
        return 1, val2
    freq = val1 + val2 * 0.00005
    return 0, freq


def getfrequency():
    rc, result = read_one_reg_ex(seq_num, 0xB400)
    if rc != 0:
        return 1, result
    TX_CHL = result & 0x3FF
    TX_Grid = result >> 13
    if TX_Grid == 0:
        TX_Grid = 0.1
    elif TX_Grid == 1:
        TX_Grid = 0.05
    elif TX_Grid == 2:
        TX_Grid = 0.025
    elif TX_Grid == 3:
        TX_Grid = 0.0125
    else:
        TX_Grid = 0.00625
    rc, channel_min_1 = read_one_reg_ex(seq_num, 0x818B)
    if rc != 0:
        return 1, channel_min_1
    rc, channel_min_2 = read_one_reg_ex(seq_num, 0x818C)
    if rc != 0:
        return 1, channel_min_2
    rc, channel_min_3 = read_one_reg_ex(seq_num, 0x818D)
    if rc != 0:
        return 1, channel_min_3
    channel_min_4 = (channel_min_2 << 8) + channel_min_3
    channel_min = channel_min_1 + channel_min_4 / 20 / 1000
    # print(channel_min)
    # print(TX_Grid)
    # print(TX_CHL)
    rc, result = read_one_reg_ex(seq_num, 0xB430)
    if rc != 0:
        return 1, result
    FREQ = (TX_CHL - 1) * TX_Grid + channel_min + result
    my_sig.infoSignal.emit(-1,f'getfrequency: {FREQ}')
    return 0, FREQ


def TAP_MON_TX_POWER_ADC(seq_num):
    rc, reply = execute_mcm_cmd(seq_num, "get", "TAP_MON_TX_POWER_ADC", [0])
    if rc != 0 or 'PASS' not in reply:
        return 1, 'get TAP_MON_TX_POWER_ADC fail'
    linestrlist = reply.split('\n')
    re = int(linestrlist[0])
    return 0, re


x_2_plot = []
y_2_plot = []
m_2_plot = []
n_2_plot = []


def linear_fit(x, y):
    def fun(z, a, b):
        return a * z + b

    slope, offset = optimize.curve_fit(fun, x, y)[0]
    x_2_plot = x
    y_2_plot = y
    m_2_plot = numpy.linspace(x[0] - 1, x[-1] + 1, 100)
    n_2_plot = [fun(v, slope, offset) for v in m_2_plot]
    return float(slope), float(offset)


def quadratic_fit(x, y):
    def fun(z, a, b, c):
        return a * z ** 2 + b * z + c

    a, b, c = optimize.curve_fit(fun, x, y)[0]
    x_2_plot = x
    y_2_plot = y
    m_2_plot = numpy.linspace(x[0] - 1, x[-1] + 1, 100)
    n_2_plot = [fun(v, a, b, c) for v in m_2_plot]
    return float(a), float(b), float(c)


def get_PUMP_LAS_COEFFS(seq_num):
    rc, reply = execute_mcm_cmd(seq_num, "eget", "PUMP_LAS_COEFFS", [0])
    if rc != 0 or 'PASS' not in reply:
        return 1, 'eget PUMP_LAS_COEFFS fail'
    strs = reply.split('\n\n-PASS-')[0].split('\n')
    coeffs = [float(x) for x in strs]
    return 0, coeffs


def BSP_ADC_REG(seq_num, detail):
    rc, reply = execute_mcm_cmd(seq_num, "get", "BSP_ADC_REG", [0, detail])
    if rc != 0 or 'PASS' not in reply:
        return 1, f'get BSP_ADC_REG {detail} fail'
    strs = reply.split('\n')[0].split(',')
    current = float(strs[0])
    adc = int(strs[1])
    return 0, (current, adc)


def get_pic_temp(seq_num):
    rc, result = execute_mcm_cmd(seq_num, "get", "BSP_ADC_REG", [0, 'PIC_TOP1_NTC'])
    if rc != 0 or 'PASS' not in result:
        return 1, 'get BSP_ADC_REG PIC_TOP1_NTC fail'
    linestrlist = result.split(',')
    num = float(linestrlist[0])
    return 0, num


def setfrequency(seq_num, FREQ):
    if FREQ <= 196.1:
        channel_min_1 = read_one_reg(seq_num, 0x818B)
        channel_min_2 = read_one_reg(seq_num, 0x818C)
        channel_min_3 = read_one_reg(seq_num, 0x818D)
        channel_min_4 = (channel_min_2 << 8) + channel_min_3
        channel_min = channel_min_1 + channel_min_4 / 20 / 1000
        TX_CHL = (FREQ * 160 - channel_min * 160) + 1
        # print(TX_CHL)
        TX_Grid = 5 << 13
        # print(TX_Grid)
        REG = int(TX_CHL + TX_Grid)
        write_one_reg(seq_num, 0xB400, REG)
        # print(hex(int(REG)))
        return 0, ''
    else:
        return 1, 'setfrequency fail'


def PICP_AVG_GC_MON(seq_num):
    rc, reply = execute_mcm_cmd(seq_num, "get", "PICP_AVG_GC_MON", [0])
    if rc != 0 or 'PASS' not in reply:
        return 1, 'get PICP_AVG_GC_MON fail'
    strs = reply.split('\n\n-PASS-')[0].split('\n')
    TIAs = [float(x) for x in strs[12:16]]
    Power_ch = float(strs[0])
    return 0, (TIAs, Power_ch)


def PICP_RX_OPT_PWR_RATIO(seq_num):
    rc, reply = execute_mcm_cmd(seq_num, "get", "PICP_RX_OPT_PWR_RATIO", [0])
    if rc != 0 or 'PASS' not in reply:
        return 1, 'get PICP_RX_OPT_PWR_RATIO fail'
    linestrlist = reply.split('\n')
    RXPDL_Ratio_woDiv = float(linestrlist[0])
    RXPDL_Ratio_wiDiv = float(linestrlist[1])
    return 0, (RXPDL_Ratio_woDiv, RXPDL_Ratio_wiDiv)


def AVS_CALIBRATE(seq_num, detail='exec'):
    rc, reply = execute_mcm_cmd(seq_num, "get", "AVS_CALIBRATE", [detail])
    if rc != 0 or 'PASS' not in reply:
        return 1, f'get AVS_CALIBRATE {detail} fail'
    strs = reply.split('\n')[0].split(',')
    re = float(strs[0])
    return 0, re


# def get_rfpd_slope_off_temp(seq_num):

def dump_room_data(hg_offset_r,lg_offset_r,):
    hg_offset_r = rem_dic['RX_PWR_HG_OFFSET']
    lg_offset_r = rem_dic['RX_PWR_LG_OFFSET']
    C1_r = rem_dic['C1']
    C2_r = rem_dic['C2']
    C3_r = rem_dic['C3']
    T_r = float(rem_dic['T_r'])
    my_sig.infoSignal.emit(-1,
        f'dump room data: hg_offset_r: {hg_offset_r}, lg_offset_r: {lg_offset_r}, C1_r: {C1_r}, C2_r: {C2_r}, C3_r: {C3_r}, T_r: {T_r}')
    pickle.dump([hg_offset_r, lg_offset_r, C1_r, C2_r, C3_r, T_r], open(logdir + '\\room.pkl', 'wb'))

    if EDFAless is True:
        Ct1_r = rem_dic['Ct1']
        Ct2_r = rem_dic['Ct2']
        Ct3_r = rem_dic['Ct3']
        my_sig.infoSignal.emit(-1,f'dump room data: Ct1_r: {Ct1_r}, Ct2_r: {Ct2_r}, Ct3_r: {Ct3_r}, T_r: {T_r}')
        pickle.dump([Ct1_r, Ct2_r, Ct3_r, T_r], open(logdir + '\\room_TX.pkl', 'wb'))
    return 0, ''


def dump_cold_data():
    hg_offset_c = rem_dic['RX_PWR_HG_OFFSET']
    lg_offset_c = rem_dic['RX_PWR_LG_OFFSET']
    C1_c = rem_dic['C1']
    C2_c = rem_dic['C2']
    C3_c = rem_dic['C3']
    T_c = float(rem_dic['T_c'])
    my_sig.infoSignal.emit(-1,
        f'dump cold data: hg_offset_c: {hg_offset_c}, lg_offset_c: {lg_offset_c}, C1_c: {C1_c}, C2_c: {C2_c}, C3_c: {C3_c}, T_c: {T_c}')
    pickle.dump([hg_offset_c, lg_offset_c, C1_c, C2_c, C3_c, T_c], open(logdir + '\\cold.pkl', 'wb'))
    if EDFAless is True:
        Ct1_c = rem_dic['Ct1']
        Ct2_c = rem_dic['Ct2']
        Ct3_c = rem_dic['Ct3']
        my_sig.infoSignal.emit(-1,f'dump room data: Ct1_r: {Ct1_c}, Ct2_r: {Ct2_c}, Ct3_r: {Ct3_c}, T_r: {T_c}')
        pickle.dump([Ct1_c, Ct2_c, Ct3_c, T_c], open(logdir + '\\cold_TX.pkl', 'wb'))
    return 0, ''


def dump_hot_data():
    hg_offset_h = rem_dic['RX_PWR_HG_OFFSET']
    lg_offset_h = rem_dic['RX_PWR_LG_OFFSET']
    C1_h = rem_dic['C1']
    C2_h = rem_dic['C2']
    C3_h = rem_dic['C3']
    T_h = float(rem_dic['T_h'])
    my_sig.infoSignal.emit(-1,
        f'dump room data: hg_offset_h: {hg_offset_h}, lg_offset_h: {lg_offset_h}, C1_h: {C1_h}, C2_h: {C2_h}, C3_h: {C3_h}, T_h: {T_h}')
    pickle.dump([hg_offset_h, lg_offset_h, C1_h, C2_h, C3_h, T_h], open(logdir + '\\hot.pkl', 'wb'))
    if EDFAless is True:
        Ct1_h = rem_dic['Ct1']
        Ct2_h = rem_dic['Ct2']
        Ct3_h = rem_dic['Ct3']
        my_sig.infoSignal.emit(-1,f'dump room data: Ct1_h: {Ct1_h}, Ct2_h: {Ct2_h}, Ct3_h: {Ct3_h}, T_h: {T_h}')
        pickle.dump([Ct1_h, Ct2_h, Ct3_h, T_h], open(logdir + '\\hot_TX.pkl', 'wb'))
    return 0, ''


def generate_hex_param(*args):
    re = [0]
    re += [float2hex(x) for x in args]
    return re


def test_mcm_cmd(seq_num, action, key, valst, sheet_re, r, ccnt, extract, method, expect):
    average_cnt = 1
    sobj = re.search(r"average\((\d+)\)", method)
    if sobj:
        average_cnt = int(sobj.group(1))
    res = None
    res_tmp = ''
    total = 0.0
    for i in range(average_cnt):
        rc, info = execute_mcm_cmd(seq_num, action, key, valst)
        if rc != 0:
            return rc, 'MCM命令失败'
        sheet_re.write(r, ccnt + 2, info)
        res = info
        if extract and extract != '/':  # 需要通过正则表达式extract从命令返回信息中提取数据
            sobj = re.search(extract, info)
            if not sobj:
                return 1, f"从MCM命令返回信息中提取结果失败，正则表达式: {extract}"
            res_all = sobj.groups()
            my_sig.infoSignal.emit(-1,f'res: {res_all}')
            if len(res_all) > 1:
                res = res_all
            else:
                res = res_all[0]  # sobj.group(1)
                if average_cnt > 1:
                    res_val = str2val(res)
                    my_sig.infoSignal.emit(-1,f'res_val: {res_val}')
                    total += res_val
            res_tmp += f'{res}' + ' '

    sheet_re.write(r, ccnt + 3, res_tmp[:-1])
    if average_cnt > 1:
        res = total / average_cnt
        my_sig.infoSignal.emit(-1,f'sum: {total}, cnt: {average_cnt}')
    else:
        # res = val_by_method(method, res)
        res = val_after_method(res, method)
    sheet_re.write(r, ccnt + 4, f'{res}')
    if check_val_range(res, expect):  # 检查结果是否符合预期范围
        return 0, res
    return 1, f'测试结果{res}, 不符合预期范围{expect}'


def val_after_method(strval, method):
    global rem_dic
    # sheet_re.write(r, ccnt + 1, 'PASS')  # 不比较，只存？
    if method == 'hex_to_float':
        res_value = hex_to_float(strval)
    elif method == 'hex_to_unsigned_int':
        res_value = hex_to_unsigned_int(strval)
    elif method == 'hex_to_signed_int_001':
        res_value = hex_to_signed_int_001(strval)
    elif method == 'hex_to_signed_int_div256':
        res_value = hex_to_signed_int_div256(strval)
    # elif method == 'get_scaling':
    #     valst = strval.split(',')
    #     res_value = float(valst[0]) / int(valst[1])  # 逗号前是实际值，逗号后是DAC值
    #     rem_dic['DAC_value'] = int(valst[1])
    else:
        sobj = re.search(r"bit_cut_signed_num\((\d+),\s*(\d+)\)", method)  # bit_cut_signed_num(13, 8)
        if sobj:
            bit_h = int(sobj.group(1))
            bit_l = int(sobj.group(2))
            num = str2val(strval)
            return bit_cutout_signed(num, bit_h, bit_l)
        sobj = re.search(r"bit_cut_unsigned_num\((\d+),\s*(\d+)\)", method)  # bit_cut_unsigned_num(13, 8)
        if sobj:
            bit_h = int(sobj.group(1))
            bit_l = int(sobj.group(2))
            num = str2val(strval)
            return bit_cutout_unsigned(num, bit_h, bit_l)
        res_value = strval
    return res_value


def get_cfg_col_idx(sheet_ori, ccnt):
    # 下标
    iexp = -1
    itype = -1
    iact = -1
    ikey = -1
    ival = -1
    iextract = -1
    imethod = -1
    ifailstop = -1
    irem = -1
    for c in range(ccnt):
        strhead = sheet_ori.cell(0, c).value
        if "pre_result" in strhead:
            iexp = c
        elif "extract" in strhead:  # 需要从结果中提取信息时配置
            iextract = c
        elif "method" in strhead:
            imethod = c
        elif "stop_on_fail" in strhead:
            ifailstop = c
        elif "remember" in strhead:
            irem = c
        elif "command_type" in strhead:
            itype = c
        elif "action" in strhead:
            iact = c
        elif "key" in strhead:
            ikey = c
        elif "value" in strhead:  # value必须在最右列
            ival = c
            break
    return iexp, iextract, imethod, ifailstop, irem, itype, iact, ikey, ival


def get_param(sheet_ori, r, iexp, iextract, imethod, ifailstop, irem, iact, ikey, ival):
    # cmd_type = val_to_str(sheet_ori.cell(r, itype).value)
    action = sheet_ori.cell(r, iact).value.strip().lower()
    key = val_to_str(sheet_ori.cell(r, ikey).value)
    val = val_to_str(sheet_ori.cell(r, ival).value)
    exp = val_to_str(sheet_ori.cell(r, iexp).value)
    extract = val_to_str(sheet_ori.cell(r, iextract).value)
    method = val_to_str(sheet_ori.cell(r, imethod).value)
    stop_on_fail = False if sheet_ori.cell(r, ifailstop).value == 'N' else True
    rem_name = val_to_str(sheet_ori.cell(r, irem).value)
    test_des = sheet_ori.cell(r, 1).value
    return exp, extract, method, stop_on_fail, rem_name, action, key, val, test_des


def get_opm_pwr():
    global olp85
    opm_pwr = INVALID_PWR
    for i in range(3):  # 3次重试，有概率会返回-999，延时1s后重试
        opm_pwr = olp85.read_power()
        if opm_pwr != INVALID_PWR:
            return opm_pwr
        time.sleep(1)
    return opm_pwr


# def measure_tx_pwr():
def get_tx_pwr():
    # calibration工序使用，需切换光开关
    global osw_tr
    osw_tr.set_olp(0)
    opm_pwr = get_opm_pwr()
    tx_pwr = opm_pwr + tx_pwr_offset
    my_sig.infoSignal.emit(-1,f'opm pwr: {opm_pwr}, tx power: {tx_pwr}')
    return tx_pwr


# def measure_rx_power():
def get_rx_pwr():
    # calibration工序使用，需切换光开关
    global osw_tr
    osw_tr.set_olp(1)
    opm_pwr = get_opm_pwr()
    rx_pwr = opm_pwr + rx_pwr_offset
    my_sig.infoSignal.emit(-1,f'opm pwr: {opm_pwr}, rx power: {rx_pwr}')
    return rx_pwr


def set_rx_power(target):
    global voa_rx
    # opm_pwr = target - rx_pwr_offset
    # my_sig.infoSignal.emit(-1,f'set rx_power: {target}, opm pwr: {opm_pwr}..')
    # return set_to_power(olp85, voa, opm_pwr)
    t = 0
    t_limit = 20
    while True:
        power = get_rx_pwr()
        att = voa_rx.get_attr()
        dp = power - target
        my_sig.infoSignal.emit(-1,'Power:%s dBm, ATT:%s dB' % (power, att))
        if abs(dp) < 0.5:
            return 0, power
        elif t > t_limit:
            my_sig.infoSignal.emit(-1,f'无法调节到目标值{target}')
            return 1, f'无法调节到目标值{target}'
        else:
            voa_rx.set_attr(att + dp * 1)
            t += 1
            time.sleep(0.5)


def rx_power_adjust_below(pwr):
    global voa_rx
    global olp85
    print(f'调整接收光功率到{pwr}dBm以下')
    voa_rx.set_attr(voa_rx.il_max)
    # olp85.powermode()
    # power = olp85.read_power()
    power = get_rx_pwr()
    while power > pwr:
        msgbox(f'调整接收光功率无法调整到{pwr}dBm以下，请检查')
        # power = olp85.read_power()
        return 1, power
    return 0, power


def rx_power_adjust_above(pwr):
    global voa_rx
    global olp85
    print(f'调整接收光功率到{pwr}dBm以上')
    voa_rx.set_attr(voa_rx.il_min)
    # olp85.powermode()
    # RX_HP_dB = olp85.read_power()
    RX_HP_dB = get_rx_pwr()
    if RX_HP_dB < pwr:
        msgbox(f'调整接收光功率无法调整到{pwr}dBm以上，请检查')
        return 1, RX_HP_dB
    return 0, RX_HP_dB


def get_TEF_delta():
    global rem_dic
    TEF_HI_list = []
    TEF_HQ_list = []
    TEF_VI_list = []
    TEF_VQ_list = []
    average_times = 10
    for i in range(average_times):
        rc, h = TEF(seq_num, [0x0, 0xF01A0044])
        if rc != 0:
            return rc, h
        time.sleep(0.5)
        rc, v = TEF(seq_num, [0x0, 0xF01A0054])
        if rc != 0:
            return rc, v
        time.sleep(0.5)
        TEF_HI_list.append(h[0])
        TEF_HQ_list.append(h[1])
        TEF_VI_list.append(v[0])
        TEF_VQ_list.append(v[1])
        my_sig.infoSignal.emit(-1,f'第{i + 1}次读取:[TEF_HI,TEF_HQ,TEF_VI,TEF_VQ]=[{h[0]}, {h[1]}, {v[0]}, {v[1]}]')
    TEF_Hdelta = (sum(TEF_HI_list) - sum(TEF_HQ_list)) / average_times
    TEF_Vdelta = (sum(TEF_VI_list) - sum(TEF_VQ_list)) / average_times
    my_sig.infoSignal.emit(-1,f'TEF_Hdelta: {TEF_Hdelta} TEF_Vdelta: {TEF_Vdelta}')
    # rem_dic['TEF_Hdelta'] = TEF_Hdelta
    # rem_dic['TEF_Vdelta'] = TEF_Vdelta
    return 0, (TEF_Hdelta, TEF_Vdelta)


def IQ_balance():
    global rem_dic
    TEF_Hdelta = rem_dic['TEF_Hdelta']
    TEF_Vdelta = rem_dic['TEF_Vdelta']
    OA_HInew = int(rem_dic['OA_HInew'], 16)
    OA_HQnew = int(rem_dic['OA_HQnew'], 16)
    OA_VInew = int(rem_dic['OA_VInew'], 16)
    OA_VQnew = int(rem_dic['OA_VQnew'], 16)
    TEF_TOLERANCE = 0.3
    loop_times = 0
    while abs(TEF_Hdelta) > TEF_TOLERANCE or abs(TEF_Vdelta) > TEF_TOLERANCE:
        # 通过IQ之间TEF之差，反馈调节驱动电流值，直到差值小于误差容限
        loop_times += 1
        my_sig.infoSignal.emit(-1,'开始进行第%d次IQ Balance调节' % loop_times)
        # OA_tef_slope = 2.5  # 反馈调节系数，经摸索2.5收敛比较快

        if abs(TEF_Hdelta) > TEF_TOLERANCE:
            OA_HInew -= round(abs(TEF_Hdelta) / TEF_Hdelta)  # 计算
            OA_HQnew += round(abs(TEF_Hdelta) / TEF_Hdelta)
            rc, res_value = execute_mcm_cmd(seq_num, "set", "PIC_DRIVER_REG", [0, 0x2014, OA_HInew])
            if rc != 0 or 'PASS' not in res_value:
                return 1, f'OA_HInew 设置为{OA_HInew} 失败'
            my_sig.infoSignal.emit(-1,f'OA_HInew 设置为{OA_HInew} 成功')
            rc, res_value = execute_mcm_cmd(seq_num, "set", "PIC_DRIVER_REG", [0, 0x3014, OA_HQnew])
            if rc != 0 or 'PASS' not in res_value:
                return 1, f'OA_HQnew 设置为{OA_HQnew} 失败'
            my_sig.infoSignal.emit(-1,f'OA_HQnew 设置为{OA_HQnew} 成功')
        if abs(TEF_Vdelta) > TEF_TOLERANCE:
            OA_VInew -= round(abs(TEF_Vdelta) / TEF_Vdelta)
            OA_VQnew += round(abs(TEF_Vdelta) / TEF_Vdelta)

            # 设置
            rc, res_value = execute_mcm_cmd(seq_num, "set", "PIC_DRIVER_REG", [0, 0x0014, OA_VInew])
            if rc != 0 or 'PASS' not in res_value:
                return 1, f'OA_VInew 设置为{OA_VInew} 失败'
            my_sig.infoSignal.emit(-1,f'OA_VInew 设置为{OA_VInew} 成功')
            rc, res_value = execute_mcm_cmd(seq_num, "set", "PIC_DRIVER_REG", [0, 0x1014, OA_VQnew])
            if rc != 0 or 'PASS' not in res_value:
                return 1, f'OA_VQnew 设置为{OA_VQnew} 失败'
            my_sig.infoSignal.emit(-1,f'OA_VQnew 设置为{OA_VQnew} 成功')

        rc, result = get_TEF_delta()  # 计算IQ之间TEF之差
        if rc != 0:
            return 1, result
        TEF_Hdelta, TEF_Vdelta = result
    my_sig.infoSignal.emit(-1,
        f'OA_HInew: {OA_HInew}, OA_HQnew: {OA_HQnew}, OA_VInew: {OA_VInew}, OA_VQnew: {OA_VQnew}, TEF_Hdelta: {TEF_Hdelta}, TEF_Vdelta: {TEF_Vdelta}')
    return 0, (OA_HInew, OA_HQnew, OA_VInew, OA_VQnew, TEF_Hdelta, TEF_Vdelta)


def dQ_init():
    return 0, BER_to_Q(rem_dic['BER_X']) - BER_to_Q(rem_dic['BER_Y'])


def increase_BER():
    global rem_dic
    global voa_ase
    BER = rem_dic['BER']
    PDL_BER_RANGE = [1e-2, 1.3e-2]
    il_max = rem_dic['il_max']
    il_min = 0
    cnt_max = 20
    cnt = 0
    while not PDL_BER_RANGE[0] < BER < PDL_BER_RANGE[1]:
        if cnt > cnt_max:
            return 1, '调整BER失败'
        cnt += 1
        my_sig.infoSignal.emit(-1,'BER:%1.2e' % BER)
        if PDL_BER_RANGE[0] > BER:
            il_max = voa_ase.get_attr()
            # response = msgbox_ques(f'噪声太小，请调整(BER: {BER} ({PDL_BER_RANGE[0]}, {PDL_BER_RANGE[1]})')
        else:
            il_min = voa_ase.get_attr()
            # response = msgbox_ques(f'噪声太大，请调整(BER: {BER} ({PDL_BER_RANGE[0]}, {PDL_BER_RANGE[1]})')
        voa_ase.set_attr((il_max + il_min) / 2)
        time.sleep(1)
        # if not response:
        #     return 1, "退出噪声调整"

        # 3. Reset the BER Counter
        rc, res_value = write_one_reg_ex(seq_num, 0x9061, 0x0001)
        if rc != 0:
            return rc, res_value
        time.sleep(5)  # 12.31
        rc, BER = read_lineside_ber(seq_num)
        if rc != 0:
            return rc, 'get ber fail'
    for i in range(3):
        rc, BER = read_lineside_ber(seq_num)
        if rc != 0:
            return rc, 'get ber fail'
        time.sleep(1)
    my_sig.infoSignal.emit(-1,f'BER: {BER}')
    return 0, BER


def PDL_cal():
    global rem_dic
    TX_PDL_Q_ERROR = 0.1
    INDEX_MAX = 20
    PDL_STEP_VALUE = 25  # 100
    if 'PDL_STEP_VALUE' in rem_dic:
        PDL_STEP_VALUE = int(rem_dic['PDL_STEP_VALUE'])
    DAC_TXPOWXY_RANGE = [44000, 54000]
    index = 0
    dQ_init = rem_dic['dQ_init']
    dQ = dQ_init
    DAC_value = int(rem_dic['DAC_value'])
    SCALING_TXPOWXY = rem_dic['SCALING_TXPOWXY']
    while abs(dQ) > TX_PDL_Q_ERROR:  # and index < INDEX_MAX:
        if index > INDEX_MAX:
            # input('PDL 校准失败')
            return 1, 'PDL校准失败，尝试次数超出限制'
        else:
            DAC_value += PDL_STEP_VALUE
            SCALING_uA = DAC_value * SCALING_TXPOWXY
            if DAC_value > DAC_TXPOWXY_RANGE[1] or DAC_value < DAC_TXPOWXY_RANGE[0]:
                return 1, 'PDL 校准失败，DAC值超出门限'
            else:
                rc, res_value = execute_mcm_cmd(seq_num, "set", "BSP_DAC_REG", [0, 'TXPOWXY', float2hex(SCALING_uA)])
                if rc != 0 or 'PASS' not in res_value:
                    return 1, f'TXPOWXY 设置为{SCALING_uA} 失败'
                my_sig.infoSignal.emit(-1,f'TXPOWXY 设置为{SCALING_uA} 成功')

                rc, res_value = BSP_DAC_REG(seq_num, 'TXPOWXY')  # 读发端DAC值
                if rc != 0:
                    return 1, res_value
                Current, DAC_value = res_value
                # SCALING_TXPOWXY = Current / DAC_value  # 12.31
                my_sig.infoSignal.emit(-1,f'DAC_VALUE: {DAC_value}, SCALING_TXPOWXY: {SCALING_TXPOWXY}')
                rc, res_value = write_one_reg_ex(seq_num, 0x9061, 0x0001)  # 6.2. Reset the BER Counter
                if rc != 0:
                    return 1, res_value
                time.sleep(5)
                rc, dQ = get_dQ()
                if rc != 0:
                    return 1, dQ
                my_sig.infoSignal.emit(-1,f'dQ: {dQ}')
                if index == 0 and abs(dQ) > abs(dQ_init):  # 判断调整方向是否错误 #if index == 0 and dQ > dQ_init:
                    my_sig.infoSignal.emit(-1,'调节方向错误，变更调整方向！')
                    PDL_STEP_VALUE = -PDL_STEP_VALUE
                index += 1
    # FINAL_TXPOWXY = int(DAC_value ** 2 / 2 ** 16)
    TXPOWXY_INIT = (DAC_value * SCALING_TXPOWXY) ** 2  # 2022/7/21 增加
    # my_sig.infoSignal.emit(-1,f'FINAL_TXPOWXY: {FINAL_TXPOWXY}')
    return 0, TXPOWXY_INIT


def tx_voa_cal():
    global rem_dic
    total_maximum_power = float('-inf')
    # current_tx_power = opm.read_power()
    current_tx_power = rem_dic['tx_power']  # 获取输出功率 TBD
    voa_volt = rem_dic['voa_volt']
    MAX_VOA_VOLT = float(rem_dic['MAX_VOA_VOLT'])
    # MAXIMUM_POWER_TARGET = 4
    VOA_STEP = 0.1  # V
    while voa_volt <= MAX_VOA_VOLT + 1:
        if current_tx_power <= total_maximum_power:
            voa_volt -= VOA_STEP
            rc, res_value = execute_mcm_cmd(seq_num, "set", "VOA_TX_VOLTAGE", [0, float2hex(voa_volt)])  # 设置TXPWRADJ
            if rc != 0 or 'PASS' not in res_value:
                return 1, f'VOA_TX_VOLTAGE 设置为{voa_volt} 失败'
            my_sig.infoSignal.emit(-1,f'找到最小损耗点，电压为：{voa_volt} V')
            break
        elif voa_volt == MAX_VOA_VOLT + 1:
            return 1, 'Tx VOA 校准失败,VOA电压扫描范围内找不到最小损耗点！'
        else:
            total_maximum_power = current_tx_power
            voa_volt += VOA_STEP
            rc, res_value = execute_mcm_cmd(seq_num, "set", "VOA_TX_VOLTAGE", [0, float2hex(voa_volt)])  # 设置TXPWRADJ
            if rc != 0 or 'PASS' not in res_value:
                return 1, f'VOA_TX_VOLTAGE 设置为{voa_volt} 失败'
            time.sleep(0.5)
            # current_tx_power = opm.read_power()  # 获取输出功率
            # opm_pwr = get_opm_pwr()
            current_tx_power = get_tx_pwr()  # opm_pwr + tx_pwr_offset
            # my_sig.infoSignal.emit(-1,f'opm pwr: {opm_pwr}, txpower: {current_tx_power}')
    # rem_dic['TX_VOA_MAX_VOLT'] = voa_volt
    # POUT_MAX_VOA = get_opm_pwr() + tx_pwr_offset
    # if POUT_MAX_VOA < MAXIMUM_POWER_TARGET:
    #     my_sig.infoSignal.emit(-1,'Tx VOA 校准失败,无法达到最大输出功率标准')
    return 0, voa_volt


def tx_mon_cal():
    global rem_dic
    TX_MOD_LIMITS = [-10, 6]
    TXMOD_STEP = 2
    txmod = TX_MOD_LIMITS[0]
    x = []
    y = []
    while txmod <= TX_MOD_LIMITS[1]:
        power = int(txmod * 100)  # in 0.01dBm
        rc, res_value = write_one_reg_ex(seq_num, 0xb410, power & 0xffff)  # 设置模块输出功率为txmod dBm
        if rc != 0:
            return rc, res_value
        time.sleep(2)
        TX_POWER = get_tx_pwr()  # get_opm_pwr() + tx_pwr_offset
        rc, TAP_MON_ADC = TAP_MON_TX_POWER_ADC(seq_num)
        if rc != 0:
            return 1, TAP_MON_ADC
        x.append(TX_POWER)
        y.append(TAP_MON_ADC)
        txmod += TXMOD_STEP
    my_sig.infoSignal.emit(-1,f'txpowers: {x}')
    my_sig.infoSignal.emit(-1,f'ADCs: {y}')
    try:
        TAP_MON_TX_SLOPE, TAP_MON_TX_OFFSET = linear_fit(x, y)  # 线性拟合出斜率和偏移
        my_sig.infoSignal.emit(-1,f'TAP_MON_TX_SLOPE, TAP_MON_TX_OFFSET: {TAP_MON_TX_SLOPE}, {TAP_MON_TX_OFFSET}')
    except Exception as err:
        my_sig.sig_exception.emit(f"linear_fit exception: {err}")
        return 1, f"linear_fit exception: {err}"
    # rem_dic['TAP_MON_TX_SLOPE'] = TAP_MON_TX_SLOPE
    # rem_dic['TAP_MON_TX_OFFSET'] = TAP_MON_TX_OFFSET
    return 0, (TAP_MON_TX_SLOPE, TAP_MON_TX_OFFSET)


def set_txpower(val):
    # 设置TX输出功率
    val = float(val)
    rc, res_value = write_one_reg_ex(seq_num, 0xb410, int(val * 100) & 0xffff)
    if rc != 0:
        return 1, f'Tx输出功率设置为{val}dBm 失败'
    my_sig.infoSignal.emit(-1,f'Tx输出功率设置为{val}dBm 成功')
    return 0, ''


def PUMP_PWR_ADJ_INIT():
    rc, res_value = BSP_DAC_REG(seq_num, 'PUMP_PWR_ADJ')
    if rc != 0:
        return 1, res_value
    pump_pwr, _ = res_value
    rc, Clist = get_PUMP_LAS_COEFFS(seq_num)
    if rc != 0:
        return 1, Clist
    if Clist[0] != 0:
        PUMP_PWR_ADJ_INIT = pump_pwr / Clist[0]
    else:
        PUMP_PWR_ADJ_INIT = pump_pwr
    return 0, PUMP_PWR_ADJ_INIT


def pump_cal(tx_max_tar_pwr):
    global olp85
    global rem_dic
    PUMP_LIMITS = [60, 150 + 1]
    PUMP_STEP = 5
    TX_MAX_TARGET_POWER = float(tx_max_tar_pwr)
    # TX_MAX_TARGET_POWER = 4
    PUMP_PWR_ADJ_INIT = float(rem_dic['PUMP_PWR_ADJ_INIT'])
    x = []
    y = []
    # olp85.powermode()
    for PUMP_PWR_ADJ in range(*PUMP_LIMITS, PUMP_STEP):
        rc, reply = execute_mcm_cmd(seq_num, "set", "BSP_DAC_REG", [0, 'PUMP_PWR_ADJ', float2hex(PUMP_PWR_ADJ)])
        if rc != 0 or 'PASS' not in reply:
            return 1, 'set PUMP_PWR_ADJ fail'
        time.sleep(1)
        tx_power = 10 ** (get_tx_pwr() / 10)  # 读功率计功率 in mW
        # tx_power = olp85.read_power_w() * 1000 * (10 ** (tx_pwr_offset/10))  # 读功率计功率 in mW
        my_sig.infoSignal.emit(-1,'tx_Power=%s mW' % tx_power)
        x.append(tx_power)
        y.append(PUMP_PWR_ADJ)
    my_sig.infoSignal.emit(-1,f'tx_Power: {x}, PUMP_PWR_ADJ: {y}')
    slope, offset = linear_fit(x, y)
    my_sig.infoSignal.emit(-1,f'slope: {slope}, offset: {offset}')
    PUMP_PWR_ADJ_TGT = 10 ** (TX_MAX_TARGET_POWER / 10) * slope + offset
    RATIO_PUMP_LUT = PUMP_PWR_ADJ_TGT / PUMP_PWR_ADJ_INIT
    # print(PUMP_PWR_ADJ_TGT, RATIO_PUMP_LUT)
    my_sig.infoSignal.emit(-1,f'PUMP_PWR_ADJ_TGT: {PUMP_PWR_ADJ_TGT}, RATIO_PUMP_LUT: {RATIO_PUMP_LUT}')
    # rem_dic['RATIO_PUMP_LUT'] = RATIO_PUMP_LUT
    if PUMP_PWR_ADJ_TGT > PUMP_LIMITS[1] or PUMP_PWR_ADJ_TGT < PUMP_LIMITS[0]:
        # input('校准失败')
        return 1, f'PUMP_PWR_ADJ_TGT: {PUMP_PWR_ADJ_TGT}, 校准失败'
    return 0, (PUMP_PWR_ADJ_TGT, RATIO_PUMP_LUT)


def set_PUMP_LAS_COEFFS():
    rc, Clist = get_PUMP_LAS_COEFFS(seq_num)
    if rc != 0:
        return rc, Clist
    Clist[0] = rem_dic['RATIO_PUMP_LUT']
    my_sig.infoSignal.emit(-1,f'Clist: {Clist}')
    # RATIO_PUMP_LUT写入EEPROM数据
    rc, reply = execute_mcm_cmd(seq_num, "eset", "PUMP_LAS_COEFFS", generate_hex_param(*Clist))
    if rc != 0 or 'PASS' not in reply:
        return 1, 'eset PUMP_LAS_COEFFS fail'
    # TEMP_REF = rem_dic['TEMP_REF']
    # FREQ_REF = rem_dic['getfrequency']
    # rc, reply = execute_mcm_cmd(seq_num, "eset", "PUMP_LAS_REFS", generate_hex_param(TEMP_REF, FREQ_REF))
    # if rc != 0 or 'PASS' not in reply:
    #     return 1, 'eset PUMP_LAS_REFS fail'
    return 0, f'eset PUMP_LAS_COEFFS {Clist}'


def get_rx_mon_slope_off():
    # global olp85
    # global voa_rx
    global rem_dic

    RXPD_ADC_HIGH = []
    RX_TAP_MONITOR_mW_HIGH = []
    rx_power_start = RX_TAP_MONITOR_LIST_HIGH[0]  # 20220428第一个点反馈调节
    rc, result = set_rx_power(rx_power_start)
    if rc != 0:
        return 1, result
    il0 = voa_rx.get_attr()
    for rx_power in RX_TAP_MONITOR_LIST_HIGH:
        voa_rx.set_attr(il0 + rx_power_start - rx_power)  # 20220428之后的点直接调节voa
        time.sleep(1)
        # rc, result = set_rx_power(rx_power)
        # # rc, result = set_to_power(olp85, voa, rx_power)  # 调整接收光功率
        # if rc != 0:
        #     return 1, result
        rx_read = 0
        rx_pd_adc_high = 0
        average_times = 1
        for i in range(average_times):
            p_temp = get_rx_pwr()
            # p_temp = olp85.read_power()
            rc, result = BSP_ADC_REG(seq_num, "PIC_RXPDXY_HIGH")
            if rc != 0:
                return rc, result
            h_temp = result[1]
            rx_pd_adc_high += h_temp / average_times
            rx_read += p_temp / average_times
            time.sleep(0.1)
        s = 'Rx_pwr(dBm),adc_high: %.2f %d' % (rx_read, rx_pd_adc_high)
        print(s)
        my_sig.infoSignal.emit(-1,s)
        # write_data(s + '\n')
        if GC_MON_AVE_ADC_MIN < rx_pd_adc_high < GC_MON_AVE_ADC_MAX:
            RX_TAP_MONITOR_mW_HIGH.append(10 ** (rx_read / 10))
            RXPD_ADC_HIGH.append(rx_pd_adc_high)
    RX_PWR_HG_SLOPE, RX_PWR_HG_OFFSET = linear_fit(RX_TAP_MONITOR_mW_HIGH, RXPD_ADC_HIGH)

    RXPD_ADC_LOW = []
    RX_TAP_MONITOR_mW_LOW = []
    rx_power_start = RX_TAP_MONITOR_LIST_LOW[0]  # 20220428第一个点反馈调节
    rc, result = set_rx_power(rx_power_start)
    if rc != 0:
        return 1, result
    il0 = voa_rx.get_attr()
    for rx_power in RX_TAP_MONITOR_LIST_LOW:
        voa_rx.set_attr(il0 + rx_power_start - rx_power)  # 20220428之后的点直接调节voa
        time.sleep(1)
        # rc, result = set_rx_power(rx_power)  # 调整接收光功率
        # # rc, result = set_to_power(olp85, voa, rx_power)  # 调整接收光功率
        # if rc != 0:
        #     return 1, result
        rx_read = 0
        rx_pd_adc_low = 0
        average_times = 1
        for i in range(average_times):
            p_temp = get_rx_pwr()
            # p_temp = olp85.read_power()
            rc, result = BSP_ADC_REG(seq_num, "PIC_RXPDXY_LOW")
            if rc != 0:
                return rc, result
            l_temp = result[1]
            rx_pd_adc_low += l_temp / average_times
            rx_read += p_temp / average_times
            time.sleep(0.1)
        s = 'Rx_pwr(dBm),adc_low: %.2f %d' % (rx_read, rx_pd_adc_low)
        print(s)
        my_sig.infoSignal.emit(-1,s)
        # write_data(s + '\n')
        if GC_MON_AVE_ADC_MIN < rx_pd_adc_low < GC_MON_AVE_ADC_MAX:
            RX_TAP_MONITOR_mW_LOW.append(10 ** (rx_read / 10))
            RXPD_ADC_LOW.append(rx_pd_adc_low)
    RX_PWR_LG_SLOPE, RX_PWR_LG_OFFSET = linear_fit(RX_TAP_MONITOR_mW_LOW, RXPD_ADC_LOW)
    # rem_dic['hg_slope_r'] = RX_PWR_HG_SLOPE
    # rem_dic['hg_offset_r'] = RX_PWR_HG_OFFSET
    # rem_dic['lg_slope_r'] = RX_PWR_LG_SLOPE
    # rem_dic['lg_offset_r'] = RX_PWR_LG_OFFSET
    # rem_dic['RX_PWR_HG_SLOPE'] = RX_PWR_HG_SLOPE
    # rem_dic['RX_PWR_HG_OFFSET'] = RX_PWR_HG_OFFSET
    # rem_dic['RX_PWR_LG_SLOPE'] = RX_PWR_LG_SLOPE
    # rem_dic['RX_PWR_LG_OFFSET'] = RX_PWR_LG_OFFSET
    my_sig.infoSignal.emit(-1,'hg_slope_r, hg_offset_r, lg_slope_r, lg_offset_r: %s %s %s %s' % (
        RX_PWR_HG_SLOPE, RX_PWR_HG_OFFSET, RX_PWR_LG_SLOPE, RX_PWR_LG_OFFSET))
    return 0, (RX_PWR_HG_SLOPE, RX_PWR_HG_OFFSET, RX_PWR_LG_SLOPE, RX_PWR_LG_OFFSET)


def tx_shutter_cal():
    global rem_dic
    sleep_time = 1
    SHUTTER_CUR_REF = [0.05, 0.5, 1, 2, 4, 8, 14, 20, 30, 45]
    rem_dic['SHUTTER_CUR_REF'] = SHUTTER_CUR_REF
    ATT_COFF_LIST = []
    # TX_POW_REF = rem_dic['TX_POW_REF']
    TX_POW_REF = get_tx_pwr()
    for current in SHUTTER_CUR_REF:
        rc, reply = execute_mcm_cmd(seq_num, "set", "BSP_DAC_REG", [0, "TXSHUTTER_X", float2hex(current)])
        if rc != 0 or 'PASS' not in reply:
            return 1, 'set BSP_DAC_REG TXSHUTTER_X fail'
        rc, reply = execute_mcm_cmd(seq_num, "set", "BSP_DAC_REG", [0, "TXSHUTTER_Y", float2hex(current)])
        if rc != 0 or 'PASS' not in reply:
            return 1, 'set BSP_DAC_REG TXSHUTTER_Y fail'

        time.sleep(sleep_time)
        TX_POW = get_tx_pwr()  # 读TX功率
        ATT_COFF = TX_POW_REF - TX_POW
        ATT_COFF_LIST.append(ATT_COFF)
        print('Current, TX_pow, ATT_coff', current, TX_POW, ATT_COFF)
    return 0, ATT_COFF_LIST


def get_rx_mon_freq_coff():
    osw_oau_set_olp(1)
    DARK_CURRENT_LOW = 0
    RX_PD_LOG_NORM = []
    TX_PD_LOG_NORM = []

    def wait_powerup():
        while True:
            my_sig.infoSignal.emit(-1,'wait_powerup -15')
            pwr = get_tx_pwr()
            if pwr > -15:
                break
            time.sleep(5)

    rc, result = set_rx_power(5)  # Rx 输入设置为+5dBm
    if rc != 0:
        return 1, result
    for freq in FREQs:
        # write_data(' 频率：%.6f THz  ' % freq)
        rc, result = setfrequency(seq_num, freq)
        if rc != 0:
            return 1, result
        time.sleep(5)
        wait_powerup()
        pwr = get_rx_pwr()
        rx_pd_current_low = 0
        rx_pd_adc_low = 0
        bok = False
        for _ in range(5):  # 多读几次试试，有概率性读到0的问题，重试
            rc, result = BSP_ADC_REG(seq_num, "PIC_RXPDXY_LOW")
            if rc != 0:
                return rc, result
            rx_pd_current_low, rx_pd_adc_low = result
            if rx_pd_current_low != 0 and rx_pd_adc_low != 0:
                bok = True
                break
            time.sleep(1)
        if not bok:
            my_sig.infoSignal.emit(-1,'rx_pd_current_low or rx_pd_adc_low is 0')
        rx_pd_log_norm = 10 * math.log10(rx_pd_current_low - DARK_CURRENT_LOW) - 30 - pwr
        RX_PD_LOG_NORM.append(rx_pd_log_norm)
        s = 'freq rx_pd_log_norm rx_pd_current_low,rx_pd_adc_low, rx_pow: %.6f %s %s %s %s' % (
            freq, rx_pd_log_norm, rx_pd_current_low, rx_pd_adc_low, pwr)
        my_sig.infoSignal.emit(-1,s)

        if EDFAless is True:
            my_sig.infoSignal.emit(-1,' 频率：%.6f THz  ' % freq)
            # testMgr.set_freq(freq)
            # time.sleep(80)
            TX_POWER = get_tx_pwr()
            rc, result = BSP_ADC_REG(seq_num, "PIC_PMON_TXPDX")
            if rc != 0:
                return rc, result
            TXPDX, _ = result

            rc, result = BSP_ADC_REG(seq_num, "PIC_PMON_TXPDY")
            if rc != 0:
                return rc, result
            TXPDY, _ = result
            tx_pd_log_norm = 10 * math.log10((TXPDX + TXPDY) / 2) - 30 - TX_POWER
            TX_PD_LOG_NORM.append(tx_pd_log_norm)
            s = 'freq TXPDX, TXPDY,tx_pd_log_norm: %.6f %s %s %s' % (freq, TXPDX, TXPDY, tx_pd_log_norm)
            my_sig.infoSignal.emit(-1,s)

    delta_freq_list = [x - FREQs[2] for x in FREQs]
    a, b, c = quadratic_fit(delta_freq_list, RX_PD_LOG_NORM)
    my_sig.infoSignal.emit(-1,f'C1: {a}, C2: {b}, c3: {c}')
    # rc, TEMP = get_pic_temp(seq_num)
    # if rc != 0:
    #     return rc, TEMP
    rc, result = setfrequency(seq_num, FREQs[2])
    if rc != 0:
        return 1, result
    osw_oau_set_olp(0)

    if EDFAless is True:
        rc, info = execute_mcm_cmd(seq_num, 'eget', 'PIC_TXPD_RESP_X_COEFF', [0x0])
        if rc != 0 or 'PASS' not in info:
            return 1, 'eget PIC_TXPD_RESP_X_COEFF失败'
        TXPDXY_LOG = info.split('\n')[0].split(',')[4]
        TXPDXY_LOG = float(TXPDXY_LOG)
        rc, info = execute_mcm_cmd(seq_num, 'eget', 'PIC_TX_TRANS_XY_REF', [0x0])
        if rc != 0 or 'PASS' not in info:
            return 1, 'eget PIC_TX_TRANS_XY_REF失败'
        TRANS_LOG = info.split('\n')[0].split(',')[3]
        TRANS_LOG = float(TRANS_LOG)
        TXPD_REF = TXPDXY_LOG - TRANS_LOG
        my_sig.infoSignal.emit(-1,f'TXPD_REF:{TXPD_REF}')
        delta_freq_list = [x - FREQs[2] for x in FREQs]
        norm_list = [x - TXPD_REF for x in TX_PD_LOG_NORM]
        at, bt, ct = quadratic_fit(delta_freq_list, norm_list)
        my_sig.infoSignal.emit(-1,f'tx: at bt ct: {at}, {bt}, {ct}')
        return 0, (a, b, c, at, bt, ct)

    else:
        return 0, (a, b, c)


mode_state_dic = {0x100: "High-Power-Down", 0x0080: "TX Turn-off",
                  0x0040: "Fault", 0x0020: "Ready", 0x0010: "TX Turn-on",
                  0x0008: "TX-Off", 0x0004: "High-Power-Up", 0x0002: "Low-Power",
                  0x0001: "Initialize state"}


def check_mode_state(state: str, timeout):
    # 在timeout时间内查询状态是否为state
    state = int(state, 16)
    if '0x' in timeout.lower():
        timeout = int(timeout, 16)
    else:
        timeout = float(timeout)
    s_time = time.time()
    res_value = 0
    while time.time() - s_time < timeout:
        if get_exit_flag():
            # my_sig.sig_button_color.emit(testname, sheet_idx, STATE_STOP)
            raise stopException(f'测试手动停止')
        rc, res_value = read_one_reg_ex(seq_num, 0xb016)
        if rc == 0:
            if res_value in mode_state_dic:
                my_sig.infoSignal.emit(-1,"当前模块状态: " + mode_state_dic[res_value])
            if res_value == state:
                return 0, res_value
            time.sleep(5)
    return 1, f'在{timeout}s超时内查询模块状态都不是{hex(state)}，最后状态:{hex(res_value)}'


def check_avs_state(state: str, timeout):
    # 在timeout时间内查询AVS状态是否为state
    state = int(state, 16)
    if '0x' in timeout.lower():
        timeout = int(timeout, 16)
    else:
        timeout = float(timeout)
    s_time = time.time()
    res_value = 0
    read_state = -1
    while time.time() - s_time < timeout:
        if get_exit_flag():
            # my_sig.sig_button_color.emit(testname, sheet_idx, STATE_STOP)
            raise stopException(f'测试手动停止')
        rc, info = execute_mcm_cmd(seq_num, 'get', 'ADCDAC_SAR_AVS_TEST', [0x0])  # 读取AVS状态
        if rc != 0 or 'PASS' not in info:
            return 1, '读取AVS状态失败'
        if rc == 0:
            read_state = int(info.split('\n')[0].strip())
            if res_value in mode_state_dic:
                my_sig.infoSignal.emit(-1,"当前AVS状态: " + read_state)
            if read_state == state:
                return 0, read_state
            time.sleep(1)
    return 1, f'在{timeout}s超时内查询AVS状态都不是{state}，最后状态:{read_state}'


def check_rx_demode_lock(timeout):
    # 在timeout时间内查询rx_demode_lock状态是否为1
    state = 1
    if '0x' in timeout.lower():
        timeout = int(timeout, 16)
    else:
        timeout = float(timeout)
    s_time = time.time()
    res_value = -1
    while time.time() - s_time < timeout:
        if get_exit_flag():
            raise stopException(f'测试手动停止')
        rc, val = read_one_reg_retry(seq_num, 0x90C1)
        if rc == 0:
            res_value = bit_cutout_unsigned(val, 5, 5)
            if res_value == state:
                return 0, res_value
        time.sleep(1)
    return 1, f'在{timeout}s超时内查询rx_demode_lock状态都不是1，最后状态:{res_value}'


def dac_delay_cal():
    AVG_BER_initial = rem_dic['AVG_BER_initial']
    # if AVG_BER_initial < 1e-3:  # 待取消注释 # 待改为入参再验证？
    #     # if AVG_BER_initial < 1e-12:
    #     pass  # 初始BER满足要求就什么也不用做
    # else:
    reply = execute_mcm_cmd(seq_num, 'eget', 'MCM_DSP_EFUSE', [0x0])
    if reply[0] != 0 or 'PASS' not in reply[1]:
        return 1, 'eget MCM_DSP_EFUSE fail'
    MCM_DSP_EFUSE_initial = [int(x, 16) for x in reply[1].split('\n')[0].strip().split(',')]  # 读取上一行命令的结果
    MCM_DSP_EFUSE_new = MCM_DSP_EFUSE_initial.copy()
    MCM_DSP_EFUSE_new[7] = 0x1  # 把第八项的0xFFFFFFFF改成0x1
    if HDR_FLAG:
        rc, res_value = handleDiagSetGetCmd(seq_num, "eset", "MCM_DSP_EFUSE", [0] + MCM_DSP_EFUSE_new)
    else:
        rc, res_value = execute_mcm_cmd(seq_num, "eset", "MCM_DSP_EFUSE", [0] + MCM_DSP_EFUSE_new)
    if rc != 0 or 'PASS' not in res_value:
        return 1, "eset MCM_DSP_EFUSE fail"

    execute_set_reset_cmd(seq_num)  # 重置模块
    time.sleep(3)
    set_mode(0x4a, 0x0502)
    while True:
        rc, bok = check_rx_demode_lock('60')  # 等待模块收端锁定
        if rc == 0 and bok:
            break
        time.sleep(1)
    time.sleep(60)  # 收端锁定后再等60s
    rc, AVG_BER_NEW = read_lineside_ber(seq_num)  # 记录新BER
    if rc != 0:
        return 1, "get pre_BER fail"
    if AVG_BER_NEW < AVG_BER_initial * 0.7:
        pass  # 如果新BER比初始BER小，就结束了
    else:
        #  如果新BER没有比 初始BER小，就把 MCM_DSP_EFUSE改回初始值,然后重置模块
        if HDR_FLAG:
            rc, res_value = handleDiagSetGetCmd(seq_num, "eset", "MCM_DSP_EFUSE", [0] + MCM_DSP_EFUSE_initial)
        else:
            rc, res_value = execute_mcm_cmd(seq_num, "eset", "MCM_DSP_EFUSE", [0] + MCM_DSP_EFUSE_initial)
        if rc != 0 or 'PASS' not in res_value:
            return 1, "eset MCM_DSP_EFUSE fail"
        execute_set_reset_cmd(seq_num)
    return 0, ""


def sub_card_pwron():
    # 子卡供电
    rc, info = execute_mcm_cmd(seq_num, 'eget', 'MCM_MFG_SKU_REV', [0x0])  # 读取MCM版本
    if rc != 0 or 'PASS' not in info:
        return 1, '读取MCM版本失败'
    ver = info.split('\n')[0].strip()
    if ver == '01':  # 仅GL1模块需要进行子卡供电
        # set	ASIC_REG	0x0	0xff00002c	0x3490500
        rc, info = execute_mcm_cmd(seq_num, 'set', 'ASIC_REG', [0x0, 0xff00002c, 0x3490500])  # 子卡供电
        if rc != 0 or 'PASS' not in info:
            return 1, '子卡供电失败'
    return 0, ''


def get_mv():
    # 读取模块的3V3_IN MV
    mv_sum = 0
    mv_max = float("-inf")
    mv_min = float("inf")
    for _ in range(5):  # 读5次，去掉最大最小值后取平均
        rc, info = execute_mcm_cmd(seq_num, 'get', 'BSP_ADC_REG', [0x0, 'P3P3V_VIN'])
        if rc != 0 or 'PASS' not in info:
            return 1, '读取模块的3V3_IN MV失败'
        volt = float(info.split(',')[0].strip())
        mv_sum += volt
        if volt > mv_max:
            mv_max = volt
        if volt < mv_min:
            mv_min = volt
    mv_aver = (mv_sum - mv_max - mv_min) / 3
    return 0, mv_aver


def set_temprature(conditon):
    my_sig.infoSignal.emit(-1,f'set_temprature: {conditon}')
    temp_range = Temp[conditon]
    if not chamber:
        msgbox('请设置温度到：%s ~ %s' % (temp_range[0], temp_range[1]))
    else:
        chamber_Temp_tar = chamber_Temp[conditon]
        set_chamber_temp(chamber_Temp_tar, False)
    return 0, temp_range[0]


def wait_temprature(conditon):
    my_sig.infoSignal.emit(-1,f'wait_temprature: {conditon}')
    if not chamber:
        return wait_temprature_manual(conditon)
    else:
        return wait_temprature_auto(conditon)


def sleep_check_stop(sleep_sec):
    # 延时（单位秒），长时间延时过程中不断检测是否手动停止测试
    t0 = time.time()
    while True:
        if time.time() - t0 > sleep_sec:
            break
        if get_exit_flag():
            raise Exception('测试手动停止')
        time.sleep(5)


def set_chamber_temp(temprature, bWait = True):
    my_sig.infoSignal.emit(-1,f'设置温循箱目标温度：{temprature}')
    chamber.SetTemp(temprature)
    chamber.OnOff(1)
    if not bWait:
        return
    # 等待温箱温度到达目标温度
    while True:
        if get_exit_flag():
            raise Exception('测试手动停止')
        readChamberTemp = chamber.GetTemp()[0]
        # my_sig.infoSignal.emit(-1,f'温循箱实时温度：{readChamberTemp}')
        if abs(readChamberTemp - temprature) <= 0.5:
            break
        else:
            time.sleep(10)


def wait_temprature_auto(conditon):
    # 设置温箱经验值温度
    temp_range = Temp[conditon]
    chamber_Temp_tar = chamber_Temp[conditon]
    set_chamber_temp(chamber_Temp_tar)
    while True:
        if get_exit_flag():
            raise Exception('测试手动停止')
        rc, temperatrue = get_pic_temp(seq_num)
        if rc != 0:
            return rc, temperatrue
        if temp_range[0] < temperatrue < temp_range[1]:  # 判断温度是否在范围内
            my_sig.infoSignal.emit(-1,'PIC温度为%.2f ℃' % temperatrue)
            time.sleep(temp_wait_time)
            rc, temperatrue1 = get_pic_temp(seq_num)
            if rc != 0:
                return rc, temperatrue1
            if abs(temperatrue1 - temperatrue) < temp_stable_th:  # 判断温度是否稳定
                my_sig.infoSignal.emit(-1,'PIC温度%ss内变化小于%s, 已经稳定' % (temp_wait_time, temp_stable_th))
                temperatrue = temperatrue1
                break
            else:
                my_sig.infoSignal.emit(-1,'%ss内温度 %.2f ℃->%.2f ℃,等待温度稳定！' % (temp_wait_time, temperatrue, temperatrue1))
    return 0, temperatrue


def wait_temprature_manual(conditon):
    temp_range = Temp[conditon]

    msgbox('请设置温度到：%s ~ %s' % (temp_range[0], temp_range[1]))
    # my_sig.infoSignal.emit(-1,'请设置温度到：%s ~ %s' % (temp_range[0], temp_range[1]))
    while True:
        rc, temperatrue = get_pic_temp(seq_num)
        if rc != 0:
            return rc, temperatrue
        if temp_range[0] < temperatrue < temp_range[1]:  # 判断温度是否在范围内
            my_sig.infoSignal.emit(-1,'PIC温度为%.2f ℃' % temperatrue)
            time.sleep(temp_wait_time)
            rc, temperatrue1 = get_pic_temp(seq_num)
            if rc != 0:
                return rc, temperatrue
            if abs(temperatrue1 - temperatrue) < temp_stable_th:  # 判断温度是否稳定
                my_sig.infoSignal.emit(-1,'PIC温度%ss内变化小于%s, 已经稳定' % (temp_wait_time, temp_stable_th))
                break
            else:
                my_sig.infoSignal.emit(-1,'%ss内温度 %.2f ℃->%.2f ℃,等待温度稳定！' % (temp_wait_time, temperatrue, temperatrue1))
        else:
            if temp_range[0] >= temperatrue:
                my_sig.infoSignal.emit(-1,'PIC温度为%.2f ℃, 太低，' % temperatrue)
            if temp_range[1] <= temperatrue:
                my_sig.infoSignal.emit(-1,'PIC温度为%.2f ℃, 太高' % temperatrue)
            response = msgbox_ques('继续等待?')
            if response:
                # time.sleep(5)
                pass
            else:
                break
    msgbox('请确认温度已稳定！')
    rc, temp = get_pic_temp(seq_num)
    if rc == 0:
        temperatrue = temp
    return 0, temperatrue


def rx_chn_pwr_cal():
    # global olp85
    # global voa_rx
    RX_TAP_MONITOR = []
    GC_MON_AVE_ADC = []
    Data_Useful = []
    # rem_dic['BAUD_RATE_REF'] = 60.13855  #放在excel里配置了
    inpower_start = RX_CHANNEL_MONITOR[0]  # 20220428第一个点反馈调节
    rc, inpower_set = set_rx_power(inpower_start)
    if rc != 0:
        return 1, inpower_set
    inpower_pre = float('inf')
    il0 = voa_rx.get_attr()
    for inpower in RX_CHANNEL_MONITOR:
        # print('调整接收光功率到%s' % inpower)
        voa_rx.set_attr(il0 + inpower_start - inpower)  # 20220428之后的点直接调节voa
        time.sleep(1)
        inpower_set = get_rx_pwr()
        if inpower_set - inpower_pre > -0.3:  # 功率变化大于于-0.3的点不要
            my_sig.infoSignal.emit(-1,f'当前功率:{inpower_set} dBm, 上一个点功率: {inpower_pre}, 变化量>-0.3!')
            continue
        inpower_pre = inpower_set
        # rc, inpower_set = set_rx_power(inpower)
        # # rc, inpower_set = set_to_power(olp85, voa, inpower)
        # if rc != 0:
        #     return 1, inpower_set
        rc, result = PICP_AVG_GC_MON(seq_num)
        if rc != 0:
            return 1, result
        TIAs = result[0]
        gc_mon_adc_ave = sum(TIAs) / len(TIAs)
        saturated_num = 0
        for tia in TIAs:
            if tia >= GC_MON_AVE_ADC_MAX:
                saturated_num += 1
        if gc_mon_adc_ave <= GC_MON_AVE_ADC_MIN:
            pass
        elif saturated_num < 3:
            Data_Useful.append(True)
            RX_TAP_MONITOR.append(inpower_set)
            GC_MON_AVE_ADC.append(gc_mon_adc_ave)
            my_sig.infoSignal.emit(-1,'INPOWER:%s dBm; GC_MON_AVE_ADC:%d' % (inpower_set, gc_mon_adc_ave))
        else:
            Data_Useful.append(False)
            break

    # 确保RX_TAP_MONITOR为降序排列
    my_sig.infoSignal.emit(-1,f'RX_TAP_MONITOR: {RX_TAP_MONITOR}')
    my_sig.infoSignal.emit(-1,f'GC_MON_AVE_ADC: {GC_MON_AVE_ADC}')

    for i in range(40):
        rc, reply = execute_mcm_cmd(seq_num, "eset", "PICP_RX_CH_PWR_CAL", [0, i, float2hex(float('nan')), 65535])
        if rc != 0 or 'PASS' not in reply:
            return 1, 'eset PICP_RX_CH_PWR_CAL fail'

    for i in range(len(RX_TAP_MONITOR)):
        rc, reply = execute_mcm_cmd(seq_num, 'eset', 'PICP_RX_CH_PWR_CAL',
                                    [0, i, float2hex(RX_TAP_MONITOR[i]), int(GC_MON_AVE_ADC[i])])
        if rc != 0 or 'PASS' not in reply:
            return 1, 'eset PICP_RX_CH_PWR_CAL fail'
    return 0, ''


def rx_voa_cal():
    global rem_dic
    RXVOA_CUR_REF = [0.5, 1, 2, 4, 8, 14, 18, 24, 36, 44]
    rem_dic['RXVOA_CUR_REF'] = RXVOA_CUR_REF
    sleep_time = 1
    RX_POWER_BASE = float(rem_dic['RX_POWER_BASE'])
    RX_POWER = []
    ATT_COEFF = []
    for voa_il in RXVOA_CUR_REF:
        rc, reply = execute_mcm_cmd(seq_num, "set", "BSP_DAC_REG", [0, "RXVOA_X", float2hex(voa_il)])
        if rc != 0 or 'PASS' not in reply:
            return 1, 'set BSP_DAC_REG RXVOA_X fail'
        rc, reply = execute_mcm_cmd(seq_num, "set", "BSP_DAC_REG", [0, "RXVOA_Y", float2hex(voa_il)])
        if rc != 0 or 'PASS' not in reply:
            return 1, 'set BSP_DAC_REG RXVOA_Y fail'

        time.sleep(sleep_time)
        rc, result = PICP_AVG_GC_MON(seq_num)
        if rc != 0:
            return 1, result
        rx_pow = result[1]  # 获取通道功率
        RX_POWER.append(rx_pow)
        att_coe = RX_POWER_BASE - rx_pow
        ATT_COEFF.append(att_coe)
        my_sig.infoSignal.emit(-1,'VOA_current:%s , Rx_power:%s , Att_coeff:%s' % (voa_il, rx_pow, att_coe))
    rem_dic['ATT_COEFF'] = ATT_COEFF
    my_sig.infoSignal.emit(-1,f'ATT_COEFF: {ATT_COEFF}')  # 检查RX_POWER是否单调，否则退出 ？？这里是什么意思
    return 0, RX_POWER


def set_ATT_COEFF():
    # 设置VOA衰减系数
    global rem_dic
    ATT_COEFF = rem_dic['ATT_COEFF']
    if HDR_FLAG:
        rc, res_value = handleDiagSetGetCmd(seq_num, "eset", "PICX_RXVOA_ATT_REF", generate_hex_param(*ATT_COEFF))
    else:
        rc, res_value = execute_mcm_cmd(seq_num, "eset", "PICX_RXVOA_ATT_REF", generate_hex_param(*ATT_COEFF))
    if rc != 0 or 'PASS' not in res_value:
        return 1, "eset PICX_RXVOA_ATT_REF fail"
    return 0, f'HDR: eset PICX_RXVOA_ATT_REF: {ATT_COEFF}'


def set_RXVOA_CUR_REF():
    # 设置VOA参考电流
    RXVOA_CUR_REF = rem_dic['RXVOA_CUR_REF']
    if HDR_FLAG:
        rc, res_value = handleDiagSetGetCmd(seq_num, "eset", "PICX_RXVOA_CUR_REF", generate_hex_param(*RXVOA_CUR_REF))
    else:
        rc, res_value = execute_mcm_cmd(seq_num, "eset", "PICX_RXVOA_CUR_REF", generate_hex_param(*RXVOA_CUR_REF))
    if rc != 0 or 'PASS' not in res_value:
        return 1, "eset PICX_RXVOA_CUR_REF fail"
    return 0, f'HDR: eset PICX_RXVOA_CUR_REF: {RXVOA_CUR_REF}'


def RX_PDL_Ratio():
    average_times = 10
    RX_PDL_Ratio_list = []
    for i in range(average_times):  # 读取10次 RX_PDL_Ratio, 取平均
        rc, result = PICP_RX_OPT_PWR_RATIO(seq_num)
        if rc != 0:
            return rc, result
        RXPDL_Ratio_woDiv = result[0]
        RX_PDL_Ratio_list.append(RXPDL_Ratio_woDiv)
        time.sleep(0.1)
    RX_PDL_Ratio = sum(RX_PDL_Ratio_list) / average_times
    my_sig.infoSignal.emit(-1,'RX_PDL_Ratio: %s' % RX_PDL_Ratio)
    return 0, RX_PDL_Ratio


def RX_HP_MW():
    return 0, 10 ** (float(rem_dic['RX_HP_dB']) / 10)


def RFPD_SLOPE():
    # RFPD_SLOPE = (RFPD_HP - RFPD_OFFSET) / RX_HP_MW
    return 0, (float(rem_dic['RFPD_HP']) - float(rem_dic['RFPD_OFFSET'])) / float(rem_dic['RX_HP_MW'])


def pic_cal():  # load_temperature_data():
    global rem_dic
    hg_offset_r, lg_offset_r, C1_r, C2_r, C3_r, T_r = pickle.load(open(logdir + '\\room.pkl', 'rb'))
    my_sig.infoSignal.emit(-1,
        f'hg_offset_r: {hg_offset_r}, lg_offset_r: {lg_offset_r}, C1_r: {C1_r}, C2_r: {C2_r}, C3_r: {C3_r}, T_r: {T_r}')
    hg_offset_c, lg_offset_c, C1_c, C2_c, C3_c, T_c = pickle.load(open(logdir + '\\cold.pkl', 'rb'))
    my_sig.infoSignal.emit(-1,
        f'hg_offset_c: {hg_offset_c}, lg_offset_c: {lg_offset_c}, C1_c: {C1_c}, C2_c: {C2_c}, C3_c: {C3_c}, T_c: {T_c}')
    hg_offset_h, lg_offset_h, C1_h, C2_h, C3_h, T_h = pickle.load(open(logdir + '\\hot.pkl', 'rb'))
    my_sig.infoSignal.emit(-1,
        f'hg_offset_h: {hg_offset_h}, lg_offset_h: {lg_offset_h}, C1_h: {C1_h}, C2_h: {C2_h}, C3_h: {C3_h}, T_h: {T_h}')
    T_c, T_r, T_h = float(T_c), float(T_r), float(T_h)  # 待修改，后面取消
    rem_dic['T_c'] = T_c
    rem_dic['T_r'] = T_r
    rem_dic['T_h'] = T_h
    '''计算高低温下暗电流系数'''
    OFFSET_HI_V_NORM = [hg_offset_c / hg_offset_r, 1, hg_offset_h / hg_offset_r]
    OFFSET_LO_V_NORM = [lg_offset_c / lg_offset_r, 1, lg_offset_h / lg_offset_r]
    TEMP_DELTA_V = [T_c - T_r, 0, T_h - T_r]
    HI_2, HI_1, HI_0 = quadratic_fit(TEMP_DELTA_V, OFFSET_HI_V_NORM)
    LO_2, LO_1, LO_0 = quadratic_fit(TEMP_DELTA_V, OFFSET_LO_V_NORM)
    rem_dic['HI_2'] = HI_2
    rem_dic['HI_1'] = HI_1
    rem_dic['HI_0'] = HI_0
    rem_dic['LO_2'] = LO_2
    rem_dic['LO_1'] = LO_1
    rem_dic['LO_0'] = LO_0
    my_sig.infoSignal.emit(-1,'HI_2, HI_1, HI_0,LO_2, LO_1, LO_0: %s %s %s %s %s %s' % (HI_2, HI_1, HI_0, LO_2, LO_1, LO_0))

    if EDFAless is True:
        a0, b0, c0, T0 = pickle.load(open(logdir + '\\cold_TX.pkl', 'rb'))
        my_sig.infoSignal.emit(-1,f'a0: {a0}, b0: {b0}, c0: {c0}, T0: {T0}')
        a1, b1, c1, T1 = pickle.load(open(logdir + '\\room_TX.pkl', 'rb'))
        my_sig.infoSignal.emit(-1,f'a1: {a1}, b1: {b1}, c1: {c1}, T1: {T1}')
        a2, b2, c2, T2 = pickle.load(open(logdir + '\\hot_TX.pkl', 'rb'))
        my_sig.infoSignal.emit(-1,f'a2: {a2}, b2: {b2}, c2: {c2}, T2: {T2}')

        rem_dic['T0'] = float(T0)
        rem_dic['T1'] = float(T1)
        rem_dic['T2'] = float(T2)
        rem_dic['a0'] = float(a0)
        rem_dic['a1'] = float(a1)
        rem_dic['a2'] = float(a2)
        rem_dic['b0'] = float(b0)
        rem_dic['b1'] = float(b1)
        rem_dic['b2'] = float(b2)
        rem_dic['c0'] = float(c0)
        rem_dic['c1'] = float(c1)
        rem_dic['c2'] = float(c2)
    return 0, (HI_2, HI_1, HI_0, LO_2, LO_1, LO_0)


def avs_cal():
    cal_time_limit = 90
    t0 = time.time()
    time.sleep(10)
    rc, reply = AVS_CALIBRATE(seq_num)
    if rc != 0:
        return 1, reply
    if reply == 0:
        msgbox('AVS校准暂未运行')
    while reply == 1 or reply == 2:
        rc, reply = AVS_CALIBRATE(seq_num)
        if rc != 0:
            return 1, reply
        if reply == 1:
            my_sig.infoSignal.emit(-1,'AVS正在进行校准')
        elif reply == 2:
            my_sig.infoSignal.emit(-1,'AVS正在采集校准数据')
        t1 = time.time()
        if t1 - t0 > cal_time_limit:
            my_sig.infoSignal.emit(-1,'AVS校准超时，失败！')
            return 1, 'AVS校准超时，失败！'
        time.sleep(5)
    if reply == 4:
        my_sig.infoSignal.emit(-1,'AVS校准失败')
        return 1, 'AVS校准失败'
    if reply == 3:
        return 0, 'AVS校准成功'


def save_one_sheet(wb, sheet_name, fpath):
    # 保存excel的一个sheet到文件fpath
    wb_save = copy.deepcopy(wb)
    wb_save._Workbook__worksheets = [worksheet for worksheet in wb._Workbook__worksheets if
                                     worksheet.name == sheet_name]
    wb_save.save(fpath)


def voa_create(addr: str, param: str):
    sobj = re.search('^COM\d+', addr, re.IGNORECASE)  # addr是否为串口
    if sobj:
        return VOA(addr, int(param))
    else:
        return VOA_board(addr, int(param, 16))


def connect_dev_cal(params):
    global olp85
    global osw_sop
    global osw_ase
    global osw_tr
    global osw_oau
    global voa_rx
    global voa_ase
    global chamber
    try:
        my_sig.sig_enable_ui.emit(False)
        if "opm_ip" in params:
            my_sig.infoSignal.emit(-1,f'connect opm...')
            opm_ip = params["opm_ip"]
            my_sig.infoSignal.emit(-1,f'connect opm: {opm_ip} ...')
            olp85 = OPM(ip=opm_ip)
        if "voa_rx_addr" in params:
            if voa_rx:  # 断开重连，不然COM口占用状态连不上
                voa_rx.close()
            my_sig.infoSignal.emit(-1,f'connect voa_rx...')
            voa_rx_addr = params["voa_rx_addr"]
            voa_rx_baud = params["voa_rx_baud"]
            my_sig.infoSignal.emit(-1,f"open voa_rx port: {voa_rx_addr}, baud: {voa_rx_baud}")
            voa_rx = voa_create(voa_rx_addr, voa_rx_baud)
            voa_rx.set_attr(0)
        if "voa_ase_ip" in params:
            if voa_ase:
                voa_ase.close()
            my_sig.infoSignal.emit(-1,f'connect voa_ase...')
            voa_ase_ip = params["voa_ase_ip"]
            voa_ase_sn = params["voa_ase_sn"]
            my_sig.infoSignal.emit(-1,f"open voa_ase ip: {voa_ase_ip}, sn: {voa_ase_sn}")
            voa_ase = voa_create(voa_ase_ip, voa_ase_sn)
            voa_ase.set_attr(30)
        if "osw_sop_ip" in params:
            my_sig.infoSignal.emit(-1,f'connect osw_sop...')
            osw_sop_ip = params["osw_sop_ip"]
            osw_sop_sn = params["osw_sop_sn"]
            my_sig.infoSignal.emit(-1,f"open osw_sop ip: {osw_sop_ip}, sn: {osw_sop_sn}")
            osw_sop = OLPB_Board(osw_sop_ip, osw_sop_sn)
            osw_sop.set_olp(0)
        if "osw_ase_ip" in params:
            my_sig.infoSignal.emit(-1,f'connect osw_ase...')
            osw_ase_ip = params["osw_ase_ip"]
            osw_ase_sn = params["osw_ase_sn"]
            my_sig.infoSignal.emit(-1,f"open osw_ase ip: {osw_ase_ip}, sn: {osw_ase_sn}")
            osw_ase = OLPB_Board(osw_ase_ip, osw_ase_sn)
            osw_ase.set_olp(0)
        if "osw_tr_ip" in params:
            my_sig.infoSignal.emit(-1,f'connect osw_tr...')
            osw_tr_ip = params["osw_tr_ip"]
            osw_tr_sn = params["osw_tr_sn"]
            my_sig.infoSignal.emit(-1,f"open osw_tr ip: {osw_tr_ip}, sn: {osw_tr_sn}")
            osw_tr = OLPB_Board(osw_tr_ip, osw_tr_sn)
            osw_tr.set_olp(0)
        if "osw_oau_ip" in params:
            my_sig.infoSignal.emit(-1,f'connect osw_oau...')
            osw_oau_ip = params["osw_oau_ip"]
            osw_oau_sn = params["osw_oau_sn"]
            my_sig.infoSignal.emit(-1,f"open osw_oau ip: {osw_oau_ip}, sn: {osw_oau_sn}")
            osw_oau = OLPB_Board(osw_oau_ip, osw_oau_sn)
            osw_oau.set_olp(1)
        if "box_addr" in params:
            if chamber:   # 断开重连，不然COM口占用状态连不上
                chamber.close()
            my_sig.infoSignal.emit(-1,f'connect chamber...')
            box_addr = params["box_addr"]
            my_sig.infoSignal.emit(-1,f'connect chamber: {box_addr} ...')
            chamber = GWS(box_addr)
        else:
            if chamber:
                chamber.close()
            chamber = None
    except Exception as e:
        my_sig.sig_update_state.emit(f"连接仪表失败: " + str(e), STATE_FAIL)
        raise
    finally:
        my_sig.sig_enable_ui.emit(True)


def connect_dev_precal(params):
    global olp85
    try:
        my_sig.sig_enable_ui.emit(False)
        if "opm_ip" in params:
            my_sig.infoSignal.emit(-1,f'connect opm...')
            opm_ip = params["opm_ip"]
            my_sig.infoSignal.emit(-1,f'connect opm: {opm_ip} ...')
            olp85 = OPM(ip=opm_ip)
    except Exception as e:
        my_sig.sig_update_state.emit(f"连接仪表失败: " + str(e), STATE_FAIL)
        raise
    finally:
        my_sig.sig_enable_ui.emit(True)


def set_precal_network(params):
    global osw_sop
    global osw_ase
    global osw_tr
    global osw_oau
    global voa_rx
    if "osw_sop_ip" in params and not osw_sop:
        my_sig.infoSignal.emit(-1,f'connect osw_sop...')
        osw_sop_ip = params["osw_sop_ip"]
        osw_sop_sn = params["osw_sop_sn"]
        my_sig.infoSignal.emit(-1,f"open osw_sop ip: {osw_sop_ip}, sn: {osw_sop_sn}")
        osw_sop = OLPB_Board(osw_sop_ip, osw_sop_sn)
        # osw_sop.set_olp(0)
    if "osw_ase_ip" in params and not osw_ase:
        my_sig.infoSignal.emit(-1,f'connect osw_ase...')
        osw_ase_ip = params["osw_ase_ip"]
        osw_ase_sn = params["osw_ase_sn"]
        my_sig.infoSignal.emit(-1,f"open osw_ase ip: {osw_ase_ip}, sn: {osw_ase_sn}")
        osw_ase = OLPB_Board(osw_ase_ip, osw_ase_sn)
        # osw_ase.set_olp(0)
    if "osw_tr_ip" in params and not osw_tr:
        my_sig.infoSignal.emit(-1,f'connect osw_tr...')
        osw_tr_ip = params["osw_tr_ip"]
        osw_tr_sn = params["osw_tr_sn"]
        my_sig.infoSignal.emit(-1,f"open osw_tr ip: {osw_tr_ip}, sn: {osw_tr_sn}")
        osw_tr = OLPB_Board(osw_tr_ip, osw_tr_sn)
        # osw_tr.set_olp(0)
    if "osw_oau_ip" in params and not osw_oau:
        my_sig.infoSignal.emit(-1,f'connect osw_oau...')
        osw_oau_ip = params["osw_oau_ip"]
        osw_oau_sn = params["osw_oau_sn"]
        my_sig.infoSignal.emit(-1,f"open osw_oau ip: {osw_oau_ip}, sn: {osw_oau_sn}")
        osw_oau = OLPB_Board(osw_oau_ip, osw_oau_sn)
        # osw_oau.set_olp(1)
    if "voa_rx_addr" in params and not voa_rx:
        my_sig.infoSignal.emit(-1,f'connect voa_rx...')
        voa_rx_addr = params["voa_rx_addr"]
        voa_rx_baud = params["voa_rx_baud"]
        my_sig.infoSignal.emit(-1,f"open voa_rx port: {voa_rx_addr}, baud: {voa_rx_baud}")
        # voa_rx = VOA(voa_rx_addr, voa_rx_baud)
        voa_rx = voa_create(voa_rx_addr, voa_rx_baud)
    voa_rx.set_attr(14)
    osw_sop.set_olp(0)
    osw_ase.set_olp(0)
    osw_tr.set_olp(0)
    osw_oau.set_olp(1)


def test_by_cfg(dco_seq, wb_ori, wb_re, sheet_idx, testname, module_sn, save_alone=True, params={}):
    # 根据excel配置执行测试，wb_ori：原始配置的workbook，wb_re：复制的workbook，sheet_idx：要测试的sheet号，testname：工序名
    # save_alone：该测试项是否单独保存excel结果，params：入参
    # 返回：True表示测试成功，False表示测试失败
    global olp85
    # global voa
    global voa_rx
    global voa_ase
    global osw_sop
    global osw_ase
    global osw_tr
    global osw_oau
    global chamber
    global tx_pwr_offset
    global rx_pwr_offset
    global logdir
    global seq_num
    my_sig.sig_enable_ui.emit(False)
    s_time = time.time()
    start_time = time.strftime("%Y%m%d%H%M%S", time.localtime())[2:]
    my_sig.infoSignal.emit(-1,f'{testname} start...')
    # exitFlag = False
    itemname = ''
    test_des = ''
    is_pass_total = ''
    res_flag = ""
    try:
        globals()['seq_num'] = dco_seq
        if not wb_ori:
            raise Exception(f'请确认excel配置文件是否存在')
        itemname = wb_ori.sheet_names()[sheet_idx]
        my_sig.sig_button_color.emit(testname, sheet_idx, STATE_START)
        # my_sig.sig_update_state.emit(STATE_START)
        if "result_dir" in params:
            logdir = params["result_dir"]
        else:
            logdir = os.path.join(logdir_pa, module_sn)
        my_sig.infoSignal.emit(-1,f'logdir: {logdir}')
        sheet_ori = wb_ori.sheet_by_index(sheet_idx)
        # itemname = wb_ori.sheet_names()[sheet_idx]
        my_sig.infoSignal.emit(-1,f'{itemname} start...')
        sheet_re = wb_re.get_sheet(sheet_idx)

        config = init_data()
        for ele in params:
            rem_dic[ele] = params[ele]
        if "tx_pwr_offset" in params:
            tx_pwr_offset = params["tx_pwr_offset"]
            print(f'tx_pwr_offset: {tx_pwr_offset}')
        if "rx_pwr_offset" in params:
            rx_pwr_offset = params["rx_pwr_offset"]
            print(f'rx_pwr_offset: {rx_pwr_offset}')
        opm_ip = ""
        if "opm_ip" in params:
            opm_ip = params["opm_ip"]
        if 'RFPD_LOSS_OBS' not in rem_dic:
            if config.has_option('property', 'rfpd_loss_obs'):
                rfpd_loss_obs = config.get('property', 'rfpd_loss_obs')
                rem_dic['RFPD_LOSS_OBS'] = rfpd_loss_obs
                my_sig.infoSignal.emit(-1,f'RFPD_LOSS_OBS: {rfpd_loss_obs}')
        totoal_res = True
        if "opm_ip" in params and not olp85:
            my_sig.infoSignal.emit(-1,f'connect opm: {opm_ip} ...')
            olp85 = OPM(ip=opm_ip)
        if "voa_rx_addr" in params and not voa_rx:
            my_sig.infoSignal.emit(-1,f'connect voa_rx...')
            voa_rx_addr = params["voa_rx_addr"]
            voa_rx_baud = params["voa_rx_baud"]
            my_sig.infoSignal.emit(-1,f"open voa_rx port: {voa_rx_addr}, baud: {voa_rx_baud}")
            # voa_rx = VOA(voa_rx_addr, voa_rx_baud)
            voa_rx = voa_create(voa_rx_addr, voa_rx_baud)
            voa_rx.set_attr(0)
        if "voa_ase_ip" in params and not voa_ase:
            my_sig.infoSignal.emit(-1,f'connect voa_ase...')
            voa_ase_ip = params["voa_ase_ip"]
            voa_ase_sn = params["voa_ase_sn"]
            my_sig.infoSignal.emit(-1,f"open voa_ase ip: {voa_ase_ip}, sn: {voa_ase_sn}")
            # voa_ase = VOA_board(voa_ase_ip, voa_ase_sn)
            voa_ase = voa_create(voa_ase_ip, voa_ase_sn)
            # voa_ase.set_attr(voa_ase.il_max)
            voa_ase.set_attr(30)
        if "osw_sop_ip" in params and not osw_sop:
            my_sig.infoSignal.emit(-1,f'connect osw_sop...')
            osw_sop_ip = params["osw_sop_ip"]
            osw_sop_sn = params["osw_sop_sn"]
            my_sig.infoSignal.emit(-1,f"open osw_sop ip: {osw_sop_ip}, sn: {osw_sop_sn}")
            osw_sop = OLPB_Board(osw_sop_ip, osw_sop_sn)
            osw_sop.set_olp(0)
        if "osw_ase_ip" in params and not osw_ase:
            my_sig.infoSignal.emit(-1,f'connect osw_ase...')
            osw_ase_ip = params["osw_ase_ip"]
            osw_ase_sn = params["osw_ase_sn"]
            my_sig.infoSignal.emit(-1,f"open osw_ase ip: {osw_ase_ip}, sn: {osw_ase_sn}")
            osw_ase = OLPB_Board(osw_ase_ip, osw_ase_sn)
            osw_ase.set_olp(0)
        if "osw_tr_ip" in params and not osw_tr:
            my_sig.infoSignal.emit(-1,f'connect osw_tr...')
            osw_tr_ip = params["osw_tr_ip"]
            osw_tr_sn = params["osw_tr_sn"]
            my_sig.infoSignal.emit(-1,f"open osw_tr ip: {osw_tr_ip}, sn: {osw_tr_sn}")
            osw_tr = OLPB_Board(osw_tr_ip, osw_tr_sn)
            osw_tr.set_olp(0)
        if "osw_oau_ip" in params and not osw_oau:
            my_sig.infoSignal.emit(-1,f'connect osw_oau...')
            osw_oau_ip = params["osw_oau_ip"]
            osw_oau_sn = params["osw_oau_sn"]
            my_sig.infoSignal.emit(-1,f"open osw_oau ip: {osw_oau_ip}, sn: {osw_oau_sn}")
            osw_oau = OLPB_Board(osw_oau_ip, osw_oau_sn)
            osw_oau.set_olp(1)
        if "box_addr" in params:  # 温循箱
            if not chamber:
                my_sig.infoSignal.emit(-1,f'connect chamber...')
                box_addr = params["box_addr"]
                my_sig.infoSignal.emit(-1,f'connect chamber: {box_addr} ...')
                chamber = GWS(box_addr)
        else:
            if chamber:
                chamber.close()
            chamber = None

        rcnt = sheet_ori.nrows
        ccnt = sheet_ori.ncols
        # 下标
        iexp, iextract, imethod, ifailstop, irem, itype, iact, ikey, ival = get_cfg_col_idx(sheet_ori, ccnt)
        sheet_re.write(0, ccnt + 1, '是否通过')
        sheet_re.write(0, ccnt + 2, '返回值')
        sheet_re.write(0, ccnt + 3, '截取值')
        sheet_re.write(0, ccnt + 4, '计算值')
        for r in range(1, rcnt):
            if get_exit_flag():
                # my_sig.sig_button_color.emit(testname, sheet_idx, STATE_STOP)
                raise stopException(f'{itemname} 测试手动停止')
                # raise Exception(f'{itemname} 测试手动停止')
            cmd_type = val_to_str(sheet_ori.cell(r, itype).value)
            if not cmd_type:  # "command_type"为空的跳过
                continue
            exp, extract, method, stop_on_fail, rem_name, action, key, val, test_des = get_param(sheet_ori, r, iexp,
                                                                                                 iextract, imethod,
                                                                                                 ifailstop, irem, iact,
                                                                                                 ikey, ival)
            is_pass = FAIL
            res_value = None
            str_valst = []
            mcm_valst = []
            for i in range(ival, ccnt):
                strval = val_to_str(sheet_ori.cell(r, i).value)
                if not strval:
                    break
                rem_val = None
                sobj = re.search(r'^<(\S+)>$', strval)  # 尖括号的替换为变量值
                if sobj:
                    rem_val = rem_dic[sobj.group(1)]
                    strval = val_to_str(rem_val)
                str_valst.append(strval)
                if cmd_type == "mcm" or cmd_type == "hdr":
                    if isinstance(rem_val, list) or isinstance(rem_val, tuple):
                        for vv in rem_val:
                            mcm_valst.append(str2mcmval(val_to_str(vv)))
                    else:
                        mcm_valst.append(str2mcmval(strval))
            my_sig.infoSignal.emit(-1,
                f'Row[{r}]，cmd_type: [{cmd_type}], action: [{action}], key: [{key}], value: {str_valst}, expect: [{exp}], extract: [{extract}], method: [{method}]')

            if cmd_type == "mcm" or ((not HDR_FLAG) and cmd_type == "hdr"):
                rc = 1
                res_value = ''
                for i in range(3):  # 3次重试
                    if action.lower() == 'eset':
                        sheet_re.write(r, ccnt + 5, f'{action} {key} {str_valst}')
                    rc, res_value = test_mcm_cmd(seq_num, action, key, mcm_valst, sheet_re, r, ccnt, extract, method,
                                                 exp)
                    if rc == 0:
                        break
                    time.sleep(1)
                is_pass = PASS if rc == 0 else FAIL
            elif cmd_type == "register":
                if action == 'set':
                    addr = str2val(key)
                    str_valst = val.split(',')
                    # cnt = len(str_valst)
                    # val = str2val(val)
                    rc, res_value = execute_set_regs_cmd(seq_num, addr, list(map(str2val, str_valst)))
                    # rc, res_value = write_one_reg_ex(seq_num, addr, val)
                    is_pass = PASS if rc == 0 else FAIL
                    # sheet_re.write(r, ccnt + 1, is_pass)
                elif action == 'get':
                    adlst = re.split('[ \-~]', key.strip())
                    if len(adlst) == 1:  # 读一个寄存器
                        addr = str2val(key)
                        # bok = False
                        for i in range(5):  # 5次重试
                            rc, res_value = read_one_reg_ex(seq_num, addr)
                            if rc == 0:
                                strval = hex(res_value)
                                sheet_re.write(r, ccnt + 2, f'{strval}')
                                # and res_value == str2val(exp):  # 待确认读一个寄存器的是否存在转换后比较？
                                res_value = val_after_method(strval, method)
                                sheet_re.write(r, ccnt + 4, f'{res_value}')
                                if check_val_range(res_value, exp):
                                    is_pass = PASS
                                    # bok = True
                                    break
                            time.sleep(1)
                    else:  # 其他读多个寄存器的场景
                        for i in range(5):  # 5次重试
                            addr_start = str2val(adlst[0])
                            addr_end = str2val(adlst[-1])
                            cnt = addr_end - addr_start + 1
                            rc, data = read_reg_val(seq_num, addr_start, cnt)
                            if rc == 0:
                                strval = '0x' + bytes2str(data)
                                sheet_re.write(r, ccnt + 2, f'{strval}')
                                res_value = val_after_method(strval, method)
                                sheet_re.write(r, ccnt + 4, f'{res_value}')
                                if check_val_range(res_value, exp):
                                    is_pass = PASS
                                    break
                            time.sleep(1)

            elif cmd_type == "mode_switch":
                if action == 'low_power':
                    if val == '1' or val == 'TRUE':
                        is_lowpwr = True
                    else:
                        is_lowpwr = False
                    res = execute_set_lowpwr_enable_cmd(seq_num, is_lowpwr)
                    is_pass = PASS if res[0] == 0 else FAIL
                elif action == 'reset':
                    res = execute_set_reset_cmd(seq_num)
                    is_pass = PASS if res[0] == 0 else FAIL
                elif action == 'tx_disable':
                    if val == '1' or val == 'TRUE':
                        is_txdisable = True
                    else:
                        is_txdisable = False
                    res = execute_set_tx_enable_cmd(seq_num, not is_txdisable)
                    is_pass = PASS if res[0] == 0 else FAIL

            elif cmd_type == "sleep":
                time.sleep(float(val))
                is_pass = PASS
            elif cmd_type == "hdr" and HDR_FLAG:
                transaction = 100
                rc, res_value = handleDiagSetGetCmd(seq_num, action, key, mcm_valst, transaction)
                if rc == 0:
                    sheet_re.write(r, ccnt + 2, res_value)
                    if check_val_range(res_value, exp):
                        is_pass = PASS
            elif cmd_type == "opm_tx":
                if testname == CAL:
                    osw_tr.set_olp(0)
                opm_pwr = get_opm_pwr()
                my_sig.infoSignal.emit(-1,f'opm pwr: {opm_pwr}')
                sheet_re.write(r, ccnt + 2, opm_pwr)
                res_value = opm_pwr + tx_pwr_offset
                sheet_re.write(r, ccnt + 4, res_value)
                if check_val_range(res_value, exp):
                    is_pass = PASS
            elif cmd_type == "opm_rx":
                if testname == CAL:
                    osw_tr.set_olp(1)
                opm_pwr = get_opm_pwr()
                my_sig.infoSignal.emit(-1,f'opm pwr: {opm_pwr}')
                sheet_re.write(r, ccnt + 2, opm_pwr)
                res_value = opm_pwr + rx_pwr_offset
                sheet_re.write(r, ccnt + 4, res_value)
                if check_val_range(res_value, exp):
                    is_pass = PASS
            elif cmd_type == "opm":
                if action == 'read_power':
                    res_value = get_opm_pwr()
                    my_sig.infoSignal.emit(-1,f'opm pwr: {res_value}')
                    sheet_re.write(r, ccnt + 2, res_value)
                    if check_val_range(res_value, exp):
                        is_pass = PASS
                elif action == 'powermode':
                    rc, res_value = olp85.powermode()
                    is_pass = PASS if rc == 0 else FAIL
            elif cmd_type == "rxpower":
                if action == 'set':
                    # val = str2val(val_to_str(val))
                    val = float(val)
                    rc, res_value = set_rx_power(val)
                    # rc, res_value = set_to_power(olp85, voa, val)
                    if rc == 0:
                        is_pass = PASS
                        sheet_re.write(r, ccnt + 2, res_value)
                elif action == 'below':
                    # val = str2val(val_to_str(val))
                    val = float(val)
                    rc, res_value = rx_power_adjust_below(val)
                    if rc == 0:
                        is_pass = PASS
                        sheet_re.write(r, ccnt + 2, res_value)
                elif action == 'above':
                    # val = str2val(val_to_str(val))
                    val = float(val)
                    rc, res_value = rx_power_adjust_above(val)
                    if rc == 0:
                        is_pass = PASS
                        sheet_re.write(r, ccnt + 2, res_value)
            elif cmd_type == 'func':
                rc, res_value = getattr(this, method)(*str_valst)  # 执行名为method的函数，value列表中的数据作为函数参数
                sheet_re.write(r, ccnt + 4, f'{res_value}')
                rem_dic[method] = res_value
                if rc == 0 and check_val_range(res_value, exp):
                    is_pass = PASS
                my_sig.infoSignal.emit(-1,f'{method}() return code: {rc}, value: {res_value}')

            elif cmd_type == 'qdd_register':
                page, addrs = key.split(':')
                page = int(page[:-1], 16)
                addr_start = int(addrs.split('-')[0])
                try:
                    addr_stop = int(addrs.split('-')[1])
                except:
                    addr_stop = addr_start
                if action == 'set':
                    str_valst = val.split(',')
                    rc, res_value = writeI2CWords(seq_num, page, addr_start, list(map(str2val, str_valst)))
                    is_pass = PASS if rc == 0 else FAIL
                elif action == 'get':
                    for i in range(5):  # 5次重试
                        cnt = addr_stop - addr_start + 1
                        rc, data = readI2CWords(seq_num, page, addr_start, cnt)
                        if rc == 0:
                            strval = '0x' + bytes2str(data)
                            sheet_re.write(r, ccnt + 2, f'{strval}')
                            res_value = val_after_method(strval, method)
                            sheet_re.write(r, ccnt + 4, f'{res_value}')
                            if check_val_range(res_value, exp):
                                is_pass = PASS
                                break
                        time.sleep(1)

            sheet_re.write(r, ccnt + 1, is_pass)

            if is_pass != PASS:
                totoal_res = False
                my_sig.sig_exception.emit(f'{cmd_type}: {action} {key} fail: {res_value}')
                my_sig.sig_exception.emit(f'{test_des} 失败！')
                if stop_on_fail:
                    raise Exception(f'{test_des} 失败！{res_value}')
            if rem_name:
                # my_sig.infoSignal.emit(-1,f'remember name: {rem_name}...')
                if ',' in rem_name:  # 保存的名字有多个时，逗号隔开，注意此时正则表达式也需要写成捕获对应个数的值
                    rnames = rem_name.split(',')
                    rem_cnt = min(len(rnames), len(res_value))
                    for j in range(rem_cnt):  # 个数取remember名和返回值的个数的最小值
                        # rem_val = res_value[j]
                        rem_dic[rnames[j].strip()] = res_value[j]  # res_value[j].strip()
                        my_sig.infoSignal.emit(-1,
                            f'remember: {rnames[j].strip()} : {res_value[j]}, {type(res_value[j])}')  # , type(res_value): {type(res_value)}
                else:
                    rem_dic[rem_name] = res_value
                    my_sig.infoSignal.emit(-1,
                        f'remember: {rem_name} : {res_value}, {type(res_value)}')  # , type(res_value): {type(res_value)}

        # 写TOF配置
        if sheet_idx == 0 and "tof_file" in params:
            rc, res_value = write_tof_data(params["tof_sn"], params["tof_file"])
            sheet_re.write(rcnt, ccnt + 4, f'{res_value}')
            is_pass = PASS if rc == 0 else FAIL
            sheet_re.write(rcnt, ccnt + 1, is_pass)
            if is_pass != PASS:
                my_sig.sig_exception.emit(f'写TOF配置失败！')
                raise Exception(f'写TOF配置失败: {res_value}')

        if totoal_res:
            is_pass_total = PASS
            res_flag = "PASS"
            my_sig.sig_button_color.emit(testname, sheet_idx, STATE_PASS)
            # my_sig.sig_update_state.emit(STATE_PASS)
        else:
            is_pass_total = FAIL
            res_flag = "FAIL"
            my_sig.sig_button_color.emit(testname, sheet_idx, STATE_FAIL)
            # my_sig.sig_update_state.emit(STATE_FAIL)
        # is_pass_total = PASS if totoal_res else FAIL
        cost_time = round(time.time() - s_time, 2)
        my_sig.infoSignal.emit(-1,f'{itemname} finish: {is_pass_total} ({cost_time} s)\n')
        sheet_re.write(rcnt + 1, 1, '总结果')
        sheet_re.write(rcnt + 1, ccnt + 1, f'{is_pass_total}')
        if save_alone:
            # wb_re.save(f'./log/{testname}_{sheet_idx}_{module_sn}_{start_time}.xls')
            save_one_sheet(wb_re, itemname,
                           os.path.join(logdir, f'{testname}_{sheet_idx}_{module_sn}_{res_flag}_{start_time}.xls'))
        save_debug_xls(wb_re, f'{testname}_result.xls')
        # my_sig.sig_enable_ui.emit(True)
        return totoal_res
    # except stopException as e:
    #     my_sig.infoSignal.emit(-1,f'{itemname} 测试手动停止')
    #     my_sig.sig_button_color.emit(testname, sheet_idx, STATE_STOP)
    #     # my_sig.sig_update_state.emit(STATE_STOP)
    #     raise
    except Exception as e:
        if get_exit_flag():
            my_sig.infoSignal.emit(-1,f'{itemname} 测试手动停止')
            my_sig.sig_button_color.emit(testname, sheet_idx, STATE_STOP)
            res_flag = "STOP"
            # my_sig.sig_update_state.emit(STATE_STOP)
        else:
            my_sig.sig_exception.emit(f"{itemname} Exception: " + str(e))
            my_sig.sig_button_color.emit(testname, sheet_idx, STATE_FAIL)
            res_flag = "FAIL"
            # my_sig.sig_update_state.emit(STATE_FAIL)
        # my_sig.sig_enable_ui.emit(True)
        if itemname:
            if save_alone:
                # wb_re.save(f'./log/{testname}_{sheet_idx}_{module_sn}_{start_time}.xls')
                save_one_sheet(wb_re, itemname,
                               os.path.join(logdir, f'{testname}_{sheet_idx}_{module_sn}_{res_flag}_{start_time}.xls'))
            save_debug_xls(wb_re, f'{testname}_result.xls')
        raise
    finally:
        my_sig.sig_enable_ui.emit(True)


def save_debug_xls(wb_re, fname):
    try:
        wb_re.save(os.path.join(logdir_debug, fname))
    except Exception as e:
        my_sig.sig_exception.emit(str(e))


def get_cmds_from_workbook(workbook, cmdtype):
    sheet = workbook.sheet_by_name(cmdtype)
    cmd_items = []
    rcnt = sheet.nrows
    ccnt = sheet.ncols
    for r in range(rcnt):
        action = sheet.cell(r, 0).value
        cmd = sheet.cell(r, 1).value
        cmdstr = action + ',' + cmd + ','
        val_lst = []
        for c in range(2, ccnt):
            val = sheet.cell(r, c).value
            if isinstance(val, str):
                val = val.strip()
                if val:
                    val_lst.append(val)
            elif val is not None:
                val_lst.append(val)
        cmdstr += '[' + ','.join(str(i) for i in val_lst) + ']'
        cmd_items.append(cmdstr)
    return cmd_items


def str2mcmval(strval: str):
    if re.search('^0x[0-9a-f]+$', strval, re.IGNORECASE):  # 0x开头，整数
        return int(strval, 16)
    # elif re.search('^[+-]?\d+(\.\d+)?$', strval):  # 纯数字、有小数点都作为浮点数处理
    elif re.search('^[+-]?\d+(\.\d+)?(e[-]?\d+)?$', strval, re.IGNORECASE):  # 纯数字、有小数点或科学计数法都作为浮点数处理
        fval = float(strval)
        return float2hex(fval)
    else:
        return strval


def get_mcm_cmd_from_str(cmdstr):
    pattern = '\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*\\[(.*)\\]'
    sobj = re.search(pattern, cmdstr)
    if not sobj:
        raise Exception(f'mcm cmd not match, pattern: {pattern}, cmdstr: {cmdstr}')
    vstr = sobj.group(3)
    vstrlst = []
    if vstr:
        vstrlst = sobj.group(3).split(',')
    valst = []
    for ele in vstrlst:
        ele = ele.strip()
        valst.append(str2mcmval(ele))
    action = sobj.group(1)
    cmd = sobj.group(2)
    return action, cmd, valst


def osw_sop_set_olp(status):
    global osw_sop
    status = int(float(status) + 0.1)
    if status not in [0, 1]:
        return 1, "不支持的光开关状态"
    act = "接入" if status == 1 else "Bypass"
    my_sig.infoSignal.emit(-1,act + '扰偏仪')
    osw_sop.set_olp(status)
    return 0, ''


def osw_ase_set_olp(status):
    global osw_ase
    status = int(float(status) + 0.1)
    if status not in [0, 1]:
        return 1, "不支持的光开关状态"
    act = "接入" if status == 1 else "断开"
    my_sig.infoSignal.emit(-1,act + '噪声源')
    osw_ase.set_olp(status)
    return 0, ''


def osw_oau_set_olp(status):
    global osw_oau
    status = int(float(status) + 0.1)  # 读excel读到1.0。。
    if status not in [0, 1]:
        return 1, "不支持的光开关状态"
    # act = "接入" if status == 1 else "断开"
    # my_sig.infoSignal.emit(-1,act + '噪声源')
    osw_oau.set_olp(status)
    return 0, ''


# voa_ase.set_attr(voa_ase.il_max)
def voa_ase_set_attr(att):
    global voa_ase
    if isinstance(att, str):
        if att.strip() == "il_max":
            att = voa_ase.il_max
    att = float(att)
    # if status not in [0, 1]:
    #     return 0, "不支持的光开关状态"
    # act = "接入" if status == 1 else "Bypass"
    # my_sig.infoSignal.emit(-1,act + '扰偏仪')
    return 0, voa_ase.set_attr(att)


def eset_8xxx_override(seq_num, addr_begin: int, cnt: int, val_lst: list):
    # 更改厂家信息命令，val_lst为int的list
    i = 0
    val_cnt = len(val_lst)
    reply = ""
    for addr in range(addr_begin, addr_begin + cnt):
        if i < val_cnt:
            val = val_lst[i]
        else:
            val = 0x20
        rc, reply = execute_mcm_cmd(seq_num, 'eset', '8XXX_OVERRIDE_I', [addr, val])
        if rc != 0:
            raise Exception(f"eset 8XXX_OVERRIDE_I {addr} {val} 命令失败")
            # return 1, f'mcm命令失败'
        i += 1
    return 0, reply


def eset_8xxx_override_str(seq_num, addr_begin: int, cnt: int, strVal: str):
    # 更改厂家信息命令，写入值为strVal的ASCII码
    i = 0
    val_cnt = len(strVal)
    reply = ""
    for addr in range(addr_begin, addr_begin + cnt):
        if i < val_cnt:
            val = ord(strVal[i])
        else:
            val = 0x20
        rc, reply = execute_mcm_cmd(seq_num, 'eset', '8XXX_OVERRIDE_I', [addr, val])
        if rc != 0:
            raise Exception(f"eset 8XXX_OVERRIDE_I {addr} {val} 命令失败")
            # return 1, 'mcm命令失败'
        i += 1
    return 0, reply


def read_all_vend(seq_num):
    vend_name = read_vend_name(seq_num)
    vend_oui = read_vend_oui(seq_num)
    vend_pn = read_vend_pn(seq_num)
    vend_sn = read_vend_sn(seq_num)
    hw_ver = read_hw_ver(seq_num)
    fw_vera, fw_verb = read_fw_ver(seq_num)
    return vend_name, vend_oui, vend_pn, vend_sn, hw_ver, fw_vera, fw_verb


def save_excel_info(sheet, row, key, is_pass, value=""):
    # 写入测试单
    if sheet:
        sheet.write(row, 0, key)  # , cell_overwrite_ok=True
        cur_time = time.strftime("%H:%M:%S", time.localtime())
        sheet.write(row, 1, value)
        sheet.write(row, 2, cur_time)
        sheet.write(row, 3, is_pass)


def write_all_vend(sheet_vend, seq_num, vend_name, vend_oui, vend_pn, vend_sn, hw_ver):
    reply = execute_hdr_retry(seq_num, "eset", "8XXX_OVERRIDE_EEPROM_HDR", [0xfeedface], HDR_FLAG)
    # rc, reply = execute_mcm_cmd(seq_num, "eset", "8XXX_OVERRIDE_EEPROM_HDR", [0xfeedface])
    save_excel_info(sheet_vend, 0, "eset 8XXX_OVERRIDE_EEPROM_HDR 0xfeedface", PASS, reply)
    write_vend_name(seq_num, vend_name)
    save_excel_info(sheet_vend, 1, "写生产厂家", PASS, vend_name)
    write_vend_oui(seq_num, vend_oui)
    save_excel_info(sheet_vend, 2, "写组织唯一标识符", PASS, vend_oui)
    write_vend_pn(seq_num, vend_pn)
    save_excel_info(sheet_vend, 3, "写产品编码", PASS, vend_pn)
    # if not vend_sn:  # vend_sn为空时，读当前vend_sn，取后9位，前面加上"OCT"得到新SN，写入新SN
    #     sn_ori = read_vend_sn(seq_num).strip()
    #     vend_sn = "OCT" + sn_ori[-9:]
    write_vend_sn(seq_num, vend_sn)
    save_excel_info(sheet_vend, 4, "写序列号", PASS, vend_sn)
    if hw_ver:
        write_hw_ver(seq_num, hw_ver)
        save_excel_info(sheet_vend, 5, "写硬件版本", PASS, hw_ver)

    execute_hdr_retry(seq_num, "eset", "8XXX_OVERRIDE_EEPROM_HDR", [0xCAFEBABE], HDR_FLAG)
    # execute_mcm_cmd(seq_num, "eset", "8XXX_OVERRIDE_EEPROM_HDR", [0xCAFEBABE])
    save_excel_info(sheet_vend, 6, "eset 8XXX_OVERRIDE_EEPROM_HDR 0xCAFEBABE", PASS, reply)

    execute_set_reset_cmd_retry(seq_num)
    time.sleep(5)
    read_name, read_oui, read_pn, read_sn, read_ver, read_vera, read_verb = read_all_vend(seq_num)
    my_sig.sig_vend_name.emit(read_name)
    my_sig.sig_vend_oui.emit(read_oui)
    my_sig.sig_vend_pn.emit(read_pn)
    my_sig.sig_vend_sn.emit(read_sn)
    my_sig.sig_vend_ver.emit(read_ver)
    my_sig.sig_vend_fwver.emit(read_vera, read_verb)
    bok = True
    str_err = ''
    if read_name != vend_name + ' ' * (len(read_name) - len(vend_name)):  # 补齐空格后与回读内容比较
        bok = False
        str_err += '生产厂家 '
        is_pass = FAIL
    else:
        is_pass = PASS
    save_excel_info(sheet_vend, 8, "回读校验生产厂家", is_pass, read_name)
    if read_oui != vend_oui:
        bok = False
        str_err += '组织唯一标识符 '
        is_pass = FAIL
    else:
        is_pass = PASS
    save_excel_info(sheet_vend, 9, "回读校验组织唯一标识符", is_pass, read_oui)
    if read_pn != vend_pn + ' ' * (len(read_pn) - len(vend_pn)):
        bok = False
        str_err += '产品编码 '
        is_pass = FAIL
    else:
        is_pass = PASS
    save_excel_info(sheet_vend, 10, "回读校验产品编码", is_pass, read_pn)
    if read_sn != vend_sn + ' ' * (len(read_sn) - len(vend_sn)):
        bok = False
        str_err += '序列号 '
        is_pass = FAIL
    else:
        is_pass = PASS
    save_excel_info(sheet_vend, 11, "回读校验序列号", is_pass, read_sn)
    if hw_ver and read_ver != hw_ver:
        bok = False
        str_err += '硬件版本 '
        is_pass = FAIL
    else:
        is_pass = PASS
    save_excel_info(sheet_vend, 12, "回读校验硬件版本", is_pass, read_ver)

    if not bok:
        str_err += '回读校验失败!'
        return 1, str_err
    return 0, ''


def write_vend_name(seq_num, vend_name: str):
    # 写生产厂家，REG地址8021h-8030h，写ascii码
    my_sig.infoSignal.emit(-1,f"写生产厂家：{vend_name}")
    eset_8xxx_override_str(seq_num, 0x8021, 16, vend_name)


def write_vend_oui(seq_num, vend_oui: str):
    # 写组织唯一标识符，REG地址8031h-8033h，vend_oui如："0x6c 0x2e 0x33"
    my_sig.infoSignal.emit(-1,f"写组织唯一标识：{vend_oui}")
    val_lst = list(map(lambda x: int(x, 16), vend_oui.split()))
    eset_8xxx_override(seq_num, 0x8031, 3, val_lst)


def write_vend_pn(seq_num, vend_pn: str):
    # 写产品编码，REG地址8034h-8043h，写ascii码
    my_sig.infoSignal.emit(-1,f"写产品编码：{vend_pn}")
    eset_8xxx_override_str(seq_num, 0x8034, 16, vend_pn)


def write_vend_sn(seq_num, vend_sn: str):
    # 写序列号，REG地址8044h-8053h，写ascii码
    my_sig.infoSignal.emit(-1,f"写序列号：{vend_sn}")
    eset_8xxx_override_str(seq_num, 0x8044, 16, vend_sn)


def write_hw_ver(seq_num, hw_ver: str):
    # 写硬件版本号，is_y为0时写x版本，为1时写y版本，hw_ver如"1.5"，写入寄存器的值为15
    lst = hw_ver.split('.')
    if len(lst) != 2:
        raise Exception("请输入正确的硬件版本号格式：x.y")
    x, y = lst
    my_sig.infoSignal.emit(-1,f"写硬件版本：{x}.{y}")
    eset_8xxx_override(seq_num, 0x806A, 1, [int(x)])
    eset_8xxx_override(seq_num, 0x806B, 1, [int(y)])


def read_vend_name(seq_num):
    # 读取生产厂家，REG地址8021h-8030h，返回完整信息，如果后面有空格，返回的信息里也有空格
    lst = read_max_120_reg(seq_num, 0x8021, 16)
    vend_name = ''.join(map(chr, lst))
    my_sig.infoSignal.emit(-1,f"生产厂家读值：[{vend_name}]")
    return vend_name


def read_vend_oui(seq_num):
    # 读取组织唯一标识符，REG地址8031h-8033h
    # return read_max_120_reg(seq_num, 0x8031, 3)
    lst = read_max_120_reg(seq_num, 0x8031, 3)
    vend_oui = ' '.join(map(hex, lst))
    my_sig.infoSignal.emit(-1,f"组织唯一标识符读值：[{vend_oui}]")
    return vend_oui


def read_vend_pn(seq_num):
    # 读产品编码，REG地址8034h-8043h
    lst = read_max_120_reg(seq_num, 0x8034, 16)
    vend_pn = ''.join(map(chr, lst))
    my_sig.infoSignal.emit(-1,f"产品编码读值：[{vend_pn}]")
    return vend_pn


def read_prod_date(seq_num):
    # 读生产日期
    date = ""
    # tempdate = "20220609"
    # result = execute_mcm_cmd(seq_num, "eset", "MFG_DOM", [tempdate])
    result = execute_mcm_cmd(seq_num, "eget", "MFG_DOM", [])
    if "PASS" in result[1]:
        sobj = re.search('(\\S+)', result[1])
        if sobj:
            date = sobj.group(1)
    my_sig.infoSignal.emit(-1,f"生产日期读值：[{date}]")
    return date


def read_vend_sn(seq_num):
    # 读序列号，REG地址8044h-8053h
    lst = read_max_120_reg(seq_num, 0x8044, 16)
    vend_sn = ''.join(map(chr, lst))
    my_sig.infoSignal.emit(-1,f"序列号读值：[{vend_sn}]")
    return vend_sn


def read_hw_ver(seq_num):
    # 读硬件版本号，is_y为0时读x版本，为1时读y版本，返回字符串如"1.5"
    x, y = read_max_120_reg(seq_num, 0x806A, 2)
    hw_ver = F'{x}.{y}'
    my_sig.infoSignal.emit(-1,f"硬件版本读值：[{hw_ver}]")
    return hw_ver


def read_fw_ver(seq_num):
    # 读固件版本号
    xa, ya = read_max_120_reg(seq_num, 0x806C, 2)
    xb, yb = read_max_120_reg(seq_num, 0x807B, 2)
    z = read_one_reg(seq_num, 0x9010)
    vera = f'{xa}.{ya}.{z & 0xff}'
    verb = f'{xb}.{yb}.{z >> 8}'
    my_sig.infoSignal.emit(-1,f"固件版本读值：[{vera}], [{verb}]")
    return vera, verb


def tia_reg_cal():
    transaction = 0
    my_sig.infoSignal.emit(-1,'------------------------------------------------------------')
    my_sig.infoSignal.emit(-1,'TIA Reg1&Reg6 Calibration start')
    my_sig.infoSignal.emit(-1,'------------------------------------------------------------')
    # Calibration for Reg1
    Reg1_value = [-1, -1, -1, -1]
    for channel in range(4):
        [Cal_sucessful, tansaction, value] = PreDrv_cal(seq_num, transaction, channel)
        if Cal_sucessful == 'TRUE':
            Reg1_value[channel] = value
        else:
            my_sig.infoSignal.emit(-1,'PreDrv_cal failed')
            return 1, 'TIA校准失败'

    my_sig.infoSignal.emit(-1,'TIA Register1 Calibration Result is: 0x%.4x 0x%.4x 0x%.4x 0x%.4x' % (
        Reg1_value[0], Reg1_value[1], Reg1_value[2], Reg1_value[3]))
    my_sig.infoSignal.emit(-1,'------------------------------------------------------------')
    # Calibration for Reg6
    Reg6_value = [-1, -1, -1, -1]
    execute_mcm_cmd_once(seq_num, "set", "PICP_SW_CNTL_RXTIA", [0, 0, 0])
    execute_mcm_cmd_once(seq_num, "set", "BSP_DAC_REG", [0, 'PIC_TIAOA1X', float2hex(0.0)])
    execute_mcm_cmd_once(seq_num, "set", "BSP_DAC_REG", [0, 'PIC_TIAOA2X', float2hex(0.0)])
    execute_mcm_cmd_once(seq_num, "set", "BSP_DAC_REG", [0, 'PIC_TIAOA1Y', float2hex(0.0)])
    execute_mcm_cmd_once(seq_num, "set", "BSP_DAC_REG", [0, 'PIC_TIAOA2Y', float2hex(0.0)])

    execute_mcm_cmd_once(seq_num, "set", "PIC_TIA_REG", [0, 0x72, 0x0002, 0x618])
    execute_mcm_cmd_once(seq_num, "set", "PIC_TIA_REG", [0, 0x72, 0x1002, 0x618])
    execute_mcm_cmd_once(seq_num, "set", "PIC_TIA_REG", [0, 0x73, 0x0002, 0x618])
    execute_mcm_cmd_once(seq_num, "set", "PIC_TIA_REG", [0, 0x73, 0x1002, 0x618])
    my_sig.infoSignal.emit(-1,'TIA Register6 Calibration Result is:0x%.4x 0x%.4x 0x%.4x 0x%.4x' % (
        Reg6_value[0], Reg6_value[1], Reg6_value[2], Reg6_value[3]))
    my_sig.infoSignal.emit(-1,'------------------------------------------------------------')

    for channel in range(4):
        [Cal_sucessful, tansaction, value] = CurrSteBias_cal(seq_num, transaction, channel)
        if Cal_sucessful == 'TRUE':
            Reg6_value[channel] = value
        else:
            my_sig.infoSignal.emit(-1,'CurrSteBias_cal failed')
            return 1, 'TIA校准失败'

    execute_mcm_cmd_once(seq_num, "eset", "PICX_TIA_REG_01", [0] + Reg1_value)
    execute_mcm_cmd_once(seq_num, "eset", "PICX_TIA_REG_06", [0] + Reg6_value)
    return 0, 'TIA校准成功'


def write_tof_data(sn: str, tof_file_name: str):
    # tof_file_name = os.path.join(cfg_dir, 'TOF', f'santec_{sn}.csv')
    my_sig.infoSignal.emit(-1,f'写TOF配置: [{tof_file_name}]...')
    fid = csv.reader(open(tof_file_name))
    rc, reply = execute_mcm_cmd(seq_num, "eset", "BSP_ETOF_INSTALLED", [0, 1])
    if rc != 0 or 'PASS' not in reply:
        return 1, 'eset BSP_ETOF_INSTALLED fail'
    execute_hdr_retry(seq_num, "eset", "BSP_NTWK_LANE_EEPROM_HDR", [0, 0xCAFEBABE], HDR_FLAG)
    rc, reply = execute_mcm_cmd(seq_num, 'eset', 'TUNE2_FILTER_SERIAL_NUM', [0, sn])
    if rc != 0 or 'PASS' not in reply:
        return 1, 'eset TUNE2_FILTER_SERIAL_NUM fail'
    execute_hdr_retry(seq_num, "eset", "TUNE2_FILTER_EEPROM_HDR", [0, 0xFEEDFACE], HDR_FLAG)

    i = 105
    for x in fid:
        if len(x) == 3:
            freq = float(x[0])
            filter2_vol = float(x[1])
            filter1_vol = float(x[2])
            if filter1_vol * filter2_vol != 0:
                raise Exception('<filter_1_voltage> 和<filter_2_voltage>，同时不为0，可能损坏TOF')
            rc, reply = execute_mcm_cmd(seq_num, 'eset', 'TUNE2_TABLE_ENTRY', [0, i, float2hex(freq), float2hex(filter1_vol), float2hex(filter2_vol)])
            if rc != 0 or 'PASS' not in reply:
                return 1, 'eset TUNE2_TABLE_ENTRY fail'
            i -= 1
        else:
            break
    execute_hdr_retry(seq_num, "eset", "TUNE2_FILTER_EEPROM_HDR", [0, 0xCAFEBABE], HDR_FLAG)
    my_sig.infoSignal.emit(-1,f'写TOF配置成功: [{tof_file_name}]')
    return 0, f"TOF信息写入成功({tof_file_name})"

####################################
# 发包和收包必须在不同的线程中执行
# 收包进程阻塞发包主进程，解析结果到发包主进程的全局临时变量
# 收包进程主动停止条件：1）收包进程解析到正确响应报文 2）收包进程5s内未解析到正确响应报文
####################################


if __name__ == "__main__":
    logger, config = init()
    src_eth = config.get("winpcap", "src_eth")

    # # 全局临时变量，记录每次请求开始时间
    # tmp_request_time = int(time.time())
    # # 全局临时变量，记录每次命令的长度
    # tmp_request_len = 0
    # # 全局临时变量，记录每次命令的返回值
    # tmp_callback_value = None

    print("0, 'get', 'ISL_SET_GET', [0x24]")
    # async_win_send_packet(0, 'get', 'ISL_SET_GET', [0x24])
    # WinPcapUtils.capture_on(src_eth, packet_callback_once)
    # value1 = tmp_callback_value
    value1 = execute_mcm_cmd(0, 'get', 'ISL_SET_GET', [0x24])
    print(value1)

    # print("1, 'get', 'ISL_SET_GET', [0x24]")
    # async_win_send_packet(1, 'get', 'ISL_SET_GET', [0x24])
    # WinPcapUtils.capture_on(src_eth, packet_callback_once)
    # value2 = tmp_callback_value
    # print(value2)

    print("0, 'get', 'BSP_ADC_REG', [0, 'PIC_TOP1_NTC']")
    # async_win_send_packet(0, 'get', 'BSP_ADC_REG', [0, 'PIC_TOP1_NTC'])
    # WinPcapUtils.capture_on(src_eth, packet_callback_once)
    # value3 = tmp_callback_value
    value3 = execute_mcm_cmd(0, 'get', 'BSP_ADC_REG', [0, 'PIC_TOP1_NTC'])
    print(value3)
