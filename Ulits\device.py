# -*- coding: utf-8 -*-
# @Time : 2021/9/14 13:11
# <AUTHOR> 25646
# @File : calibration_procedure.py
# @Project: 400G CFP2 DCO
import serial
import socket
import time
from ..utils.MySignal import my_sig
import threading
import re
INVALID_PWR = -90


class COM(object):
    bytes_mode = True
    wait_limit = 100

    def __init__(self, com: str, boudrate: int):
        self.ser = serial.Serial(com, int(boudrate), timeout=1)
        self.status = False
        self.__connect__()

    def __connect__(self):
        if not self.ser.isOpen():
            try:
                self.ser.open()
            except:
                self.status = False
                return False

    def send_recv(self, cmd):
        _=self.ser.read_all()
        if not self.bytes_mode:
            cmd = cmd.encode('utf-8')   # + b'\r'  # 温循箱不加结束符
        self.ser.write(cmd)
        t = 0
        while True:
            t += 1
            time.sleep(0.1)
            re = self.ser.read_all()
            if re != b'':
                break
            if t > 100:
                input('无法读取返回值')
        if not self.bytes_mode:
            re = re.decode('utf-8')
        return re

    def close(self):
        if self.ser.isOpen():
            self.ser.close()

class OPM(object):

    def __init__(self, ip='***********', port=8000):
        self.ip = ip
        self.port = port
        self.s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.s.settimeout(5)
        my_sig.infoSignal.emit(-1,f'try to connect opm {ip}')
        self.s.connect((self.ip, self.port))
        self.powermode()
        # self.send_cmd(':APPM:SEL? ')
        # self.send_cmd(' ')
        # self.send_cmd('*IDN?')
        # print('OSA connect success')
        # self.s.close()

    def send_cmd(self, cmd):
        cmd = cmd.encode('utf-8') + b'\n'
        my_sig.sig_debug.emit(f'send: {cmd}')
        self.s.send(cmd)
        buffer = []
        try:
            while True:
                data = self.s.recv(1024)
                if data:
                    buffer.append(data)
                else:
                    break
        except:
            pass
        finally:
            buffer = b''.join(buffer)
            my_sig.sig_debug.emit(f'recv: {buffer}')
            return buffer

    def powermode(self):
        cmd = ':APPM:SEL "BBPOWFULL"'
        self.send_cmd(cmd)
        data = self.send_cmd(':APPM:SEL?')
        if data == b'BBPOWFULL\n':
            my_sig.infoSignal.emit(-1,'set powermode successed ')
            return 0, ''
        else:
            my_sig.infoSignal.emit(-1,'set opm powermode failed')
            return 1, 'set opm powermode failed'
            # input('set powermode failed')

    def read_power(self):
        try:
            cmd = ':POW:DBM:WLEN1?'
            data = self.send_cmd(cmd)
            return float(data.decode('utf-8'))
        except:
            return INVALID_PWR

    def read_power_w(self):
        try:
            cmd = ':POW:WATT:WLEN1?'
            data = self.send_cmd(cmd)
            return float(data.decode('utf-8'))
        except:
            return 0

    def close_opm(self):
        if self.s:
            self.send_cmd('CLOSE')
            self.s.close()
            self.s = None


class AQ6150(object):
    def __init__(self, ip='**********', port=10001, user='anonymous', pwd='123'):
        self.ip = ip
        self.port = port
        self.user = user
        self.pwd = pwd
        self.status = False
        self.s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.s.settimeout(2)

    def __connect__(self):
        my_sig.infoSignal.emit(-1,f'try to connect AQ6150 {self.ip}')
        try:
            self.s.connect((self.ip, self.port))
        except OSError:
            pass
        except:
            self.status = False
            return False
        self.send_cmd('open "%s""' % self.user)
        reply = self.receive()
        if 'AUTHENTICATE CRAM-MD5' not in reply:
            self.s.close()
            self.status = False
            return False
        self.send_cmd(self.pwd)
        reply = self.receive()
        if "ready" not in reply:
            self.s.close()
            self.status = False
            return False
        self.send_cmd('*IDN?')
        reply = self.receive()
        my_sig.infoSignal.emit(-1,'%s connected successfully' % reply[:-2])
        self.status = True
        self.setup()
        return True

    def setup(self):
        self.send_cmd("*RST")
        self.send_cmd(':UNIT:WL NM')
        self.send_cmd(':UNIT:POW DBM')
        self.send_cmd(':CORRection:DEVice BROad')

    def read_wavelength(self):
        if not self.status:
            self.__connect__()
        self.send_cmd(':READ:ARR:POW:WAV?')
        reply = self.receive()
        if reply == '0\r\n':
            return 0
        else:
            wavelength = self.translate_data(reply)[0] * 1e9
        return wavelength

    def read_power(self):
        if not self.status:
            self.__connect__()
        self.send_cmd(":FETC:ARR:POW?")
        reply = self.receive()
        if reply == '0\r\n':
            return -120
        else:
            power = self.translate_data(reply)[0]
            return power

    def send_cmd(self, cmd):
        my_sig.sig_debug.emit('send:' + cmd)
        cmd = cmd.encode('utf-8') + b'\n'
        self.s.send(cmd)

    def receive(self):
        buffer = []
        t0 = time.time()
        while len(buffer) == 0:
            try:
                while True:
                    data = self.s.recv(1024)
                    if data:
                        buffer.append(data)
                    else:
                        break
            except:
                pass
            t1 = time.time()
            if t1 - t0 > 5:
                raise ValueError('读取返回值超时')

        buffer = b''.join(buffer)
        reply = buffer.decode('utf-8')
        # print('recv:', reply)
        return reply

    @staticmethod
    def translate_data(data: str):
        d_list = []
        temp_list = data.strip().split('\r\n')
        for temp in temp_list:
            if temp != '':
                d = temp.split(',')[-1]
                d_list.append(float(d))
        return d_list


class AQ6370(object):
    def __init__(self, ip='**********', port=10001, user='anonymous', pwd='123'):
        self.ip = ip
        self.port = port
        self.user = user
        self.pwd = pwd
        self.status = False
        self.s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.s.settimeout(2)

    def __connect__(self):
        my_sig.infoSignal.emit(-1,f'try to connect AQ6370 {self.ip}')
        try:
            self.s.connect((self.ip, self.port))
        except OSError:
            pass
        except:
            self.status = False
            return False
        self.send_cmd('open "%s""' % self.user)
        reply = self.receive()
        if 'AUTHENTICATE CRAM-MD5' not in reply:
            self.s.close()
            self.status = False
            return False
        self.send_cmd(self.pwd)
        reply = self.receive()
        if "ready" not in reply:
            self.s.close()
            self.status = False
            return False
        self.send_cmd('*IDN?')
        reply = self.receive()
        my_sig.infoSignal.emit(-1,'%s connected successfully' % reply[:-2])
        self.status = True
        self.setup()
        return True

    def setup(self):
        self.send_cmd("*RST")
        self.send_cmd('CFORM1')
        self.send_cmd(':sens:sens mid')
        self.send_cmd(':trac:act TRA')
        self.send_cmd(':sens:wav:STARt 1523nm')
        self.send_cmd(':sens:wav:STOP 1573nm')
        self.send_cmd(':sens:band 0.1nm')
        self.send_cmd(':sens:sweep:points:auto on')
        self.send_cmd(':init:smode 1')

    def get_spectra(self, start_wl=1523, stop_wl=1573, resolution=0.1):
        if not self.status:
            self.__connect__()
        # if start_wl!= 1523:
        self.send_cmd(':sens:wav:STARt %.2fnm' % start_wl)
        # if stop_wl != 1563:
        self.send_cmd(':sens:wav:STOP %.2fnm' % stop_wl)
        # if resolution != 0.1:
        self.send_cmd(':sens:band %.2fnm' % resolution)
        self.send_cmd('*CLS')
        self.send_cmd(':init')
        while True:
            self.send_cmd(':stat:oper:even?')
            reply = self.receive()
            if int(reply) & 1 == 1:
                break
        self.send_cmd(':TRACE:X? TRA')
        re = self.receive()
        wavelen_in_m = self.translate_data(re)
        wavelen = [x * 1e9 for x in wavelen_in_m]
        self.send_cmd(':TRACE:Y? TRA')
        re = self.receive()
        power = self.translate_data(re)
        return wavelen, power

    def send_cmd(self, cmd):
        my_sig.sig_debug.emit('send:' + cmd)
        cmd = cmd.encode('utf-8') + b'\n'
        self.s.send(cmd)

    def receive(self):
        buffer = []
        t0 = time.time()
        while len(buffer) == 0:
            try:
                while True:
                    data = self.s.recv(1024)
                    if data:
                        buffer.append(data)
                    else:
                        break
            except:
                pass
            t1 = time.time()
            if t1 - t0 > 5:
                raise ValueError('读取返回值超时')

        buffer = b''.join(buffer)
        reply = buffer.decode('utf-8')
        # my_sig.infoSignal.emit(-1,'recv:', reply)
        return reply

    @staticmethod
    def translate_data(data: str):
        d_list = []
        temp_list = data.strip().split(',')
        for temp in temp_list:
            if temp != '':
                d = temp.split(',')[-1]
                d_list.append(float(d))
        return d_list


class VOA(object):

    def __init__(self, com='COM6', boudrate=9600):
        self.ser = serial.Serial(com, boudrate, timeout=1)
        self.il_max = 50
        self.il_min = 0

    # def __del__(self):
    #     if self.ser.isOpen():
    #         self.ser.close()

    def set_attr(self, att):
        if att > self.il_max:
            att = self.il_max
        elif att < self.il_min:
            att = self.il_min
        my_sig.infoSignal.emit(-1,'设置VOA衰减为：%s dB' % att)
        cmd = [0xef, 0xef, 0x00, 0x04, 0x13, int(att * 100) >> 8, int(att * 100) % 256]
        s = sum(cmd[2:]) & 0xff
        cmd += [s, 0xff, 0xef, 0xff, 0xff]
        rebytes = b''
        for b in cmd:
            rebytes += b.to_bytes(1, 'little')
        # my_sig.infoSignal.emit(-1,f'> {rebytes}')
        re = self.send(rebytes)
        # my_sig.infoSignal.emit(-1,'< ' + re.decode('utf-8'))
        self.get_attr()

    def get_attr(self):
        rebytes = b'\xef\xef\x00\x02\x14\x16\xff\xef\xff\xff'
        # print(rebytes)
        re = self.send(rebytes)
        # print(re)
        try:
            status = re[7]
            att = (re[5] * 256 + re[6]) / 100
            if status == 1:
                my_sig.infoSignal.emit(-1,'查询到VOA衰减为：%s dB' % att)
                self.il = att
                return att
            else:
                my_sig.infoSignal.emit(-1,re)
                my_sig.infoSignal.emit(-1,'读取VOA衰减值失败')
        except:
            my_sig.infoSignal.emit(-1,'读取VOA衰减值失败')

    def send(self, cmd: bytes):
        #my_sig.infoSignal.emit(-1,f'> {cmd}')
        if not self.ser.isOpen():
            self.ser.open()
        self.ser.read_all()
        self.ser.write(cmd)
        t = 0
        while True:
            t += 1
            time.sleep(0.1)
            re = self.ser.read_all()
            if re != b'':
                break
            if t > 100:
                # input('无法读取VOA返回值')
                raise Exception('无法读取VOA返回值')
        # print(re)
        #my_sig.infoSignal.emit(-1,'< ' + re.decode('utf-8'))
        return re

    def receive(self):
        buffer = []
        t0 = time.time()
        while len(buffer) == 0:
            try:
                while True:
                    data = self.s.recv(1024)
                    if data:
                        buffer.append(data)
                    else:
                        break
            except:
                pass
            t1 = time.time()
            if t1 - t0 > 5:
                raise ValueError('读取返回值超时')

    def close(self):
        if self.ser.isOpen():
            self.ser.close()


class VOA_board(object):

    def __init__(self, ip='***************', sn=0x00039511C026):
        self.ip = ip
        self.sn = sn.to_bytes(6, 'big')
        self.port = 9000
        self.il_max = 30
        self.il_min = 0
        self.s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.il = 0
        self.get_attr()

    def set_attr(self, att):
        cmd_send = b'\x10' + int(att * 100).to_bytes(2, 'big')
        cmd_created = self.generate_cmd(cmd_send)
        print_byte('send: ', cmd_created)
        self.s.sendto(cmd_created, (self.ip, self.port))
        reply = self.s.recv(1024)
        print_byte('recv: ', reply)
        cmd_return = self.parse_data(reply)
        sleep_time = abs(att-self.il)/30
        time.sleep(sleep_time)
        if cmd_return == b'\x10\x53':
            my_sig.infoSignal.emit(0,'VOA(%s)设置ATT为%.2fdB 成功' % (self.ip, att))
            # return att
            return self.get_attr()
        elif cmd_return == b'\x10\x46':
            my_sig.infoSignal.emit(0,'VOA(%s)设置ATT为%.2fdB 失败' % (self.ip, att))
            return self.get_attr()
        else:
            my_sig.infoSignal.emit(-1,'VOA(%s)设置ATT为%.2fdB 返回值异常' % (self.ip, att))
            return self.get_attr()

    def get_attr(self):
        cmd_send = b'\x11'
        cmd_created = self.generate_cmd(cmd_send)
        print_byte('send: ', cmd_created)
        self.s.sendto(cmd_created, (self.ip, self.port))
        reply = self.s.recv(1024)
        print_byte('recv: ', reply)
        cmd_return = self.parse_data(reply)
        if cmd_return[0] == 0x11:
            att = ((cmd_return[1] << 8) + cmd_return[2]) / 100
            my_sig.infoSignal.emit(-1,'VOA(%s)查询到ATT为%.2fdB' % (self.ip, att))
            self.il = att
            return att
        else:
            my_sig.infoSignal.emit(-1,'VOA(%s)查询ATT失败' % self.ip)
            self.il = 100
            return 100

    def generate_cmd(self, CMD: bytes):
        CMD1_LEN = len(CMD).to_bytes(2, 'big')
        CMDNUM = b'\x01'
        ModuleData = CMDNUM + CMD1_LEN + CMD

        RES = b'\x00\x00'
        Slot_SN = self.sn
        SlotLen = (len(ModuleData) + len(RES) + len(Slot_SN)).to_bytes(2, 'big')
        SLOT1DATA = SlotLen + Slot_SN + RES + ModuleData

        SlotNum = b'\x01'
        DATA = SlotNum + SLOT1DATA

        FLAG1 = b'\xef\xef\x00'
        VER = b'\x11'
        MFLAG = b'\x00'
        COMM = b'\xff\x02'
        TCOMM = b'\x00\x06'
        HSUM = b'\x00'
        SUM = b'\x00'
        LEN = (len(VER) + len(MFLAG) + len(COMM) + len(TCOMM) + len(HSUM) + len(DATA) + len(SUM)).to_bytes(2, 'big')

        header = FLAG1 + LEN + VER + MFLAG + COMM + TCOMM
        HSUM = (sum(header) & 0xff).to_bytes(1, 'big')
        SUM = (sum(header + HSUM + DATA) & 0xff).to_bytes(1, 'big')
        cmd_created = header + HSUM + DATA + SUM
        return cmd_created

    def parse_data(self, reply: bytes):
        FLAG2 = b'\xfe\xfe\x00'
        l = len(reply)
        flag_found = False
        for i in range(l):
            flag = reply[i:(i + 3)]
            if flag == FLAG2:
                flag_found = True
                LEN = (reply[(i + 3)] << 8) + reply[(i + 4)]
                CMD_return = reply[(i):(i + 5 + LEN)]
                Reply_Data = CMD_return[12:(4 + LEN)]
                if Reply_Data[0] != 0xff:
                    SLOT1DATA = Reply_Data[1:]
                    SlotLen = (SLOT1DATA[0] << 8) + SLOT1DATA[1]
                    Slot_SN = SLOT1DATA[2:8]
                    SLOT_SNXX_CMDREly = SLOT1DATA[10:]
                    if SLOT_SNXX_CMDREly[0] != 0xff:
                        CMDNUM = SLOT_SNXX_CMDREly[0]
                        CMD1_LEN = (SLOT_SNXX_CMDREly[1] << 8) + SLOT_SNXX_CMDREly[2]
                        CMD1_DATA = SLOT_SNXX_CMDREly[3:]
                        return CMD1_DATA
                    else:
                        return b''
                else:
                    return b''
        if not flag_found:
            return b''

    def close(self):
        if self.s:
            self.s.close()


class OSW_Board(object):

    def __init__(self, ip='***************', sn=0x0003D0114002):
        self.ip = ip
        self.sn = sn.to_bytes(6, 'big')
        self.port = 9000
        self.s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    def switch_num(self, num):
        cmd_created = self.generate_cmd(num)
        self.s.sendto(cmd_created, (self.ip, self.port))
        reply = self.s.recv(1024)
        l = len(reply)
        if reply[-3:-2] == num.to_bytes(1, 'big'):
            for i in range(l):
                if reply[i:i + 1] == b'\x92' and reply[i + 1:i + 2] == b'\x92':
                    my_sig.infoSignal.emit(-1,'OSW通道设置为 %d 成功' % num)

    def generate_cmd(self, num):
        ADD = b'\xEF\xEF\x00\x00\x23\x11\x00\xFF\x02\x00\x0C\x1F\x01\x00\x18\x00\x03\xD0\x11\x40\x02\x00\x00\x01\x00\x0D'
        FLAG1 = b'\xEF\xEF'  # 命令标识 (0xFEFE) 	2 bytes
        LEN1 = b'\x0a'  # len1后包括res数据长度：LEN1+RES*256
        RES = b'\x00'  # LEN高字节部分
        COMMAND = b'\x92'  # 命令码
        OptType = b'\x01'  # 操作类型：0-读取；1-设置
        AGREEMENT = b'\x0A\x0A'
        OswType = b'\x01'
        Channel = num.to_bytes(1, 'big')
        LEN2 = (len(AGREEMENT) + len(OswType) + len(Channel)).to_bytes(2, 'big')
        DATA = LEN2 + AGREEMENT + OswType + Channel
        cmd_bytes = FLAG1 + LEN1 + RES + COMMAND + OptType + DATA
        SUM = (sum(cmd_bytes) & 0xff).to_bytes(1, 'big')
        cmd_bytes += SUM

        final_bytes = ADD + cmd_bytes
        sum_total = (sum(final_bytes) & 0xff).to_bytes(1, 'big')
        final_bytes += sum_total
        return final_bytes


class OLPB_Board(object):
    # OLPB-V
    mode_dict = {0: b'\x70', 1: b'\x80'}
    mode_disp_dict = {b'\x50': '手动', b'\x70': '主路', b'\x80': '从路'}

    def __init__(self, ip='***************', sn=0x00031712c003):
        self.ip = ip
        self.sn = sn.to_bytes(6, 'big')
        self.port = 9000
        self.s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.status = 0
        self.set_mode(b'\x50')
        time.sleep(0.1)

    def set_olp(self, *status):
        '''
        status: 0为主， 1为从
        :return:
        '''
        if len(status) != 0:
            mode = self.mode_dict[status[0]]
        else:
            i = 1 - self.status
            mode = self.mode_dict[i]
        if self.set_mode(mode):
            if len(status) != 0 and self.status != status[0]:
                time.sleep(1)
                self.status = status[0]
            else:
                self.status = 1 - self.status
                time.sleep(1)


    def set_mode(self, mode):
        cmd = b'\x03'
        flag = b'\xef'
        Len = b'\x1e'
        code = b'\x20'
        rest = b'\x15\x00\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\xfd\xa8\x00\x00\x00\x00\x00'
        SUM = (sum(flag + Len + code + mode + rest) & 0xff).to_bytes(1, 'big')
        cmd_send = cmd + flag + Len + code + mode + rest + SUM
        cmd_created = self.generate_cmd(cmd_send)
        for t in range(3):  #加重试
            if t != 0:
                my_sig.infoSignal.emit(-1,'重试...')
            print_byte('send: ', cmd_created)
            self.s.sendto(cmd_created, (self.ip, self.port))
            reply = self.s.recv(1024)
            print_byte('recv: ', reply)
            cmd_return = self.parse_data(reply)
            if cmd_return == b'\x03\x53':
                my_sig.infoSignal.emit(-1,'%s(%s)设置为%s 成功' % (self.__class__.__name__, self.ip, self.mode_disp_dict[mode]))
                return True
            elif cmd_return == b'\x03\x46':
                my_sig.infoSignal.emit(-1,'%s(%s)设置为%s 失败' % (self.__class__.__name__, self.ip, self.mode_disp_dict[mode]))
            else:
                my_sig.infoSignal.emit(-1,'%s(%s)设置为%s 返回值异常' % (self.__class__.__name__, self.ip, self.mode_disp_dict[mode]))
        return False


    # def get_olp(self, *num):
    #     cmd = b'\x04'
    #     flag = b'\xef'
    #     Len = b'\x03'
    #     code = b'\x12'
    #     Num = b'\x01'
    #     SUM = (sum(flag + Len + code + Num) & 0xff).to_bytes(1, 'big')
    #     cmd_send = cmd + flag + Len + code + Num + SUM
    #     cmd_created = self.generate_cmd(cmd_send)
    #     print_byte('send:', cmd_created)
    #
    #     for _ in range(10):
    #         self.s.sendto(cmd_created, (self.ip, self.port))
    #         reply = self.s.recv(1024)
    #         cmd_return = self.parse_data(reply)
    #         print_byte('recv:', cmd_return)
    #         if len(cmd_return) != 1:
    #             mode = cmd_return[3]
    #             if mode in self.mode_disp_dict:
    #                 print('OLPB(%s)查询到状态为 %s' % (self.ip, self.mode_disp_dict[self.status]))
    #             else:
    #                 print('未知状态')
    #                 return -1
    #             if len(num) == 0:
    #                 return self.status
    #             break
    #     else:
    #         return self.status

    def generate_cmd(self, CMD: bytes):
        CMD1_LEN = len(CMD).to_bytes(2, 'big')
        CMDNUM = b'\x01'
        ModuleData = CMDNUM + CMD1_LEN + CMD

        RES = b'\x00\x00'
        Slot_SN = self.sn
        SlotLen = (len(ModuleData) + len(RES) + len(Slot_SN)).to_bytes(2, 'big')
        SLOT1DATA = SlotLen + Slot_SN + RES + ModuleData

        SlotNum = b'\x01'
        DATA = SlotNum + SLOT1DATA

        FLAG1 = b'\xef\xef\x00'
        VER = b'\x11'
        MFLAG = b'\x00'
        COMM = b'\xff\x02'
        TCOMM = b'\x00\x06'
        HSUM = b'\x00'
        SUM = b'\x00'
        LEN = (len(VER) + len(MFLAG) + len(COMM) + len(TCOMM) + len(HSUM) + len(DATA) + len(SUM)).to_bytes(2, 'big')

        header = FLAG1 + LEN + VER + MFLAG + COMM + TCOMM
        HSUM = (sum(header) & 0xff).to_bytes(1, 'big')
        SUM = (sum(header + HSUM + DATA) & 0xff).to_bytes(1, 'big')
        cmd_created = header + HSUM + DATA + SUM
        return cmd_created

    def parse_data(self, reply: bytes):
        FLAG2 = b'\xfe\xfe\x00'
        l = len(reply)
        flag_found = False
        for i in range(l):
            flag = reply[i:(i + 3)]
            if flag == FLAG2:
                flag_found = True
                LEN = (reply[(i + 3)] << 8) + reply[(i + 4)]
                CMD_return = reply[(i):(i + 5 + LEN)]
                Reply_Data = CMD_return[12:(4 + LEN)]
                if Reply_Data[0] != 0xff:
                    SLOT1DATA = Reply_Data[1:]
                    SlotLen = (SLOT1DATA[0] << 8) + SLOT1DATA[1]
                    Slot_SN = SLOT1DATA[2:8]
                    SLOT_SNXX_CMDREly = SLOT1DATA[10:]
                    if SLOT_SNXX_CMDREly[0] != 0xff:
                        CMDNUM = SLOT_SNXX_CMDREly[0]
                        CMD1_LEN = (SLOT_SNXX_CMDREly[1] << 8) + SLOT_SNXX_CMDREly[2]
                        CMD1_DATA = SLOT_SNXX_CMDREly[3:]
                        return CMD1_DATA
                    else:
                        return b''
                else:
                    return b''
        if not flag_found:
            return b''


class OLP_Board(object):
    # OLP
    mode_dict = {0: b'\x70', 1: b'\x80'}
    mode_disp_dict = {b'\x50': '手动', b'\x70': '主路', b'\x80': '从路'}

    def __init__(self, ip='**************', sn=0x0203c0a81678):
        self.ip = ip
        self.sn = sn.to_bytes(6, 'big')
        self.port = 9000
        self.s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.status = 0
        self.set_mode(b'\x50')
        time.sleep(0.1)

    def set_olp(self, *status):
        '''
        status: 0为主， 1为从
        :return:
        '''
        if len(status) != 0:
            mode = self.mode_dict[status[0]]
        else:
            i = 1 - self.status
            mode = self.mode_dict[i]

        if self.set_mode(mode):
            if len(status) != 0:
                self.status = status[0]
            else:
                self.status = 1 - self.status
        time.sleep(0.1)

    def set_mode(self, mode):
        cmd = b'\x03'
        flag = b'\xef'
        Len = b'\x1e'
        code = b'\x20'

        rest = b'\x00\x00\xFE\x0C\xFF\xA6\xFF\x9C\xFF\x4C\xFF\x60\xFF\x4C\xFF\x42\xFF\x92\x00\x00\x00\xB2\x00\x00\x00\x00\x00'
        SUM = (sum(flag + Len + code + mode + rest) & 0xff).to_bytes(1, 'big')
        cmd_send = cmd + flag + Len + code + mode + rest + SUM
        cmd_created = self.generate_cmd(cmd_send)
        for t in range(3):
            if t != 0:
                my_sig.infoSignal.emit(-1,'重试...')
            # print_byte('send: ', cmd_created)
            self.s.sendto(cmd_created, (self.ip, self.port))
            reply = self.s.recv(1024)
            cmd_return = self.parse_data(reply)
            if cmd_return == b'\x03\x53':
                my_sig.infoSignal.emit(-1,'%s(%s)设置为%s 成功' % (self.__class__.__name__, self.ip, self.mode_disp_dict[mode]))
                return True
            elif cmd_return == b'\x03\x46':
                my_sig.infoSignal.emit(-1,'%s(%s)设置为%s 失败' % (self.__class__.__name__, self.ip, self.mode_disp_dict[mode]))
            else:
                my_sig.infoSignal.emit(-1,'%s(%s)设置为%s 返回值异常' % (self.__class__.__name__, self.ip, self.mode_disp_dict[mode]))
        return False

    def generate_cmd(self, CMD: bytes):
        CMD1_LEN = len(CMD).to_bytes(2, 'big')
        CMDNUM = b'\x01'
        ModuleData = CMDNUM + CMD1_LEN + CMD

        RES = b'\x00\x00'
        Slot_SN = self.sn
        SlotLen = (len(ModuleData) + len(RES) + len(Slot_SN)).to_bytes(2, 'big')
        SLOT1DATA = SlotLen + Slot_SN + RES + ModuleData

        SlotNum = b'\x01'
        DATA = SlotNum + SLOT1DATA

        FLAG1 = b'\xef\xef\x00'
        VER = b'\x11'
        MFLAG = b'\x00'
        COMM = b'\xff\x02'
        TCOMM = b'\x00\x06'
        HSUM = b'\x00'
        SUM = b'\x00'
        LEN = (len(VER) + len(MFLAG) + len(COMM) + len(TCOMM) + len(HSUM) + len(DATA) + len(SUM)).to_bytes(2, 'big')

        header = FLAG1 + LEN + VER + MFLAG + COMM + TCOMM
        HSUM = (sum(header) & 0xff).to_bytes(1, 'big')
        SUM = (sum(header + HSUM + DATA) & 0xff).to_bytes(1, 'big')
        cmd_created = header + HSUM + DATA + SUM
        return cmd_created

    def parse_data(self, reply: bytes):
        FLAG2 = b'\xfe\xfe\x00'
        l = len(reply)
        flag_found = False
        for i in range(l):
            flag = reply[i:(i + 3)]
            if flag == FLAG2:
                flag_found = True
                LEN = (reply[(i + 3)] << 8) + reply[(i + 4)]
                CMD_return = reply[(i):(i + 5 + LEN)]
                Reply_Data = CMD_return[12:(4 + LEN)]
                if Reply_Data[0] != 0xff:
                    SLOT1DATA = Reply_Data[1:]
                    SlotLen = (SLOT1DATA[0] << 8) + SLOT1DATA[1]
                    Slot_SN = SLOT1DATA[2:8]
                    SLOT_SNXX_CMDREly = SLOT1DATA[10:]
                    if SLOT_SNXX_CMDREly[0] != 0xff:
                        CMDNUM = SLOT_SNXX_CMDREly[0]
                        CMD1_LEN = (SLOT_SNXX_CMDREly[1] << 8) + SLOT_SNXX_CMDREly[2]
                        CMD1_DATA = SLOT_SNXX_CMDREly[3:]
                        return CMD1_DATA
                    else:
                        return b''
                else:
                    return b''
        if not flag_found:
            return b''


class GWS(COM):
    bytes_mode = False

    def __init__(self, com_num: str):
        super(GWS, self).__init__(com_num, 19200)
        self.Connect()
        self.lock = threading.Lock()
        self.highest_temp = 155

    def Connect(self):
        # reply = self.send_recv("1,ROM?")      # 读取设备idn
        # reply = self.execute_cmd("CMD ERR")      # ROM?命令不支持了，随便发个错的命令，能回NA:CMD ERR就说明通信正常
        reply = self.execute_cmd("1,TEMP?")      # 读温度能返回浮点数则认为通信正常
        # if 'NA:CMD ERR' in reply.upper():
        if re.search(r"\d+\.\d+", reply):
            my_sig.infoSignal.emit(-1,'温循箱连接成功: ' + reply)
        else:
            raise Exception('温循箱连接失败')

    def Settempmax(self, tempmax):
        if tempmax != self.highest_temp:
            self.highest_temp = tempmax

    def GetTemp(self):
        # 读取温度，返回实时温度、目标温度
        with self.lock:
            temp = [0] * 2
            reply = self.execute_cmd("1,TEMP?")
            if 'OK' in reply:
                reply = self.execute_cmd("1,TEMP?")
            aaa = reply.strip().split(',')
            temp[0] = float(aaa[0])  # 实时温度
            temp[1] = float(aaa[1])  # 设置的目标温度
            my_sig.sig_debug.emit(f'温循箱实时温度{temp[0]}, 目标温度{temp[1]}')
            return temp

    def SetTemp(self, setVal: float):
        # 设置温度
        with self.lock:
            if setVal > self.highest_temp:
                setVal = self.highest_temp
            if setVal < -55:
                setVal = -55
            cmd = '1,TEMP,S' + str(setVal)
            result = self.execute_cmd(cmd)
            if 'OK' in result:
                my_sig.infoSignal.emit(-1,f'设置温循箱目标温度{setVal}成功')
            else:
                raise Exception('设置温循箱温度失败')

    def OnOff(self, status):
        with self.lock:
            if int(status) == 1:  # 启动命令
                cmd = '1,POWER,ON'
                my_sig.infoSignal.emit(-1,f'开启温循箱')
            else:  # 停止命令
                cmd = '1,POWER,OFF'
                my_sig.infoSignal.emit(-1,f'关闭温循箱')
            result = self.execute_cmd(cmd)
            if 'OK' in result:
                my_sig.infoSignal.emit(-1,'操作温循箱成功')
            else:
                raise Exception('操作温循箱失败')

    def execute_cmd(self, cmd):
        my_sig.sig_debug.emit(f'-> {cmd}')
        result = self.send_recv(cmd)
        my_sig.sig_debug.emit(f'<- {result}')
        return result


def print_byte(hint: str, by: bytes):
    info = hint + ' '
    for x in by:
        info += hex(x)[2:].zfill(2).upper() + ' '
    my_sig.sig_debug.emit(info)


if __name__ == '__main__':
    # olp = OLPB_Board()  # '192.168.129.50',0x000395129016
    # # re = olp.get_olp()
    # olp.set_olp(1)
    # olp.set_olp(0)
    chamber = GWS("COM4")
    chamber.SetTemp(28)
    readTemps = chamber.GetTemp()
    print(f'read temp: {readTemps}')