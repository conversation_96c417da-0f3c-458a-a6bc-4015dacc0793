"""
MCM_Board_Adapter所用到的方法
"""
import configparser
import math
import re

import numpy
from scipy import optimize
from scipy.special import erfcinv

from ..utils import module_comm
from ..utils.MySignal import my_sig
import struct
from ..utils.mylog import logger

exitFlag = False

def bytes2str(packet: bytes) -> str:
    result = ""
    for b in packet:
        result += "%02x" % b
    return result.upper()


def bytes2int(packet: bytes) -> int:
    result = ""
    for b in packet:
        result += "%02x" % b
    return int(result, 16)


def get_str_args(args):
    strargs = ''
    for a in args:
        if isinstance(a, int):
            strargs += hex(a)  # int都用十六进制打印
        elif isinstance(a, bytes):
            strargs += '0x' + bytes2str(a)
        else:
            strargs += f'{a}'
        strargs += ','
    return '(' + strargs + ')'


def setbyteslittle(b: bytes):
    leng = len(b)
    if leng == 1:
        return b
    res = b''
    for i in range(int(leng / 2)):
        r = reverse2byte(b[i * 2:i * 2 + 2])
        res += r
    return res


def reverse2byte(b: bytes):
    return b[1:] + b[:1]


def ints2hexstr(vals: list) -> str:
    result = ""
    for val in vals:
        result += hex(val) + " "
    return result


def exit_capture():
    global exitFlag
    exitFlag = True


def get_exit_flag():
    global exitFlag
    return exitFlag


def signed_num(num, bit):
    if bit <= 32:
        sign = num >> (bit - 1)
        if sign == 0:
            num = num
        else:
            num = -((num ^ (0xFFFFFFFF >> (32 - bit))) + 1)
    else:
        num = 0
        print('error:bit > 32')
    return num

def to_signed_num(num, bit):
    if num < 0:
        return (1 << bit) + num
    else:
        return num

def hex_to_float(h: str):
    # 十六进制字符串（4字节）转IEEE754浮点数（单精度）
    # my_sig.infoSignal.emit(-1,f'hex_to_float: {h}')
    i = int(h, 16)
    f = struct.unpack('<f', struct.pack('<I', i))[0]
    my_sig.infoSignal.emit(-1,f'hex_to_float: {h} = {f}')
    return f


def float2hex(num):
    bits, = struct.unpack('!I', struct.pack('!f', num))
    logger.debug(f'float2hex: {num} : {hex(bits)}')
    return bits


def vcc_offset(sv, mv):
    mv = float(mv)
    sv = float(sv)
    offset = abs(sv - mv)  # round(sv - mv, 8)  # 待确认是否取绝对值
    my_sig.infoSignal.emit(-1,f'sv: {sv}, mv: {mv}, vcc_offset: {offset}')
    up_lmt = sv * 0.04
    if not 0 <= offset <= up_lmt:
        return 1, f'offset:{offset} out of range 0~{up_lmt}'
    return offset

def negate(a):
    a = float(a)
    return -a


def RXPD(PIC_RXPDXY_HIGH_raw, PIC_RXPDXY_HIGH, PIC_RXPDXY_LOW):
    if int(PIC_RXPDXY_HIGH_raw) < 61440:
        res = float(PIC_RXPDXY_HIGH)
    else:
        res = float(PIC_RXPDXY_LOW)
    return res


def average(*params):
    cnt = len(params)
    sum = 0
    for ele in params:
        sum += float(ele)
    return sum / cnt


def str2mcmval(strval: str):
    if re.search('^0x[0-9a-f]+$', strval, re.IGNORECASE):  # 0x开头，整数
        return int(strval, 16)
    # elif re.search('^[+-]?\d+(\.\d+)?$', strval):  # 纯数字、有小数点都作为浮点数处理
    elif re.search('^[+-]?\d+(\.\d+)?(e[-]?\d+)?$', strval, re.IGNORECASE):  # 纯数字、有小数点或科学计数法都作为浮点数处理
        fval = float(strval)
        return float2hex(fval)
    else:
        return strval


def bit_cutout_unsigned(num, bit_h, bit_l):
    # 如bit_h: 13, bit_l: 8
    leng = bit_h - bit_l + 1
    unsign_val = (num >> bit_l) & (2 ** leng - 1)
    return unsign_val


def cfg_set_option(section, option, val):
    cfg_path = './data.ini'
    dco_config = configparser.ConfigParser()
    dco_config.read(cfg_path, encoding="utf-8-sig")
    dco_config.set(section, option, f'{val}')
    with open(cfg_path, "w", encoding="utf-8-sig") as f:
        dco_config.write(f)
    return dco_config


def init_data():
    dco_config = configparser.ConfigParser()
    dco_config.read('./data.ini', encoding="utf-8-sig")
    return dco_config


def RFPD_LOSS_OBS(rfpd, itla_power):
    # 预校准第4步得到该值，第8步依赖该值，存在文件中，方便第8步使用
    # RFPD_LOSS_OBS = [10*lg(RFPD)] -  ITLA_POWER
    rfpd = float(rfpd)
    itla_power = float(itla_power)
    rfpd_loss_obs = 10 * math.log10(rfpd) - itla_power
    config = init_data()
    config = cfg_set_option("property", "rfpd_loss_obs", rfpd_loss_obs)
    return rfpd_loss_obs


def subtract(a, b):
    # 计算a-b
    a = float(a)
    b = float(b)
    return a - b

def bit_cutout_signed(num, bit_h, bit_l):
    # 如bit_h: 13, bit_l: 8
    leng = bit_h - bit_l + 1
    unsign_val = (num >> bit_l) & (2 ** leng - 1)
    if unsign_val >= 2 ** (leng - 1):  # 最高位是1
        sign_val = unsign_val - 2 ** leng
    else:
        sign_val = unsign_val
    return sign_val


def ratio(a, b):
    # 计算a/b
    a = float(a)
    b = float(b)
    return a / b


def val_to_str(val):
    if isinstance(val, str):
        val = val.strip()
    else:
        val = str(val)
    return val


def str2val(strval):
    # 字符串转数值，输入必须是十六进制字符串/十进制字符串/浮点数字符串
    if not isinstance(strval, str):
        return strval
    if re.search('^0x[0-9a-f]+$', strval, re.IGNORECASE):  # 0x开头，十六进制数
        return int(strval, 16)
    elif re.search('^[0-9a-f]+h$', strval, re.IGNORECASE):  # h结尾，十六进制数
        return int(strval[:-1], 16)
    elif re.search('^[+-]?\d+$', strval):  # 整数
        return int(strval)
    else:
        if ".0" in strval and strval[-2:] == '.0':  # xlrd读excel会把整数后面加.0
            return int(strval[:-2])
        return float(strval)


def BER_to_Q(BER):
    Q_factor = 20 * math.log10(2 ** 0.5 * erfcinv(2 * BER))
    return Q_factor


def linear_fit(x, y):
    def fun(z, a, b):
        return a * z + b

    slope, offset = optimize.curve_fit(fun, x, y)[0]
    x_2_plot = x
    y_2_plot = y
    m_2_plot = numpy.linspace(x[0] - 1, x[-1] + 1, 100)
    n_2_plot = [fun(v, slope, offset) for v in m_2_plot]
    return float(slope), float(offset)


def generate_hex_param(*args):
    re = [0]
    re += [float2hex(x) for x in args]
    return re


def X16_BUF_TO_X8_BUF(buf_16: list):
    buf_8: list = []
    for i in buf_16:
        buf_8.append(i & 0xFF)
        buf_8.append((i >> 8) & 0xFF)
    return buf_8

def X8_BUF_TO_BYTES(buf_8: list):
    text_bytes: bytes = b''
    for i in buf_8:
        text_bytes += struct.pack('B', i)
    return text_bytes


def BYTES_TO_X8_BUF(text_bytes: bytes):
    buf_8: list = []
    for i in text_bytes:
        buf_8.append(i)
    return buf_8

def quadratic_fit(x, y):
    def fun(z, a, b, c):
        return a * z ** 2 + b * z + c

    a, b, c = optimize.curve_fit(fun, x, y)[0]
    x_2_plot = x
    y_2_plot = y
    m_2_plot = numpy.linspace(x[0] - 1, x[-1] + 1, 100)
    n_2_plot = [fun(v, a, b, c) for v in m_2_plot]
    return float(a), float(b), float(c)

def get_str_args_list(args):
    strargs = ''
    for a in args:
        if isinstance(a, int):
            strargs += hex(a)  # int都用十六进制打印
        elif isinstance(a, bytes):
            strargs += '0x' + bytes2str(a)
        elif isinstance(a, str):
            strargs += f'\'{a}\''
        else:
            strargs += f'{a}'
        strargs += ','
    return '[' + strargs[:-1] + ']'
