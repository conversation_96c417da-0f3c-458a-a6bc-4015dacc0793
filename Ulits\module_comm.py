#!/usr/bin/python
# -*- coding: UTF-8 -*-

from ..utils.bytes_helper import BytesHelper


# 批量commands组包
# def compose_packet_for_mcm_commands(commands: str):
#     helper = BytesHelper()
#     commands = eval(commands)
#     helper.add_int_d(len(commands), 1)  # CMDNUM
#     for command in commands:
#         command = eval(command)
#         module_bytes = compose_mcm_config_packet(command[0], command[1], command[2], command[3])
#         helper.add_int_d(len(module_bytes), 2)
#         helper.add_bytes(module_bytes, len(module_bytes))
#
#     return helper.get_packet()


# 设置单盘命令0xFF02对应模块组包（MCM模块测试命令）
def compose_mcm_packet(action: str, command: str, value: list):
    helper = BytesHelper()

    # below is command
    helper.add_string(command.upper(), 28)

    # below is parameter
    space_used = 0
    space = 10
    for v in value:
        if isinstance(v, int):
            helper.add_int_l(v, 4)
            space_used += 1
        elif isinstance(v, float):
            helper.add_float(v, 4)
            space_used += 1
        elif isinstance(v, str):
            helper.add_string(v.upper(), 16)
            space_used += 4
        else:
            raise TypeError('MCM 命令参数类型错误')
    helper.add_bytes(b'', 4 * (space - space_used))
    helper.reverse()

    # below is header
    if action.lower() == "set":
        diagnostic_operation = b'\x01'
    elif action.lower() == "get":
        diagnostic_operation = b'\x02'
    elif action.lower() == "eset":
        diagnostic_operation = b'\x03'
    elif action.lower() == "eget":
        diagnostic_operation = b'\x04'
    else:
        raise RuntimeError("action is invalid")
    return diagnostic_operation + helper.get_packet()


def compose_set_tx_enable_inpara(is_enable):
    helper = BytesHelper()
    param = 0x00 if is_enable else 0x01
    helper.add_int_d(param, 1)
    return helper.get_packet()


def compose_get_tx_enable_inpara():
    return b''


def compose_get_reg_inpara(addr, cnt):
    helper = BytesHelper()
    helper.add_int_l(addr, 2)  # 待确认是add_int_l还是add_int_d
    helper.add_int_l(cnt, 2)
    return helper.get_packet()


def compose_get_qdd_reg_no_page_inpara(addr, cnt):
    # tbd
    helper = BytesHelper()
    helper.add_int_l(addr, 1)  # 待确认是add_int_l还是add_int_d
    helper.add_int_l(cnt, 1)
    return helper.get_packet()

def compose_get_qdd_reg_inpara(page, addr, cnt):
    # tbd
    helper = BytesHelper()
    helper.add_int_l(page, 1)  # 待确认是add_int_l还是add_int_d
    helper.add_int_l(addr, 1)  # 待确认是add_int_l还是add_int_d
    helper.add_int_l(cnt, 1)
    return helper.get_packet()

def compose_get_qdd_reg_inpara_with_bank(bank, page, addr, cnt):
    # tbd
    helper = BytesHelper()
    helper.add_int_l(bank, 1)
    helper.add_int_l(page, 1)  # 待确认是add_int_l还是add_int_d
    helper.add_int_l(addr, 1)  # 待确认是add_int_l还是add_int_d
    helper.add_int_l(cnt, 1)
    return helper.get_packet()

def compose_set_qdd_reg_inpara(page, addr, cnt, vals):
    helper = BytesHelper()
    helper.add_int_l(page, 1)
    helper.add_int_l(addr, 1)
    helper.add_int_l(cnt, 1)
    helper.add_bytes(vals, cnt)  # 待确认是否改add_bytes_l
    return helper.get_packet()


def compose_set_qdd_reg_inpara_intlst(page, addr, cnt, vals: list):
    helper = BytesHelper()
    helper.add_int_l(page, 1)
    helper.add_int_l(addr, 1)
    helper.add_int_l(cnt, 1)
    for ele in vals:
        helper.add_int_l(ele, 1)
    return helper.get_packet()


# 根据命令和参数值生成payload寄存器的数据流
def compose_qdd_mcm_packet(action: str, command: str, value: list):
    helper = BytesHelper()
    # below is header
    helper.add_bytes(b'\x9e\x2e\x00\x00', 4)
    if action.lower() == "set":
        diagnostic_operation = 1
    elif action.lower() == "get":
        diagnostic_operation = 2
    elif action.lower() == "eset":
        diagnostic_operation = 3
    elif action.lower() == "eget":
        diagnostic_operation = 4
    else:
        raise RuntimeError("action is invalid")
    helper.add_int_l(diagnostic_operation, 2)
    helper.add_bytes(b'\x00\x00', 2)

    # below is command
    helper.add_string(command.upper(), 28)

    # below is parameter
    space_used = 0
    space = 30
    for v in value:
        if isinstance(v, int):
            helper.add_int_l(v, 4)
            space_used += 1
        elif isinstance(v, float):
            helper.add_float(v, 4)
            space_used += 1
        elif isinstance(v, str):
            helper.add_string(v, 16)
            space_used += 4
        else:
            raise TypeError('MCM 命令参数类型错误')
    helper.add_bytes(b'', 4 * (space - space_used))
    return helper.get_packet()


def compose_set_vega_mode_inpara(interface_no: int, mode: int):
    helper = BytesHelper()
    helper.add_int_l(interface_no, 1)
    helper.add_int_l(mode, 1)
    return helper.get_packet()


def compose_get_vega_mode_inpara(interface_no: int):
    helper = BytesHelper()
    helper.add_int_l(interface_no, 1)
    return helper.get_packet()


def compose_set_reg_inpara(addr, cnt, data):
    helper = BytesHelper()
    helper.add_int_l(addr, 2)
    helper.add_int_l(cnt, 2)
    helper.add_bytes_l(data, 2 * cnt)
    return helper.get_packet()


def compose_set_reg_inpara_intlst(addr, cnt, vals: list):
    helper = BytesHelper()
    if type(addr)==str:
        if addr.startswith('0x'):
            addr = int(addr[2:],16)
    helper.add_int_l(addr, 2)
    helper.add_int_l(cnt, 2)
    for ele in vals:
        helper.add_int_l(ele, 2)
    return helper.get_packet()


def compose_sla_fifo_write_inpara(vals: list):
    # vals中为需要写16次寄存器0x9F82、0x9F83的数据，共32个值，下位机写完这32次寄存器后，最后会再写寄存器0x9F84值0x00F7
    helper = BytesHelper()
    helper.add_int_l(0, 2)
    helper.add_int_l(0, 2)
    for ele in vals:
        helper.add_int_l(ele, 2)
    return helper.get_packet()


def compose_set_low_power_inpara(is_enable):
    helper = BytesHelper()
    param = 0x01 if is_enable else 0x00
    helper.add_int_d(param, 1)
    return helper.get_packet()


def compose_set_reset_inpara():
    helper = BytesHelper()
    helper.add_int_d(0x00, 1)
    return helper.get_packet()


def compose_set_wl_inpara(wl_type, rx_wl_ch, tx_wl_ch):
    # 设置波长命令的入参组包，wl_type: 1表示Rx波长，2表示Tx波长，3表示两者
    helper = BytesHelper()
    helper.add_int_d(wl_type, 1)
    helper.add_int_l(int(rx_wl_ch * 1000), 2)
    helper.add_int_l(int(tx_wl_ch * 1000), 2)
    helper.add_int_l(0, 4)
    return helper.get_packet()


def compose_get_wl_inpara(wl_type):
    # 读取波长命令的入参组包，wl_type: 1表示Rx波长，2表示Tx波长，3表示两者
    helper = BytesHelper()
    helper.add_int_d(wl_type, 1)
    return helper.get_packet()


def compose_get_pwr_diss_inpara():
    return b''


def compose_sla_fifo_write_new_inpara(addr, vals: list):
    # vals中为需要写x次寄存器0x9F82、0x9F83的数据，共x*2个值，下位机写完这32次寄存器后，最后会再写寄存器0x9F84值0x00F7
    helper = BytesHelper()
    addr_h = (addr >> 16) & 0xFFFF
    addr_l = addr & 0xFFFF
    helper.add_int_l(addr_h, 2)
    helper.add_int_l(addr_l, 2)
    for ele in vals:
        helper.add_int_l(ele, 2)
    return helper.get_packet()


def compose_sla_consec_read_inpara(addr, cnt):
    helper = BytesHelper()
    addr_h = (addr >> 16) & 0xFFFF
    addr_l = addr & 0xFFFF
    helper.add_int_l(addr_h, 2)
    helper.add_int_l(addr_l, 2)
    helper.add_int_l(cnt, 2)
    return helper.get_packet()


def compose_spim0_cmd_inpara(rflag, wflag, pCmd, cmdLength, pData, dataLength):
    # 用于实现厂家固件烧录脚本中IssueSpim0Command()函数，入参保持一致
    helper = BytesHelper()
    helper.add_int_d(rflag, 1)
    helper.add_int_d(wflag, 1)
    helper.add_bytes_l(pCmd, 8)
    helper.add_int_d(cmdLength, 1)
    helper.add_int_l(dataLength, 2)
    if pData is None:
        pData = b''
    helper.add_bytes_l(pData, 256)  # 待确认如果pData是None有没有问题
    return helper.get_packet()


subCmdDic = {}  # （模块操作命令类型（2byte）， 生成的InPara长度1， 对输入参数的操作函数）
subCmdDic["set_tx_enable"] = (
    0x4001, 1, compose_set_tx_enable_inpara)  # 设置Tx激光器开关命令 MdDevSubCmdType为0x40, MdDevSubCmdId为0x01; InPara长度1,
subCmdDic["get_tx_enable"] = (
    0x0001, 0, compose_get_tx_enable_inpara)  # 读取Tx激光器开关状态命令 MdDevSubCmdType为0x00, MdDevSubCmdId为0x01; InPara长度0,
subCmdDic["set_low_power"] = (0x4002, 1, compose_set_low_power_inpara)  # 设置模块低功耗模式
subCmdDic["set_reset"] = (0x4003, 1, compose_set_reset_inpara)  # 设置模块Reset
subCmdDic["set_wavelength"] = (0x4004, 9, compose_set_wl_inpara)  # 设置激光器波长
subCmdDic["get_wavelength"] = (0x0004, 1, compose_get_wl_inpara)  # 读取激光器波长
subCmdDic["get_power_dissipation"] = (0x0032, 0, compose_get_pwr_diss_inpara)  # 读取DCO功耗
subCmdDic["get_power_dissipation_qdd"] = (0x0019, 0, compose_get_pwr_diss_inpara)  # 读取DCO功耗

# subCmdDic["set_port_enalble"] = (0x400A, 1)  # 设置端口开关
# subCmdDic["get_port_enalble"] = (0x000A, 1)  # 读取端口开关
subCmdDic["get_reg"] = (0x0201, 4, compose_get_reg_inpara)  # 读取REG
subCmdDic["set_reg"] = (0x4202, 0, compose_set_reg_inpara)  # 写REG，值通过bytes传入（入参长度不固定，执行命令时计算）
subCmdDic["set_regs"] = (0x4202, 0, compose_set_reg_inpara_intlst)  # 写REG，值通过intlist传入（入参长度不固定，执行命令时计算）
subCmdDic["send_mcm"] = (0x4101, 69, compose_mcm_packet)

subCmdDic["sla_fifo_write"] = (
    0x420B, 68, compose_sla_fifo_write_inpara)  # FOR_MCM_SW_LOAD，写16次寄存器0x9F82和0x9F83，inpara长度：4+2*2*16
subCmdDic["sla_fifo_write_new"] = (0x420D, 0, compose_sla_fifo_write_new_inpara)  # FOR_MCM_SW_LOAD，最多写256次寄存器0x9F82和0x9F83，inpara长度：4+2*2*16——长度不固定，执行命令时计算
subCmdDic["sla_consec_read"] = (0x420E, 6, compose_sla_consec_read_inpara)  # FOR_MCM_SW_READ，读0x9F82和0x9F83
subCmdDic["spim0_cmd"] = (0x420F, 269, compose_spim0_cmd_inpara)  # flash固件烧录专用，inpara长度固定269

subCmdDic["set_vega_mode"] = (0x4003, 2, compose_set_vega_mode_inpara)
subCmdDic["get_vega_mode"] = (0x0003, 1, compose_get_vega_mode_inpara)

subCmdDic["get_qdd_reg_no_page"] = (0x0201, 2, compose_get_qdd_reg_no_page_inpara)
# subCmdDic["set_qdd_reg_no_page"] = (0x4202, 0, compose_set_qdd_reg_no_page_inpara)
# subCmdDic["set_qdd_regs_no_page"] = (0x4202, 0, compose_set_qdd_reg_inpara_intlst_no_page)
# subCmdDic["iic_bias_write"] = (0x4221, 0, compose_iic_write)
# subCmdDic["iic_bias_read"] = (0x0220, 2, compose_iic_read)
subCmdDic["get_qdd_reg"] = (0x0203, 3, compose_get_qdd_reg_inpara)
subCmdDic["get_qdd_reg_with_bank"] = (0x0205, 4, compose_get_qdd_reg_inpara_with_bank)
subCmdDic["set_qdd_reg"] = (0x4204, 0, compose_set_qdd_reg_inpara)
subCmdDic["set_qdd_regs"] = (0x4204, 0, compose_set_qdd_reg_inpara_intlst)
# subCmdDic["set_qdd_regs_with_bank"] = (0x4206, 0, compose_set_qdd_reg_inpara_intlst_with_bank)
subCmdDic["send_mcm_qdd"] = (0x4101, 156, compose_qdd_mcm_packet)
# # 设置REG命令单独做？InPara长度不固定
# # subCmdDic["set_reg"] = (0x4202, 1)  # 设置REG
#
# subCmdDic["set_manage_operate_led"] = (0x400B, 3)  # 设置端口管理、运行、LED状态
# subCmdDic["set_client_port_loopback"] = (0x400C, 3)  # 设置Client端口环回
# subCmdDic["set_line_port_loopback"] = (0x400D, 3)  # 设置Line端口环回
# subCmdDic["get_line_port_loopback"] = (0x000D, 1)  # 读取Line端口环回
# subCmdDic["set_line_port_operate"] = (0x400E, 3)  # 设置Line端口Operation Mode
# subCmdDic["get_line_port_operate"] = (0x000E, 1)  # 读取Line端口Operation Mode
# subCmdDic["set_tti_insert_val"] = (0x400F, 69)  # 设置端口TTI发送值
# subCmdDic["get_tti_insert_val"] = (0x000F, 5)  # 读取端口TTI发送值
# subCmdDic["set_tti_expect_val"] = (0x4010, 69)  # 设置端口Expect TTI
# subCmdDic["get_tti_expect_val"] = (0x0010, 5)  # 读取端口Expect TTI
# subCmdDic["get_received_tti_val"] = (0x0011, 5)  # 读取端口TTI接收值
# subCmdDic["set_delay_measure"] = (0x4012, 6)  # 设置时延测量
# subCmdDic["get_delay_meas_cfg"] = (0x0012, 2)  # 读取时延测量配置
# subCmdDic["get_delay_meas_result"] = (0x0013, 2)  # 读取时延测量结果
# subCmdDic["set_prbs"] = (0x4001, 1)  # 设置PRBS
# subCmdDic["get_prbs_cfg"] = (0x0014, 2)  # 读取PRBS配置
# subCmdDic["get_prbs_result"] = (0x0015, 2)  # 读取PRBS结果
# subCmdDic["set_lldp_enalble"] = (0x4016, 2)  # LLDP开关设置
# subCmdDic["get_lldp_enalble"] = (0x0016, 0)  # 设置Tx激光器开关
# subCmdDic["set_port_tti_mode"] = (0x4017, 6)  # 设置端口TTI模式
# subCmdDic["get_port_tti_mode"] = (0x0017, 3)  # 读取端口TTI模式
# subCmdDic["clear_port_statistics"] = (0x4018, 6)  # 设置端口统计信息清零
# subCmdDic["get_dco_ver"] = (0x0019, 0)  # 读取DCO版本信息
# subCmdDic["set_dco_fw_up_mode"] = (0x401A, 1)  # 设置DCO固件升级模式
# subCmdDic["set_dco_fw_download"] = (0x401B, 1)  # DCO固件下载
# subCmdDic["get_dco_fw_dl_status"] = (0x001B, 1)  # 读取DCO固件下载状态
# subCmdDic["set_dco_fw_run"] = (0x401C, 1)  # DCO固件运行
# subCmdDic["set_dco_fw_cpy"] = (0x401D, 1)  # DCO固件备份
# subCmdDic["set_dco_fw_ctrl"] = (0x401E, 1)  # DCO固件控制


# def compose_vega_cmd_packet(md_dev_no : int, subCmd : str, *args):  #待确认
#     # VEGA芯片命令组包
#     return compose_cmd_packet(md_dev_no, 0x0011, subCmd, *args)
#
#
# def compose_module_cmd_packet(md_dev_no: int, subCmd: str, *args):
#     return compose_cmd_packet(md_dev_no, 0x0008, subCmd, *args)
#
#
# def qdd_compose_module_cmd_packet(md_dev_no: int, subCmd: str, *args):
#     return compose_cmd_packet(md_dev_no, 0x0009, subCmd, *args)


def compose_cmd_packet(md_dev_uid: int, md_dev_no: int, subCmd: str, *args):
    if subCmd not in subCmdDic:
        raise RuntimeError(f"subCmdType: {subCmd} is invalid")
    # 组包
    helper = BytesHelper()
    helper.add_int_l(0xEFEF, 2)
    InParaBytesFunc = subCmdDic[subCmd][2]
    # print(f"args: {args}")
    subCmdInParaBytes = InParaBytesFunc(*args)
    length = 9 + len(subCmdInParaBytes)
    helper.add_int_l(length & 0xFF, 1)  # Length_LowByte
    helper.add_int_l(length >> 8, 1)  # Length_HighByte
    helper.add_int_l(0xC0, 1)  # CmdType，目前为0xC0，0xC0表示对模块器件操作
    helper.add_int_l(md_dev_uid, 2)  # MdDevUid Vega芯片的是0x0011
    helper.add_int_l(0, 1)  # res
    helper.add_int_l(md_dev_no, 1)
    helper.add_int_d(subCmdDic[subCmd][0], 2)
    helper.add_bytes(subCmdInParaBytes, len(subCmdInParaBytes))
    helper.add_int_d(helper.check_sum(0, helper.get_position()) & 0xFF, 1)

    return helper.get_packet()


# 开启POLL: EF  EF  0C  00  C1  00  00  00  00  00  00  00  00  00  AB
# 关闭POLL: EF  EF  0C  00  C1  01  00  00  00  00  00  00  00  00  AC
def compose_enablepoll_cmd_packet(bEnable):
    helper = BytesHelper()
    helper.add_int_l(0xEFEF, 2)
    length = 12
    helper.add_int_l(length & 0xFF, 1)  # Length_LowByte
    helper.add_int_l(length >> 8, 1)  # Length_HighByte
    helper.add_int_l(0xC1, 1)  # CmdType，0xC1
    if bEnable:  # 关1开0
        flag = 0
    else:
        flag = 1
    helper.add_int_l(flag, 1)
    helper.add_int_l(0, 8)  # 后面8byte全0
    helper.add_int_d(helper.check_sum(0, helper.get_position()) & 0xFF, 1)
    return helper.get_packet()


# # 设置tx激光器开关
# def compose_set_tx_enable_packet(md_dev_no: int, is_enable):
#     inpara_bytes = compose_set_tx_enable_inpara(is_enable)
#     return compose_module_cmd_packet(md_dev_no, "set_tx_enable", inpara_bytes)


# 单个command组包（设置tx激光器开关等这类命令）
def compose_single_cmd_packet(module_bytes: bytes):
    helper = BytesHelper()
    helper.add_int_d(1, 1)  # CMDNUM
    helper.add_int_d(len(module_bytes), 2)
    helper.add_bytes(module_bytes, len(module_bytes))
    return helper.get_packet()



