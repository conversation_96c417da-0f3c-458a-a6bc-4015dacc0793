#!/usr/bin/python
# -*- coding: UTF-8 -*-
from ..utils.MySignal import my_sig

import threading
from ..utils.dco_utils import *
import re
import queue
import struct
from ..utils import module_comm

TYPE_ID_CFP2 = 0x8
TYPE_ID_QDD = 0x9
TYPE_ID_VEGA = 0x11
board = None
g_board_sn = 0
g_pkt_type = ""
g_src_eth = ""
g_src_mac = ""
g_dest_mac = ""
rcvThreadRun = False
re_list = []
queueLock = threading.Lock()
workQueue = queue.Queue(10)  # 存放接收到的数据包
exitFlag = False  # 退出标志
#EMPTY_RESULT = "result is empty"

# 全局临时变量，记录每次请求开始时间
tmp_request_time = int(time.time())
# 全局临时变量，记录每次命令的长度
tmp_request_len = 0
# 全局临时变量，可能有多条命令，用list保存结果，每条命令的结果由应答状态（0表示正常）、结果数据(bytes)、解析后的内容（str, 仅mcm命令使用）组成
tmp_callback_value = []
# 全局临时变量，记录每次命令的命令追踪字
tmp_tcomm = ''
# logger = None
# capon_lock = threading.Lock()
cmd_lock = threading.Lock()
STRING_LENGTH = 8 #basic length in WORD of string parameter
Time_Sleep = 15  #waiting time for HDR commands. can even extend to 20 or 30 in case necessary. other command can use 0.
vega_mode_dic = {}
vega_mode_dic[0x02] = 'VEGA_MODE_REVERSE_GEARBOX'
vega_mode_dic[0x03] = 'VEGA_MODE_LINE_PRBS'
vega_mode_dic[0x04] = 'VEGA_MODE_HOST_PRBS'
vega_mode_dic[0x06] = 'VEGA_MODE_LINE_LOOPBACK'
vega_mode_dic[0x07] = 'VEGA_MODE_HOST_LOOPBACK'
vega_mode_dic[0xFF] = 'VEGA_MODE_IDLE'




def exit_capture():
    global exitFlag
    exitFlag = True


def get_exit_flag():
    global exitFlag
    return exitFlag

def execute_mcm_cmd_once(seq_num: int, action: str, command: str, value: list):
    global board
    return board.set_mcm(action, command, value, seq_num)
    # global tmp_callback_value
    # global tmp_request_time
    # try:
    #     cmd_lock.acquire()
    #     tmp_callback_value.clear()
    #     tmp_request_time = int(time.time())
    #     send_mcm_cmd(seq_num, action, command, value)
    #     # process_data(15)
    #     process_data(50)
    #     if not tmp_callback_value:
    #         return 1, 'result is empty'
    #     result = tmp_callback_value[0]
    #     # my_sig.infoSignal.emit(-1,f'<- {result}')
    #     my_sig.infoSignal.emit(-1,f'<- {result[0]}, {result[2]}')
    #     is_pass = '成功' if result[0] == 0 else '失败'
    #     my_sig.infoSignal.emit(-1,f'MCM命令执行{is_pass}')
    #     return result[0], result[2]
    # finally:
    #     cmd_lock.release()
def execute_mcm_cmd(seq_num: int, action: str, command: str, value: list):
    # 命令返回在执行中时，不断重试
    rc, info = 1, ''
    itry = 0
    for i in range(50):
        itry += 1
        # if get_exit_flag():
        #     raise Exception('测试手动停止')
        rc, info = execute_mcm_cmd_once(seq_num, action, command, value)
        if rc != 0:
            if itry >= 3:
                return rc, 'MCM命令失败'
            else:
                continue
        if 'MTB DIAG COMMAND IN PROGRESS' not in info:
            break
        time.sleep(1)
    return rc, info

def execute_set_vega_mode_cmd(seq_num: int, interface_no: int, mode: int):
    global board
    subCmd = 'set_vega_mode'
    board.tmp_request_len = module_comm.subCmdDic[subCmd][1]  # subCmdInPara长度从subCmdDic中查询
    flag, data = board.send_module_cmd(TYPE_ID_VEGA, seq_num, subCmd, interface_no, mode)
    my_sig.infoSignal.emit(-1,f'<- {flag}')
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'设置VEGA interface{interface_no} 模式:{mode}({vega_mode_dic[mode]}) {is_pass}')
    return flag, data

def execute_get_vega_mode_cmd(seq_num: int, interface_no: int):
    global board
    subCmd = 'get_vega_mode'
    board.tmp_request_len = module_comm.subCmdDic[subCmd][1]  # subCmdInPara长度从subCmdDic中查询
    flag, data = board.send_module_cmd(TYPE_ID_VEGA, seq_num, subCmd, interface_no)
    my_sig.infoSignal.emit(-1,f'<- {flag}')
    if flag == 0:
        mode = int.from_bytes(data, byteorder='little', signed=False)
        my_sig.sig_vegamode.emit(mode)
        my_sig.infoSignal.emit(-1,f'读取VEGA interface{interface_no} 模式成功: {mode}({vega_mode_dic[mode]})')
    else:
        my_sig.infoSignal.emit(-1,f'读取VEGA interface{interface_no}模式失败')
    return flag, data

def execute_set_tx_enable_cmd(seq_num: int, is_enable, module_type=TYPE_ID_CFP2):
    global board
    subCmd = 'set_tx_enable'
    board.tmp_request_len = module_comm.subCmdDic[subCmd][1]  # subCmdInPara长度从subCmdDic中查询
    flag, data = board.send_module_cmd(module_type, seq_num, subCmd, is_enable)
    my_sig.infoSignal.emit(-1,f'<- {flag}')
    act = '打开' if is_enable else '关闭'
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'{act}TX激光器{is_pass}')
    return flag, data

def execute_enable_poll_cmd(is_enable):
    global board
    # subCmd = 'set_tx_enable'
    board.tmp_request_len = 3 #module_comm.subCmdDic[subCmd][1]  # subCmdInPara长度从subCmdDic中查询
    flag, data = board.send_enablepoll_cmd(is_enable)
    my_sig.infoSignal.emit(-1,f'<- {flag}')
    act = '打开' if is_enable else '关闭'
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'{act}单盘轮询{is_pass}')
    return flag, data

def execute_set_wavelength_cmd(seq_num: int, wl_type, rx_wl_ch, tx_wl_ch):
    global board
    subCmd = 'set_wavelength'
    board.tmp_request_len = module_comm.subCmdDic[subCmd][1]
    flag, data = board.send_module_cmd(TYPE_ID_CFP2, seq_num, subCmd, wl_type, rx_wl_ch, tx_wl_ch)
    my_sig.infoSignal.emit(-1,f'<- {flag}')
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'设置激光器波长{is_pass}')
    return flag, data

def execute_get_wavelength_cmd(seq_num: int, wl_type):
    global board
    subCmd = 'get_wavelength'
    board.tmp_request_len = module_comm.subCmdDic[subCmd][1]
    flag, value = board.send_module_cmd(TYPE_ID_CFP2, seq_num, subCmd, wl_type)

    rx_wl_ch = 0
    tx_wl_ch = 0
    str_data = ''
    if flag == 0:
        if wl_type == 1 or wl_type == 3:
            rx_wl_ch = int.from_bytes(value[0:2], byteorder='little', signed=False) / 1000.0
        if wl_type == 2 or wl_type == 3:
            tx_wl_ch = int.from_bytes(value[2:4], byteorder='little', signed=False) / 1000.0
        str_data = f'rx_wl={rx_wl_ch}, tx_wl={tx_wl_ch}'
    my_sig.sig_show_wl.emit(str(rx_wl_ch), str(tx_wl_ch))
    my_sig.infoSignal.emit(-1,f'<- {flag}, {str_data}')
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'读取激光器波长{is_pass}')
    return [flag, [rx_wl_ch, tx_wl_ch]]

def execute_set_reset_cmd_retry(seq_num: int):
    res = None
    for _ in range(3):
        res = execute_set_reset_cmd(seq_num)
        if res[0] == 0:
            return
        time.sleep(1)
    raise Exception(f'reset失败: {res}')
def execute_set_reset_cmd(seq_num: int):
    global board
    subCmd = 'set_reset'
    board.tmp_request_len = module_comm.subCmdDic[subCmd][1]
    flag, data = board.send_module_cmd(TYPE_ID_CFP2, seq_num, subCmd)
    my_sig.infoSignal.emit(-1,f'<- {flag}')
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'设置Reset{is_pass}')
    return flag, data

def execute_set_lowpwr_enable_cmd(seq_num: int, is_enable):
    global board
    subCmd = 'set_low_power'
    board.tmp_request_len = module_comm.subCmdDic[subCmd][1]
    flag, data = board.send_module_cmd(TYPE_ID_CFP2, seq_num, subCmd, is_enable)
    my_sig.infoSignal.emit(-1,f'<- {flag}')
    act = '打开' if is_enable else '关闭'
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'{act}低功耗模式{is_pass}')
    return flag, data

def execute_get_reg_cmd(seq_num: int, addr: int, cnt: int):
    my_sig.sig_regdata.emit('')
    ret_code, data = read_reg_val(seq_num, addr, cnt)
    my_sig.sig_regdata.emit(bytes2str(data))

def read_reg_val(seq_num: int, addr: int, cnt: int):
    # 返回是否成功、data（bytes类型）
    global board
    subCmd = 'get_reg'
    board.tmp_request_len = module_comm.subCmdDic[subCmd][1]
    flag, raw_data = board.send_module_cmd(TYPE_ID_CFP2, seq_num, subCmd, addr, cnt)
    data = b''
    str_data = ''
    if flag == 0:
        data = setbyteslittle(raw_data)
        str_data = bytes2str(data)
    # my_sig.sig_regdata.emit(str_data)
    my_sig.infoSignal.emit(-1,f'<- {flag}, {str_data}')
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'读寄存器地址: {hex(addr)} 数量:{cnt} {is_pass}: 0x{str_data}')
    return flag, data

def read_one_reg(seq_num, addr):
    bok = False
    data = None
    for _ in range(3):
        rc, data = read_reg_val(seq_num, addr, 1)
        if rc == 0:
            bok = True
            break
        time.sleep(1)
    if not bok:
        raise Exception(f'读寄存器{hex(addr)}失败: {data}')
    return bytes2int(data)

def read_one_reg_ex(seq_num, addr):
    rc, data = read_reg_val(seq_num, addr, 1)
    if rc != 0:
        return rc, f'读寄存器{hex(addr)}失败: {data}'
    return 0, bytes2int(data)

def read_one_reg_retry(seq_num, addr):
    data = ''
    for _ in range(3):
        rc, data = read_reg_val(seq_num, addr, 1)
        if rc == 0:
            return 0, bytes2int(data)
        time.sleep(0.5)
    return 1, f'读寄存器{hex(addr)}失败: {data}'

def write_one_reg(seq_num, addr, val: int):
    result = None
    bok = False
    for _ in range(3):
        result = execute_set_regs_cmd(seq_num, addr, [val])
        if result[0] == 0:
            bok = True
            break
        time.sleep(1)
    if not bok:
        raise Exception(f'写寄存器 {hex(addr)} 失败')
    return result

def write_one_reg_ex(seq_num, addr, val: int):
    result = execute_set_regs_cmd(seq_num, addr, [val])
    if result[0] != 0:
        return result[0], f'写寄存器 {hex(addr)} 失败'
    return 0, ''

def write_max_128_reg(seq_num, addr, vals: list):
    # if cnt > 128:
    #     raise Exception("write_regs Data length overflow.")
    bok = False
    for _ in range(3):
        result = execute_set_regs_cmd(seq_num, addr, vals)
        if result[0] == 0:
            bok = True
            break
        time.sleep(1)
    if not bok:
        raise Exception(f'写寄存器:{addr}, 个数:{cnt} 失败')
    # my_sig.infoSignal.emit(-1,f'write {hex(addr)}: {ints2hexstr(vals)}')
    return 0, ''

def read_max_120_reg(seq_num, addr, cnt):
    if cnt > 120:
        raise Exception("Data length overflow.")
    rc = 1
    data = ''
    for _ in range(3):
        rc, data = read_reg_val(seq_num, addr, cnt)
        if rc == 0:
            break
        time.sleep(1)
    if rc != 0:
        raise Exception(f'读寄存器失败: {addr}, 个数: {cnt}')

    lst = list()
    for i in range(int(len(data)/2)):
        word = data[2*i:2*i+2]
        lst.append(bytes2int(word))
    return lst

def execute_set_regs_cmd(seq_num: int, addr: int, vals: list):
    # 写寄存器，入参vals中内容为寄存器的值(int)的list
    global board
    subCmd = 'set_regs'
    cnt = len(vals)
    board.tmp_request_len = 4 + 2 * cnt
    flag, data = board.send_module_cmd(TYPE_ID_CFP2, seq_num, subCmd, addr, cnt, vals)
    my_sig.infoSignal.emit(-1,f'<- {flag}')
    if flag == 0:
        is_pass = '成功'
        info = ''
    else:
        is_pass = '失败'
        info = f'写寄存器{hex(addr)} 数量:{cnt} 值:{ints2hexstr(vals)} {is_pass}'
    my_sig.infoSignal.emit(-1,f'写寄存器{hex(addr)} 数量:{cnt} 值:{ints2hexstr(vals)} {is_pass}')
    return flag, info

def readI2CWords(seq_num: int, page, addr, cnt):
    """
    读取QDD模块寄存器
    """
    global board
    subCmd = 'get_qdd_reg'
    vals = []
    vals_str = ''
    if page in [0xA0,]:
        while addr >= 0x100:
            page += 1
            addr -= 0x80
    while cnt > 0:
        current_page_reg_left = 0x80 - addr % 0x80
        if cnt > current_page_reg_left:
            read_cnt = current_page_reg_left
        else:
            read_cnt = cnt
        board.tmp_request_len = module_comm.subCmdDic[subCmd][1]
        flag, raw_data = board.send_module_cmd(TYPE_ID_QDD, seq_num, subCmd, page, addr, read_cnt)
        my_sig.infoSignal.emit(-1,f'<- {flag,raw_data}')
        is_pass, data = flag, raw_data
        while is_pass != 0:
            my_sig.infoSignal.emit(-1,f'读寄存器{hex(page)[2:]}h:{addr}-{addr+cnt} 失败')
            flag, raw_data = board.send_module_cmd(TYPE_ID_QDD, seq_num, subCmd, page, addr, read_cnt)
            my_sig.infoSignal.emit(-1,f'<- {flag, raw_data}')
            is_pass, data = flag, raw_data
        vals += [int(i) for i in data]
        vals_str += bytes2str(data)
        addr = (addr + read_cnt + 0x80) % 0x100
        page += 1
        cnt -= read_cnt
    my_sig.infoSignal.emit(-1,f'读寄存器{hex(page)[2:]}h:{addr}-{addr+cnt} :{vals_str} 成功')
    return 0, vals

def writeI2CWords(seq_num: int, page, addr, vals: list):
    global board
    subCmd = 'set_qdd_regs'
    info = ''
    re_flag = 0
    if page in [0xA0, ]:
        while addr >= 0x100:
            page += 1
            addr -= 0x80
    while len(vals) > 0:
        current_page_reg_left = 0x80 - addr % 0x80
        if len(vals) > current_page_reg_left:
            write_cnt = current_page_reg_left
        else:
            write_cnt = len(vals)
        vals_write = vals[:write_cnt]
        board.tmp_request_len = module_comm.subCmdDic[subCmd][1]
        flag, raw_data = board.send_module_cmd(TYPE_ID_QDD, seq_num, subCmd, page, addr, write_cnt, vals_write)
        my_sig.infoSignal.emit(-1,f'<- {flag}')
        if flag == 0:
            is_pass = flag
        else:
            re_flag = 1
            is_pass = '失败'
            info += f'写寄存器{hex(page)[2:]}h:{addr}-{addr+write_cnt} 值:{ints2hexstr(vals)} {is_pass}\n'
        my_sig.infoSignal.emit(-1,f'写寄存器{hex(page)[2:]}h:{addr}-{addr+write_cnt}值:{ints2hexstr(vals)} {is_pass}')
        addr = (addr + write_cnt + 0x80) % 0x100
        page += 1
        vals = vals[write_cnt:]
    return re_flag, info

def execute_sla_fifo_write_cmd(seq_num: int, vals: list):
    # 固件烧录专用命令，vals中为写16次寄存器0x9F82、0x9F83的数据，共32个值——升级支持可变个数1~16
    global board
    subCmd = 'sla_fifo_write'
    board.tmp_request_len = module_comm.subCmdDic[subCmd][1]
    flag, data = board.send_module_cmd(TYPE_ID_CFP2, seq_num, subCmd, vals)
    my_sig.infoSignal.emit(-1,f'<- {flag}')
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'slaFifoWrite: {is_pass}')
    # my_sig.infoSignal.emit(-1,f'slaFifoWrite: {ints2hexstr(vals)} {is_pass}')
    return flag, data


def execute_sla_fifo_write_new(seq_num: int, addr, vals: list):
    # 固件烧录专用命令，addr为要写的地址（为0时表示不修改？），vals中为写16次寄存器0x9F82、0x9F83的数据，共32个值——升级支持可变个数1~16
    global board
    subCmd = 'sla_fifo_write_new'
    # tmp_request_len = module_comm.subCmdDic[subCmd][1] # 升级支持可变长度的数据，inpara的长度不固定
    board.tmp_request_len = 4 + 2 * len(vals)
    flag, data = board.send_module_cmd(TYPE_ID_CFP2, seq_num, subCmd, addr, vals)
    my_sig.infoSignal.emit(-1,f'<- {flag}')
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'slaFifoWrite: {is_pass}')
    # my_sig.infoSignal.emit(-1,f'slaFifoWrite: {ints2hexstr(vals)} {is_pass}')
    return flag, data


def execute_sla_consec_read_cmd(seq_num: int, addr, cnt):
    # 固件烧录专用批量读命令，返回是否成功、读到的数据data(bytes类型)
    global board
    subCmd = 'sla_consec_read'
    board.tmp_request_len = module_comm.subCmdDic[subCmd][1]
    flag, raw_data = board.send_module_cmd(TYPE_ID_CFP2, seq_num, subCmd, addr, cnt)
    data = b''
    str_data = ''
    if flag == 0:
        data = setbyteslittle(raw_data)
        str_data = bytes2str(data)
    my_sig.infoSignal.emit(-1,f'<- {flag}, {str_data}')
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'sla_consec_read 地址: {hex(addr)} 数量:{cnt} {is_pass}: 0x{str_data}')
    return flag, data


def execute_sla_read_cmd(seq_num: int, addr):
    # 固件烧录专用读命令，返回是否成功、读到的数据value(int类型)
    rc, data = execute_sla_consec_read_cmd(seq_num, addr, 1)
    value = 0
    if rc == 0:
        val_h = bytes2int(data[0:2])
        val_l = bytes2int(data[2:4])
        value = (val_h << 16) | val_l
    return rc, value


def execute_get_tx_enable_cmd(seq_num: int):
    global board
    subCmd = 'get_tx_enable'
    board.tmp_request_len = module_comm.subCmdDic[subCmd][1]
    flag, data = board.send_module_cmd(TYPE_ID_CFP2, seq_num, subCmd)
    # print(result)
    str_data = ''
    if flag == 0:
        if data == b'\x00':
            str_data = 'enable'
            my_sig.sig_txstate.emit(1)
        else:
            str_data = 'disable'
            my_sig.sig_txstate.emit(0)
    my_sig.infoSignal.emit(-1,f'<- {flag}, {str_data}')
    is_pass = '成功' if flag == 0 else '失败'
    my_sig.infoSignal.emit(-1,f'读取TX激光器状态{is_pass}')
    return flag, data


def get_boardsn_from_poll_info(poll_info):
    board_sn = ''
    sobj = re.search('\\sSN:\\s*(\\S+),', poll_info)  # SN: 000880518598,
    if sobj:
        board_sn = sobj.group(1)
    return board_sn


def read_module_sn(seq_num):
    rc, reply = execute_mcm_cmd(seq_num, "eget", "MFG_SERIAL_NUMBER", [])
    if rc != 0 or 'PASS' not in reply:
        return 1, 'get module sn fail'
    sobj = re.search(r'(^[^\r\n]+)[\r\n][\s\S]*PASS', reply)  # SN: 000880518598,
    if not sobj:
        return 1, 'get module sn from reply info fail'
    sn = sobj.group(1).strip()
    return 0, sn


def execute_hdr_retry(seq_num, action, command, value, hdr_flag=True, transaction=100):
    for _ in range(3):
        if hdr_flag:
            rc, res_value = handleDiagSetGetCmd(seq_num, action, command, value, transaction)
        else:
            rc, res_value = execute_mcm_cmd(seq_num, action, command, value)
        if rc == 0 and 'PASS' in res_value:
            return res_value
        time.sleep(1)
    raise Exception(f'hdr命令 {action} {command} 失败')


def handleDiagSetGetCmd(seq_num, action, command, value, transaction=100):
    # transaction是什么？
    val_str = get_str_args_list(value)
    my_sig.infoSignal.emit(-1,f'-> [HDR] {seq_num}, {action}, {command}, {val_str}, {transaction}')
    returnValue = ""
    if action == "set":
        diagnostic_operation = 0x0001
    elif action == "get":  # Get
        diagnostic_operation = 0x0002
    elif action == "eset":
        diagnostic_operation = 0x0003
    elif action == "eget":
        diagnostic_operation = 0x0004
    else:  # invalid command
        returnValue = "Fail - invalid operation (neither set, get, eset or eget)."
        my_sig.sig_exception.emit(returnValue)
        return 1, returnValue
    command=command.upper()
    # Parse the command into 2 byte register data
    n = 2
    line = [command[i:i + n] for i in range(0, len(command), n)]
    reg_data = []
    Parameter= []
    for j in range(len(line)):
        character = line[j]
        output = [character[i:i + 1] for i in range(0, 2, 1)]
        reg_value = ""
        for i in range(2):
            if output[1 - i] != "":
                y = hex(ord(output[1 - i]))[2:]
                reg_value = reg_value + y
        reg_value = "0x" + reg_value
        value_int = int(reg_value, 16)
        reg_data.append(value_int)
    if len(reg_data) < 15:
        for j in range(len(reg_data), 15):
            reg_data.append(0)
    # Parse the parameters with type of String into 2 byte register data
    for j in range(len(value)):
        if type(value[j]) == type("ab"):
            string_parameter = value[j]
            # Parse the command into 2 byte register data
            n = 2
            line = [string_parameter[i:i + n] for i in range(0, len(string_parameter), n)]
            string_length = STRING_LENGTH
            k = 0
            for k in range(len(line)):
                character = line[k]
                output = [character[i:i + 1] for i in range(0, 2, 1)]
                string_value=''
                for i in range(2):
                    if output[1 - i] != "":
                        y = hex(ord(output[1 - i]))[2:]
                        string_value = string_value + y
                string_value = "0x" + string_value
                value_int = int(string_value, 16)
                # if value_int<0xff:
                #     value_int=value_int<<8
                if k < string_length:
                    Parameter.append(value_int)
            k+=1
            while k < string_length:
                Parameter.append(0)
                k += 1
        else:
            Parameter.append(value[j] & 0xFFFF)
            Parameter.append(value[j] >> 16 & 0xFFFF)

    if read_one_reg(seq_num, 0xAC00) != 0x0001:
        my_sig.infoSignal.emit(-1,"CDB NOT ready to accept host command.")
        return 1, "CDB NOT ready to accept host command."
    # Implement the command

    write_one_reg(seq_num, 0xAC01, 0x3A)  # Command Length important = 58 decimal
    write_one_reg(seq_num, 0xAC02, 0x0000)
    write_one_reg(seq_num, 0xAC03, transaction)
    write_one_reg(seq_num, 0xAC04, 0x0000)
    write_one_reg(seq_num, 0xAC05, diagnostic_operation)
    write_one_reg(seq_num, 0xAC06, 0x0000)

    # Send the command
    write_one_reg(seq_num, 0xAC07, reg_data[0])
    write_one_reg(seq_num, 0xAC08, reg_data[1])
    write_one_reg(seq_num, 0xAC09, reg_data[2])
    write_one_reg(seq_num, 0xAC0A, reg_data[3])
    write_one_reg(seq_num, 0xAC0B, reg_data[4])
    write_one_reg(seq_num, 0xAC0C, reg_data[5])
    write_one_reg(seq_num, 0xAC0D, reg_data[6])
    write_one_reg(seq_num, 0xAC0E, reg_data[7])
    write_one_reg(seq_num, 0xAC0F, reg_data[8])
    write_one_reg(seq_num, 0xAC10, reg_data[9])
    write_one_reg(seq_num, 0xAC11, reg_data[10])
    write_one_reg(seq_num, 0xAC12, reg_data[11])
    write_one_reg(seq_num, 0xAC13, reg_data[12])
    write_one_reg(seq_num, 0xAC14, reg_data[13])

    addr = 0xAC15
    for i in range(30):
        if i < len(Parameter):
            write_one_reg(seq_num, addr + i, Parameter[i])
        else:
            write_one_reg(seq_num, addr + i, 0)

    # Issue the CDB command

    write_one_reg(seq_num, 0xAC02, 0x0100)
    write_one_reg(seq_num, 0xAC00, 0x0EEA)
#    time.sleep(Time_Sleep)

    for j in range(1, 20):
        cmdReply = read_one_reg(seq_num, 0xAC00)
        time.sleep(1)  ###Important, not to remove
        # Check to make sure that the previous command is complete
        if (cmdReply & 0x0200) == 0x0200:
            time.sleep(1)
        else:
            break
    time.sleep(Time_Sleep)

    if cmdReply != 0x0101:
        # print("CDB Debug GET command NOT accepted - upload aborted.", hex(cmdReply))
        my_sig.infoSignal.emit(-1,"CDB Debug GET command NOT accepted - upload aborted." + hex(cmdReply))
        return 1, "CDB Debug GET command NOT accepted - upload aborted." + hex(cmdReply)

    write_one_reg(seq_num, 0xAC01, 0x0001)  # Command Length
    write_one_reg(seq_num, 0xAC02, 0x0000)
    write_one_reg(seq_num, 0xAC00, 0x0EEB)
    time.sleep(1)
    for j in range(1, 20):
#        time.sleep(1)
        cmdReply = read_one_reg(seq_num, 0xAC00)
        if (cmdReply & 0x0200) == 0x0200:
            time.sleep(1)
        else:
            break
    if cmdReply != 0x0101:
        my_sig.infoSignal.emit(-1,"CDB Debug GET command NOT accepted - upload aborted." + hex(cmdReply))
        return 1, "CDB Debug GET command NOT accepted - upload aborted." + hex(cmdReply)

    returnValue = ""
    length = read_one_reg(seq_num, 0xAC01)
    addr = 0xAC06                                             # usful reply output start from 0xAC06, Which is different from payload. 0xAC02~0xAC05 regularly not important.
    end_flag = 'FALSE'
    while (addr < 0xAC02 + length) & (end_flag == 'FALSE'):   # reply payload start from 0xAC02; regularly end by '$'. if '$' is not available, finished the full payload length(read from 0xAC01)
        result = read_one_reg(seq_num, addr)
        byte = result & 0x00FF
        ch = chr(byte)
        if ch != '\0':
            returnValue = returnValue + ch
            if (ch == '$') :
                end_flag = 'TRUE'
        byte = (result >> 8) & 0xFF
        ch = chr(byte)
        if ch != '\0':
            returnValue = returnValue + ch
            if (ch == '$') :
                end_flag = 'TRUE'
        addr += 1
    time.sleep(1)
    #print(returnValue)
    my_sig.infoSignal.emit(-1,f'<- [HDR] {returnValue}')
    return 0, returnValue


def hex_to_float(h: str):
    # 十六进制字符串（4字节）转IEEE754浮点数（单精度）
    # my_sig.infoSignal.emit(-1,f'hex_to_float: {h}')
    i = int(h, 16)
    f = struct.unpack('<f', struct.pack('<I', i))[0]
    my_sig.infoSignal.emit(-1,f'hex_to_float: {h} = {f}')
    return f


def float2hex(num):
    bits, = struct.unpack('!I', struct.pack('!f', num))
    my_sig.infoSignal.emit(-1,f'float2hex: {num} : {hex(bits)}')
    return bits


def signed_num(num, bit):
    if bit <= 32:
        sign = num >> (bit - 1)
        if sign == 0:
            num = num
        else:
            num = -((num ^ (0xFFFFFFFF >> (32 - bit))) + 1)
    else:
        num = 0
        print(' error:bit > 32')
    return num


