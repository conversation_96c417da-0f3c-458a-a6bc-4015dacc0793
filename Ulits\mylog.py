import logging
from datetime import datetime
import colorlog
import os
import sys


class Logger:
    def __init__(self, log_path=None, caller='frame'):
        # 创建一个self.logger
        self.logger = logging.Logger(os.path.basename(sys.argv[0]))
        self.logger.setLevel(logging.DEBUG)
        # 为控制台设置彩色格式化器
        console_formatter = colorlog.ColoredFormatter(
            "%(log_color)s%(asctime)s [%(levelname)s] %(filename)s %(funcName)s line:%(lineno)s - %(message)s%(reset)s",
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        # 为文件设置标准格式化器
        file_formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(filename)s line:%(lineno)s - %(message)s',
                                           datefmt='%Y-%m-%d %H:%M:%S')
        # 创建一个handler，用于写入日志文件
        if not log_path:
            log_path = os.path.join(os.path.dirname(sys.argv[0]), 'log')
            if not os.path.exists(log_path):
                os.makedirs(log_path)
        file_name = f'{caller}' + datetime.now().strftime('%Y_%m_%d_%H_%M_%S') + '.log'
        file_handler = logging.FileHandler(os.path.join(log_path,file_name))
        file_handler.setFormatter(file_formatter)
        # 创建一个handler，用于输出到控制台
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(console_formatter)
        # 给self.logger添加handler
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def get_logger(self):
        return self.logger


logger = Logger().get_logger()
