data = [
    {"name": "wangyi", "sex": "female", "age": 210003, "weight": 85000456, "height": 1457089},
    {"name": "liqiang", "sex": "male", "age": 4513, "weight": 456586, "height": 255789},
    {"name": "zahngferng", "sex": "male", "age": 21003, "weight": 453636, "height": 863789}
]

add_dict = {"name": "zahngferng", "sex": "male", "age": 21003, "weight": 453636, "height": 863789}
# Press the green button in the gutter to run the script.
if __name__ == '__main__':
    for element in data:
        # print(element.keys())
        # print(element.values())
        if element.get("name") == "wangyi":
            sex = element.get("sex")
            age = element.get("age")
            weight = element.get("weight")
            height = element.get("height")
            element.update(add_dict)
            print(f"{sex},{age},{weight},{height}")

    # 添加字典到列表末尾
    data.append(add_dict)
    print(f"添加后的数据共有{len(data)}条记录")
    print(data[-1])  # 打印最后
    # 一个添加的条目
