# 极简SOP控制系统

这是一个极简版的SOP (Serial Optical Polarization) 控制系统，实现了完全自动化的设备控制流程。

## 核心特点

- **极简操作**: 客户端只需连接并传递扰乱速率，服务器自动完成所有操作
- **自动化流程**: 自动申请资源、切换OSW通道、启用SOP扰乱
- **JSON响应**: 服务端返回标准JSON格式响应，包含code、message和data字段
- **自动清理**: 客户端断开时自动禁用扰乱并释放所有资源
- **设备保护**: 同时只允许一个客户端控制设备，避免冲突

## 自动化流程

服务器收到客户端连接后自动执行：

1. **获取SOP设备信息** - 验证设备连接状态
2. **切换输入OSW** - 切换到配置文件指定的输入通道
3. **切换输出OSW** - 切换到配置文件指定的输出通道
4. **启用远程控制** - 启用SOP设备远程控制模式
5. **设置扰乱速率** - 设置客户端指定的三角扰乱速率
6. **启用三角扰乱** - 激活SOP三角扰乱功能

客户端断开时自动清理：
- 禁用三角扰乱
- 禁用远程控制
- 释放设备占用

## 文件结构

```
share_sop/
├── server.py      # 自动化SOP服务器
├── client.py      # 极简SOP客户端
├── example.py     # 使用示例
├── config.ini     # 配置文件
└── README.md      # 说明文档
```

## 快速开始

### 1. 配置设备参数

编辑 `config.ini` 文件：

```ini
[osw]
input_osw1_ip = **************
input_osw1_port = 9000
output_osw2_ip = *************
output_osw2_port = 9000
input_channel = 1    # 自动切换的输入通道
output_channel = 2   # 自动切换的输出通道

[sop]
com_port = COM4
baudrate = 9600
```

### 2. 启动服务器

```bash
cd share_sop
python server.py
```

### 3. 启动客户端

```bash
# 使用默认扰乱速率(500Hz)
python client.py

# 指定扰乱速率
python client.py 1000

# 运行示例程序
python example.py
```

## 服务器响应格式

```json
{
    "code": 200,
    "message": "自动化SOP控制流程执行成功",
    "data": {
        "tri_rate": 500,
        "input_channel": 1,
        "output_channel": 2,
        "status": "active",
        "steps": [...]
    },
    "timestamp": 1234567890
}
```

响应码说明：
- `200`: 成功
- `400`: 请求错误
- `409`: 设备被占用
- `500`: 服务器内部错误

## 客户端使用

### 基本使用

```python
from client import SimpleSOPClient

# 创建客户端
client = SimpleSOPClient()

# 启动SOP控制（扰乱速率500Hz）
response = client.start_sop_control(tri_rate=500)

if response["code"] == 200:
    print("SOP控制启动成功")
    # 保持连接，SOP设备持续工作
    client.keep_alive()  # 按Ctrl+C停止
else:
    print(f"启动失败: {response['message']}")

# 断开连接（自动清理资源）
client.disconnect()
```

### 使用上下文管理器（推荐）

```python
with SimpleSOPClient() as client:
    response = client.start_sop_control(tri_rate=1000)

    if response["code"] == 200:
        print("SOP控制启动成功")
        input("按回车键停止...")  # 等待用户输入
    # 自动断开连接并清理资源
```

### 命令行使用

```bash
# 启动默认扰乱速率(500Hz)
python client.py

# 启动指定扰乱速率
python client.py 1000
```

## 工作原理

1. **客户端连接**: 客户端连接服务器并发送扰乱速率参数
2. **自动执行**: 服务器自动执行完整的SOP控制流程
3. **保持连接**: 客户端保持连接，SOP设备持续工作
4. **自动清理**: 客户端断开时服务器自动清理所有资源

## 错误处理

```python
with SimpleSOPClient() as client:
    response = client.start_sop_control(tri_rate=500)

    if response["code"] == 200:
        print("SOP控制启动成功")
    elif response["code"] == 409:
        print("设备被其他客户端占用")
    else:
        print(f"启动失败: {response['message']}")
```

## 注意事项

1. **独占控制**: 同一时间只能有一个客户端控制SOP设备
2. **自动清理**: 客户端断开连接时自动禁用扰乱并释放资源
3. **配置重要**: 确保config.ini中的OSW通道配置正确
4. **设备连接**: 确保SOP设备和OSW设备连接正常

## 依赖要求

- Python 3.6+
- 现有的SOP控制器模块 (SOP/sop_controller.py)
- 现有的OSW控制模块 (OSW/OSW_Board_1_8.py)
- 配置管理模块 (Config/config.py)
- 日志模块 (Tools/mylog.py)

## 与原版本的区别

| 特性 | 原版本 | 极简版本 |
|------|--------|----------|
| 客户端操作 | 需要发送多个命令 | 只需连接+扰乱速率 |
| 资源管理 | 手动申请/释放 | 自动管理 |
| OSW控制 | 手动切换通道 | 自动切换到配置通道 |
| SOP控制 | 分步设置 | 自动完整流程 |
| 错误恢复 | 手动处理 | 自动清理 |
