# 简化版SOP控制系统

这是一个简化版的SOP (Serial Optical Polarization) 控制系统，提供简单易用的设备控制接口。

## 特点

- **简单易用**: 客户端发送一次请求即可控制SOP设备并自动占用资源
- **JSON响应**: 服务端返回标准JSON格式响应，包含code、message和data字段
- **自动资源管理**: 客户端断开时自动释放设备资源
- **线程安全**: 支持多客户端并发访问，自动处理资源冲突

## 文件结构

```
share_sop/
├── server.py      # SOP服务器
├── client.py      # SOP客户端
├── example.py     # 使用示例
├── config.ini     # 配置文件
└── README.md      # 说明文档
```

## 快速开始

### 1. 启动服务器

```bash
cd share_sop
python server.py
```

### 2. 运行客户端示例

```bash
python example.py
```

## API接口

### 服务器响应格式

所有响应都采用JSON格式：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {...},
    "timestamp": 1234567890
}
```

响应码说明：
- `200`: 成功
- `400`: 请求错误
- `409`: 设备被占用
- `500`: 服务器内部错误

### 客户端API

#### 基本使用

```python
from client import SOPClient

# 创建客户端
client = SOPClient()

# 连接服务器
client.connect()

# 获取设备信息
response = client.get_device_info()
print(response)

# 设置三角扰乱
response = client.set_tri_state(True)
print(response)

# 断开连接
client.disconnect()
```

#### 使用上下文管理器（推荐）

```python
with SOPClient() as client:
    # 快速设置
    response = client.quick_setup(
        tri_rate=500,
        enable_tri=True,
        enable_remote=True
    )
    print(response)
```

### 支持的命令

1. **get_info**: 获取设备信息
2. **set_tri_state**: 设置三角扰乱状态
3. **set_tri_rate**: 设置三角扰乱速率
4. **set_remote_control**: 设置远程控制状态
5. **reset**: 重置设备
6. **release**: 释放设备占用

### 客户端方法

- `get_device_info()`: 获取SOP设备信息
- `set_tri_state(enable)`: 设置三角扰乱状态
- `set_tri_rate(rate)`: 设置三角扰乱速率
- `set_remote_control(enable)`: 设置远程控制状态
- `reset_device()`: 重置SOP设备
- `release_device()`: 释放设备占用
- `quick_setup(tri_rate, enable_tri, enable_remote)`: 快速设置

## 配置文件

编辑 `config.ini` 文件来修改服务器和设备配置：

```ini
[server]
server_ip = 127.0.0.1
server_port = 8000

[sop]
com_port = COM4
baudrate = 9600
data_bits = 8
stop_bits = 1
parity = N
eos = CR+LF
```

## 使用示例

### 基本控制

```python
with SOPClient() as client:
    # 获取设备信息
    info = client.get_device_info()
    print(f"设备ID: {info['data']['idn']}")
    
    # 启用三角扰乱
    client.set_tri_state(True)
    client.set_tri_rate(500)
    
    # 等待一段时间
    time.sleep(5)
    
    # 禁用三角扰乱
    client.set_tri_state(False)
```

### 快速设置

```python
with SOPClient() as client:
    response = client.quick_setup(
        tri_rate=1000,
        enable_tri=True,
        enable_remote=True
    )
    
    if response["code"] == 200:
        print("设置成功")
    else:
        print(f"设置失败: {response['message']}")
```

## 错误处理

系统提供完善的错误处理机制：

```python
with SOPClient() as client:
    response = client.set_tri_rate(500)
    
    if response["code"] == 200:
        print("设置成功")
    elif response["code"] == 409:
        print("设备被其他客户端占用")
    else:
        print(f"操作失败: {response['message']}")
```

## 注意事项

1. 同一时间只能有一个客户端控制SOP设备
2. 客户端断开连接时会自动释放设备资源
3. 建议使用上下文管理器来确保资源正确释放
4. 所有操作都会返回JSON格式的响应，请检查code字段确认操作结果

## 依赖要求

- Python 3.6+
- 现有的SOP控制器模块 (SOP/sop_controller.py)
- 配置管理模块 (Config/config.py)
- 日志模块 (Tools/mylog.py)
