"""简化版SOP客户端。

提供简单易用的SOP设备控制接口。
一次请求即可完成SOP控制和资源占用。
"""

import socket
import json
import time
from typing import Dict, Any, Optional

from Tools.mylog import logger
from Config.config import config


class SOPClient:
    """简化版SOP客户端。
    
    特点：
    - 自动连接管理
    - JSON格式通信
    - 一次请求完成控制和占用
    - 简单易用的API
    """

    def __init__(self, server_ip: str = None, server_port: int = None):
        """初始化SOP客户端。
        
        Args:
            server_ip: 服务器IP地址，默认从配置文件读取
            server_port: 服务器端口，默认从配置文件读取
        """
        self.logger = logger
        
        # 服务器地址配置
        self.server_ip = server_ip or config.get_value("server", "server_ip", "localhost")
        self.server_port = server_port or int(config.get_value("server", "server_port", "8000"))
        
        self.sock = None
        self.connected = False
        
        self.logger.info(f"SOP客户端初始化: {self.server_ip}:{self.server_port}")

    def connect(self) -> bool:
        """连接到SOP服务器。
        
        Returns:
            True表示连接成功，False表示连接失败
        """
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(10.0)
            self.sock.connect((self.server_ip, self.server_port))
            self.connected = True
            self.logger.info(f"已连接到SOP服务器: {self.server_ip}:{self.server_port}")
            return True
        except Exception as e:
            self.logger.error(f"连接SOP服务器失败: {e}")
            self.connected = False
            return False

    def disconnect(self):
        """断开与服务器的连接。"""
        if self.sock:
            try:
                self.sock.close()
            except:
                pass
            finally:
                self.sock = None
                self.connected = False
                self.logger.info("已断开SOP服务器连接")

    def send_request(self, command: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送SOP控制请求。
        
        Args:
            command: SOP命令名称
            params: 命令参数
            
        Returns:
            服务器响应字典
        """
        if not self.connected:
            if not self.connect():
                return {
                    "code": 500,
                    "message": "无法连接到SOP服务器",
                    "data": None
                }
        
        try:
            # 构造请求
            request = {
                "command": command,
                "params": params or {},
                "timestamp": int(time.time())
            }
            
            # 发送请求
            request_data = json.dumps(request, ensure_ascii=False)
            self.sock.sendall(request_data.encode('utf-8'))
            
            # 接收响应
            response_data = self.sock.recv(4096).decode('utf-8').strip()
            response = json.loads(response_data)
            
            self.logger.info(f"SOP请求: {command} -> {response['code']} {response['message']}")
            return response
            
        except Exception as e:
            self.logger.error(f"SOP请求失败: {e}")
            self.connected = False
            return {
                "code": 500,
                "message": f"请求失败: {str(e)}",
                "data": None
            }

    def get_device_info(self) -> Dict[str, Any]:
        """获取SOP设备信息。
        
        Returns:
            设备信息响应
        """
        return self.send_request("get_info")

    def set_tri_state(self, enable: bool) -> Dict[str, Any]:
        """设置三角扰乱状态。
        
        Args:
            enable: True启用，False禁用
            
        Returns:
            操作结果响应
        """
        return self.send_request("set_tri_state", {"enable": enable})

    def set_tri_rate(self, rate: int) -> Dict[str, Any]:
        """设置三角扰乱速率。
        
        Args:
            rate: 扰乱速率值
            
        Returns:
            操作结果响应
        """
        return self.send_request("set_tri_rate", {"rate": rate})

    def set_remote_control(self, enable: bool) -> Dict[str, Any]:
        """设置远程控制状态。
        
        Args:
            enable: True启用，False禁用
            
        Returns:
            操作结果响应
        """
        return self.send_request("set_remote_control", {"enable": enable})

    def reset_device(self) -> Dict[str, Any]:
        """重置SOP设备。
        
        Returns:
            重置结果响应
        """
        return self.send_request("reset")

    def release_device(self) -> Dict[str, Any]:
        """释放SOP设备占用。
        
        Returns:
            释放结果响应
        """
        return self.send_request("release")

    def quick_setup(self, tri_rate: int = 500, enable_tri: bool = True, 
                   enable_remote: bool = True) -> Dict[str, Any]:
        """快速设置SOP设备。
        
        Args:
            tri_rate: 三角扰乱速率
            enable_tri: 是否启用三角扰乱
            enable_remote: 是否启用远程控制
            
        Returns:
            设置结果汇总
        """
        results = {}
        
        # 1. 获取设备信息
        results["device_info"] = self.get_device_info()
        
        # 2. 设置远程控制
        results["remote_control"] = self.set_remote_control(enable_remote)
        
        # 3. 设置三角扰乱速率
        results["tri_rate"] = self.set_tri_rate(tri_rate)
        
        # 4. 设置三角扰乱状态
        results["tri_state"] = self.set_tri_state(enable_tri)
        
        # 检查是否所有操作都成功
        all_success = all(
            result.get("code") == 200 
            for result in results.values()
        )
        
        return {
            "code": 200 if all_success else 400,
            "message": "快速设置完成" if all_success else "快速设置部分失败",
            "data": results
        }

    def __enter__(self):
        """上下文管理器入口。"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口。"""
        self.release_device()
        self.disconnect()


def demo_sop_client():
    """演示SOP客户端使用方法。"""
    print("=== SOP客户端演示 ===")
    
    # 使用上下文管理器自动管理连接
    with SOPClient() as client:
        # 1. 获取设备信息
        print("\n1. 获取设备信息:")
        info_response = client.get_device_info()
        print(f"   响应: {info_response}")
        
        # 2. 快速设置
        print("\n2. 快速设置SOP设备:")
        setup_response = client.quick_setup(
            tri_rate=500,
            enable_tri=True,
            enable_remote=True
        )
        print(f"   响应: {setup_response}")
        
        # 3. 单独控制演示
        print("\n3. 单独控制演示:")
        
        # 禁用三角扰乱
        response = client.set_tri_state(False)
        print(f"   禁用三角扰乱: {response}")
        
        time.sleep(1)
        
        # 重新启用三角扰乱
        response = client.set_tri_state(True)
        print(f"   启用三角扰乱: {response}")
        
        # 4. 设备重置
        print("\n4. 设备重置:")
        reset_response = client.reset_device()
        print(f"   响应: {reset_response}")
        
    print("\n=== 演示完成 ===")


if __name__ == "__main__":
    """主程序入口。"""
    try:
        demo_sop_client()
    except Exception as e:
        logger.error(f"客户端演示失败: {e}", exc_info=True)
