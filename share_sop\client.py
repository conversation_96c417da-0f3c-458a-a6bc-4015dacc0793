"""极简SOP客户端。

客户端只需连接服务器并传递扰乱速率，服务器自动完成所有操作：
- 申请SOP和OSW资源
- 切换OSW到配置的通道
- 启用SOP三角扰乱
客户端断开时服务器自动清理所有资源。
"""

import socket
import json
import time
import sys
import os
from typing import Dict, Any, Optional

# 添加父目录到路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Tools.mylog import logger
from Config.config import config


class SimpleSOPClient:
    """极简SOP客户端。

    特点：
    - 连接即启动SOP控制流程
    - 只需传递扰乱速率参数
    - 断开即自动清理所有资源
    - 无需手动发送命令
    """

    def __init__(self, server_ip: str = None, server_port: int = None):
        """初始化极简SOP客户端。

        Args:
            server_ip: 服务器IP地址，默认从配置文件读取
            server_port: 服务器端口，默认从配置文件读取
        """
        self.logger = logger

        # 服务器地址配置
        self.server_ip = server_ip or config.get_value("server", "server_ip", "127.0.0.1")
        self.server_port = server_port or int(config.get_value("server", "server_port", "8000"))

        self.sock = None
        self.connected = False

        self.logger.info(f"极简SOP客户端初始化: {self.server_ip}:{self.server_port}")

    def start_sop_control(self, tri_rate: int = 500) -> Dict[str, Any]:
        """启动SOP控制流程。

        连接服务器并传递扰乱速率，服务器自动执行完整的控制流程。

        Args:
            tri_rate: 三角扰乱速率，默认500Hz

        Returns:
            服务器响应字典
        """
        try:
            # 连接服务器
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(10.0)
            self.sock.connect((self.server_ip, self.server_port))
            self.connected = True
            self.logger.info(f"已连接到SOP服务器: {self.server_ip}:{self.server_port}")

            # 发送启动请求
            request = {
                "tri_rate": tri_rate,
                "timestamp": int(time.time())
            }

            request_data = json.dumps(request, ensure_ascii=False)
            self.sock.sendall(request_data.encode('utf-8'))
            self.logger.info(f"已发送SOP启动请求，扰乱速率: {tri_rate}Hz")

            # 接收响应
            response_data = self.sock.recv(4096).decode('utf-8').strip()
            response = json.loads(response_data)

            if response["code"] == 200:
                self.logger.info("SOP控制流程启动成功")
                self.logger.info("保持连接中，按Ctrl+C或关闭程序即可停止SOP控制")
            else:
                self.logger.error(f"SOP控制流程启动失败: {response['message']}")

            return response

        except Exception as e:
            self.logger.error(f"启动SOP控制失败: {e}")
            self.connected = False
            return {
                "code": 500,
                "message": f"连接失败: {str(e)}",
                "data": None
            }

    def keep_alive(self):
        """保持连接活跃。

        在SOP控制期间保持与服务器的连接，直到程序结束。
        """
        if not self.connected or not self.sock:
            self.logger.error("未连接到服务器")
            return

        try:
            self.logger.info("SOP控制已激活，保持连接中...")
            self.logger.info("按 Ctrl+C 停止SOP控制")

            # 保持连接直到用户中断或连接断开
            while True:
                time.sleep(1)

        except KeyboardInterrupt:
            self.logger.info("用户中断，正在停止SOP控制...")
        except Exception as e:
            self.logger.error(f"连接异常: {e}")
        finally:
            self.disconnect()

    def disconnect(self):
        """断开与服务器的连接。

        断开连接时服务器会自动清理所有资源。
        """
        if self.sock:
            try:
                self.sock.close()
                self.logger.info("已断开SOP服务器连接，服务器将自动清理资源")
            except:
                pass
            finally:
                self.sock = None
                self.connected = False

    def __enter__(self):
        """上下文管理器入口。"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口。"""
        self.disconnect()


def demo_simple_client():
    """演示极简SOP客户端使用方法。"""
    print("=== 极简SOP客户端演示 ===")
    print("功能: 连接即启动SOP控制，断开即停止")
    print("=" * 40)

    # 创建客户端
    client = SimpleSOPClient()

    try:
        # 启动SOP控制（扰乱速率1000Hz）
        print("启动SOP控制流程...")
        response = client.start_sop_control(tri_rate=1000)

        if response["code"] == 200:
            print("✓ SOP控制流程启动成功")
            print(f"✓ 扰乱速率: {response['data']['tri_rate']}Hz")
            print(f"✓ 输入OSW通道: {response['data']['input_channel']}")
            print(f"✓ 输出OSW通道: {response['data']['output_channel']}")
            print("\n详细执行步骤:")
            for step in response['data']['steps']:
                status = "✓" if step['success'] else "✗"
                print(f"  {status} 步骤{step['step']}: {step['action']}")

            # 保持连接
            client.keep_alive()
        else:
            print(f"✗ SOP控制流程启动失败: {response['message']}")

    except Exception as e:
        print(f"✗ 客户端运行异常: {e}")
        logger.error(f"客户端运行异常: {e}", exc_info=True)
    finally:
        client.disconnect()


def quick_start_sop(tri_rate: int = 500):
    """快速启动SOP控制。

    Args:
        tri_rate: 三角扰乱速率
    """
    print(f"快速启动SOP控制，扰乱速率: {tri_rate}Hz")

    with SimpleSOPClient() as client:
        response = client.start_sop_control(tri_rate)

        if response["code"] == 200:
            print("SOP控制已启动，按Ctrl+C停止")
            client.keep_alive()
        else:
            print(f"启动失败: {response['message']}")


if __name__ == "__main__":
    """主程序入口。"""
    import sys

    print("极简SOP控制客户端")
    print("=" * 30)

    # 检查命令行参数
    if len(sys.argv) > 1:
        try:
            tri_rate = int(sys.argv[1])
            print(f"使用指定扰乱速率: {tri_rate}Hz")
            quick_start_sop(tri_rate)
        except ValueError:
            print("错误: 扰乱速率必须为整数")
            print("用法: python client.py [扰乱速率]")
    else:
        # 运行演示
        demo_simple_client()
