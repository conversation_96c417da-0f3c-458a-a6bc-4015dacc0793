"""SOP控制系统使用示例。

演示如何使用简化版的SOP控制系统进行设备控制。
"""

import time
import json
from client import SOPClient


def example_basic_usage():
    """基本使用示例。"""
    print("=== 基本使用示例 ===")
    
    # 创建客户端
    client = SOPClient()
    
    try:
        # 连接服务器
        if not client.connect():
            print("连接服务器失败")
            return
        
        # 获取设备信息
        response = client.get_device_info()
        print(f"设备信息: {json.dumps(response, indent=2, ensure_ascii=False)}")
        
        # 设置三角扰乱
        response = client.set_tri_state(True)
        print(f"启用三角扰乱: {response['message']}")
        
        # 设置扰乱速率
        response = client.set_tri_rate(500)
        print(f"设置扰乱速率: {response['message']}")
        
        # 释放设备
        response = client.release_device()
        print(f"释放设备: {response['message']}")
        
    finally:
        client.disconnect()


def example_context_manager():
    """上下文管理器使用示例。"""
    print("\n=== 上下文管理器示例 ===")
    
    # 使用with语句自动管理连接和资源
    with SOPClient() as client:
        # 快速设置
        response = client.quick_setup(
            tri_rate=1000,
            enable_tri=True,
            enable_remote=True
        )
        
        if response["code"] == 200:
            print("快速设置成功")
            print(f"设置结果: {json.dumps(response['data'], indent=2, ensure_ascii=False)}")
        else:
            print(f"快速设置失败: {response['message']}")


def example_step_by_step():
    """分步控制示例。"""
    print("\n=== 分步控制示例 ===")
    
    with SOPClient() as client:
        print("1. 获取设备信息...")
        info = client.get_device_info()
        if info["code"] == 200:
            device_data = info["data"]
            print(f"   设备ID: {device_data.get('idn', 'Unknown')}")
            print(f"   版本: {device_data.get('version', 'Unknown')}")
        
        print("\n2. 启用远程控制...")
        response = client.set_remote_control(True)
        print(f"   结果: {response['message']}")
        
        print("\n3. 设置三角扰乱参数...")
        # 先设置速率
        response = client.set_tri_rate(750)
        print(f"   设置速率: {response['message']}")
        
        # 再启用扰乱
        response = client.set_tri_state(True)
        print(f"   启用扰乱: {response['message']}")
        
        print("\n4. 等待3秒...")
        time.sleep(3)
        
        print("\n5. 禁用三角扰乱...")
        response = client.set_tri_state(False)
        print(f"   结果: {response['message']}")
        
        print("\n6. 禁用远程控制...")
        response = client.set_remote_control(False)
        print(f"   结果: {response['message']}")


def example_error_handling():
    """错误处理示例。"""
    print("\n=== 错误处理示例 ===")
    
    # 尝试连接到不存在的服务器
    client = SOPClient("192.168.1.999", 9999)
    
    response = client.get_device_info()
    print(f"连接失败响应: {response}")
    
    # 正常连接但发送无效命令
    with SOPClient() as client:
        # 直接发送无效命令
        response = client.send_request("invalid_command")
        print(f"无效命令响应: {response}")


def example_concurrent_clients():
    """并发客户端示例。"""
    print("\n=== 并发客户端示例 ===")
    
    import threading
    
    def client_task(client_id: int):
        """客户端任务。"""
        with SOPClient() as client:
            print(f"客户端{client_id}: 尝试获取设备信息")
            response = client.get_device_info()
            
            if response["code"] == 200:
                print(f"客户端{client_id}: 成功获取设备，尝试控制")
                # 尝试设置三角扰乱
                tri_response = client.set_tri_state(True)
                print(f"客户端{client_id}: {tri_response['message']}")
                
                time.sleep(2)
                
                # 释放设备
                release_response = client.release_device()
                print(f"客户端{client_id}: {release_response['message']}")
            else:
                print(f"客户端{client_id}: {response['message']}")
    
    # 创建多个客户端线程
    threads = []
    for i in range(3):
        thread = threading.Thread(target=client_task, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()


if __name__ == "__main__":
    """主程序入口。"""
    print("SOP控制系统使用示例")
    print("=" * 50)
    
    try:
        # 基本使用
        example_basic_usage()
        
        # 上下文管理器
        example_context_manager()
        
        # 分步控制
        example_step_by_step()
        
        # 错误处理
        example_error_handling()
        
        # 并发客户端
        example_concurrent_clients()
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
    
    print("\n示例程序结束")
