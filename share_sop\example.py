"""极简SOP控制系统使用示例。

演示如何使用极简版的SOP控制系统。
客户端只需连接并传递扰乱速率，服务器自动完成所有操作。
"""

import time
import json
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from client import SimpleSOPClient


def example_basic_usage():
    """基本使用示例 - 连接即启动SOP控制。"""
    print("=== 基本使用示例 ===")
    print("连接服务器即自动启动SOP控制流程")

    # 创建客户端
    client = SimpleSOPClient()

    try:
        # 启动SOP控制（默认500Hz）
        print("启动SOP控制...")
        response = client.start_sop_control(tri_rate=500)

        if response["code"] == 200:
            print("✓ SOP控制启动成功")
            print("详细信息:")
            print(json.dumps(response, indent=2, ensure_ascii=False))

            # 保持连接5秒后自动断开
            print("保持SOP控制5秒...")
            time.sleep(5)
            print("自动断开连接")
        else:
            print(f"✗ SOP控制启动失败: {response['message']}")

    finally:
        client.disconnect()


def example_context_manager():
    """上下文管理器使用示例。"""
    print("\n=== 上下文管理器示例 ===")
    print("使用with语句自动管理连接")

    # 使用with语句自动管理连接
    with SimpleSOPClient() as client:
        # 启动SOP控制（1000Hz扰乱速率）
        response = client.start_sop_control(tri_rate=1000)

        if response["code"] == 200:
            print("✓ SOP控制启动成功")
            print(f"✓ 扰乱速率: {response['data']['tri_rate']}Hz")

            # 模拟工作3秒
            print("模拟工作3秒...")
            time.sleep(3)
            print("工作完成，自动断开连接")
        else:
            print(f"✗ SOP控制启动失败: {response['message']}")


def example_different_rates():
    """不同扰乱速率示例。"""
    print("\n=== 不同扰乱速率示例 ===")

    rates = [300, 500, 1000, 1500]

    for rate in rates:
        print(f"\n测试扰乱速率: {rate}Hz")

        client = SimpleSOPClient()
        try:
            response = client.start_sop_control(tri_rate=rate)

            if response["code"] == 200:
                print(f"✓ {rate}Hz 扰乱启动成功")
                time.sleep(2)  # 运行2秒
                print(f"✓ {rate}Hz 扰乱测试完成")
            else:
                print(f"✗ {rate}Hz 扰乱启动失败: {response['message']}")

        finally:
            client.disconnect()
            time.sleep(1)  # 等待资源释放


def example_error_handling():
    """错误处理示例。"""
    print("\n=== 错误处理示例 ===")

    # 尝试连接到不存在的服务器
    print("1. 测试连接不存在的服务器...")
    client = SimpleSOPClient("192.168.1.999", 9999)
    response = client.start_sop_control(500)
    print(f"   连接失败响应: {response['message']}")

    # 测试无效的扰乱速率
    print("\n2. 测试无效的扰乱速率...")
    client = SimpleSOPClient()
    try:
        response = client.start_sop_control(tri_rate=-100)  # 负数速率
        print(f"   响应: {response['message']}")
    finally:
        client.disconnect()


def example_concurrent_clients():
    """并发客户端示例 - 演示设备占用机制。"""
    print("\n=== 并发客户端示例 ===")
    print("演示多个客户端同时尝试控制SOP设备")

    import threading

    def client_task(client_id: int, tri_rate: int):
        """客户端任务。"""
        print(f"客户端{client_id}: 尝试启动SOP控制（{tri_rate}Hz）")

        client = SimpleSOPClient()
        try:
            response = client.start_sop_control(tri_rate=tri_rate)

            if response["code"] == 200:
                print(f"客户端{client_id}: ✓ 成功获得SOP控制权")
                time.sleep(3)  # 占用3秒
                print(f"客户端{client_id}: 释放SOP控制权")
            elif response["code"] == 409:
                print(f"客户端{client_id}: ✗ 设备被占用 - {response['message']}")
            else:
                print(f"客户端{client_id}: ✗ 控制失败 - {response['message']}")

        finally:
            client.disconnect()

    # 创建3个客户端线程，使用不同的扰乱速率
    threads = []
    rates = [500, 750, 1000]

    for i, rate in enumerate(rates):
        thread = threading.Thread(target=client_task, args=(i+1, rate))
        threads.append(thread)
        thread.start()
        time.sleep(0.5)  # 错开启动时间

    # 等待所有线程完成
    for thread in threads:
        thread.join()

    print("并发测试完成")


def interactive_demo():
    """交互式演示。"""
    print("\n=== 交互式演示 ===")
    print("输入扰乱速率启动SOP控制，输入0退出")

    while True:
        try:
            rate_input = input("\n请输入三角扰乱速率 (Hz, 0=退出): ").strip()

            if rate_input == "0":
                print("退出演示")
                break

            tri_rate = int(rate_input)
            if tri_rate <= 0:
                print("扰乱速率必须为正数")
                continue

            print(f"启动SOP控制，扰乱速率: {tri_rate}Hz")

            client = SimpleSOPClient()
            try:
                response = client.start_sop_control(tri_rate=tri_rate)

                if response["code"] == 200:
                    print("✓ SOP控制启动成功")
                    input("按回车键停止SOP控制...")
                    print("停止SOP控制")
                else:
                    print(f"✗ SOP控制启动失败: {response['message']}")

            finally:
                client.disconnect()

        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n用户中断")
            break
        except Exception as e:
            print(f"错误: {e}")


if __name__ == "__main__":
    """主程序入口。"""
    print("极简SOP控制系统使用示例")
    print("=" * 50)
    print("特点: 客户端连接即自动执行完整SOP控制流程")
    print("包括: OSW切换 + SOP扰乱启动")
    print("=" * 50)

    try:
        # 基本使用
        example_basic_usage()

        # 上下文管理器
        example_context_manager()

        # 不同扰乱速率
        example_different_rates()

        # 错误处理
        example_error_handling()

        # 并发客户端
        example_concurrent_clients()

        # 交互式演示
        interactive_demo()

    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序执行出错: {e}")

    print("\n示例程序结束")
