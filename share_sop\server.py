"""简化版SOP服务器。

提供JSON格式响应的SOP设备控制服务器。
客户端发送一次请求即可控制SOP设备并自动占用资源。
"""

import socket
import threading
import json
import time
from typing import Dict, Any, Optional
from enum import Enum

from Tools.mylog import logger
from Config.config import config
from SOP.sop_controller import sop_controller


class ResponseCode(Enum):
    """响应状态码枚举。"""
    SUCCESS = 200
    ERROR = 400
    DEVICE_BUSY = 409
    INTERNAL_ERROR = 500


class SOPServer:
    """简化版SOP服务器。
    
    特点：
    - JSON格式响应
    - 一次请求完成SOP控制和资源占用
    - 自动资源管理
    """

    def __init__(self):
        """初始化SOP服务器。"""
        self.logger = logger
        self.sop = sop_controller
        self.current_client = None  # 当前占用SOP的客户端
        self.lock = threading.Lock()
        
        # 从配置读取服务器参数
        self.server_ip = config.get_value("server", "server_ip", "localhost")
        self.server_port = int(config.get_value("server", "server_port", "8000"))
        
        self.logger.info(f"SOP服务器初始化完成: {self.server_ip}:{self.server_port}")

    def create_response(self, code: ResponseCode, message: str, data: Any = None) -> str:
        """创建JSON格式响应。
        
        Args:
            code: 响应状态码
            message: 响应消息
            data: 响应数据
            
        Returns:
            JSON格式的响应字符串
        """
        response = {
            "code": code.value,
            "message": message,
            "data": data,
            "timestamp": int(time.time())
        }
        return json.dumps(response, ensure_ascii=False)

    def handle_sop_request(self, client_id: str, command: str, params: Dict[str, Any]) -> str:
        """处理SOP控制请求。
        
        Args:
            client_id: 客户端标识
            command: SOP命令
            params: 命令参数
            
        Returns:
            JSON格式响应
        """
        with self.lock:
            # 检查设备占用状态
            if self.current_client and self.current_client != client_id:
                return self.create_response(
                    ResponseCode.DEVICE_BUSY,
                    f"SOP设备正被客户端 {self.current_client} 占用",
                    {"current_client": self.current_client}
                )
            
            # 占用设备
            self.current_client = client_id
            
            try:
                # 执行SOP命令
                result = self._execute_sop_command(command, params)
                
                if result["success"]:
                    return self.create_response(
                        ResponseCode.SUCCESS,
                        f"SOP命令 '{command}' 执行成功",
                        result["data"]
                    )
                else:
                    return self.create_response(
                        ResponseCode.ERROR,
                        f"SOP命令 '{command}' 执行失败: {result['error']}",
                        None
                    )
                    
            except Exception as e:
                self.logger.error(f"执行SOP命令时发生异常: {e}", exc_info=True)
                return self.create_response(
                    ResponseCode.INTERNAL_ERROR,
                    f"服务器内部错误: {str(e)}",
                    None
                )

    def _execute_sop_command(self, command: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行具体的SOP命令。
        
        Args:
            command: SOP命令名称
            params: 命令参数
            
        Returns:
            执行结果字典
        """
        try:
            if command == "get_info":
                # 获取设备信息
                idn = self.sop.get_idn()
                version = self.sop.get_version()
                return {
                    "success": True,
                    "data": {
                        "idn": idn,
                        "version": version,
                        "status": "connected"
                    }
                }
                
            elif command == "set_tri_state":
                # 设置三角扰乱状态
                enable = params.get("enable", False)
                result = self.sop.set_tri_state(enable)
                return {
                    "success": result is not None,
                    "data": {
                        "tri_state": "enabled" if enable else "disabled",
                        "response": result
                    }
                }
                
            elif command == "set_tri_rate":
                # 设置三角扰乱速率
                rate = params.get("rate", 500)
                result = self.sop.set_tri_rate(rate)
                return {
                    "success": result is not None,
                    "data": {
                        "tri_rate": rate,
                        "response": result
                    }
                }
                
            elif command == "set_remote_control":
                # 设置远程控制
                enable = params.get("enable", False)
                result = self.sop.set_remote_control_state(enable)
                return {
                    "success": result is not None,
                    "data": {
                        "remote_control": "enabled" if enable else "disabled",
                        "response": result
                    }
                }
                
            elif command == "reset":
                # 重置设备
                result = self.sop.reset_device()
                return {
                    "success": result is not None,
                    "data": {
                        "action": "reset",
                        "response": result
                    }
                }
                
            elif command == "release":
                # 释放设备占用
                self.current_client = None
                return {
                    "success": True,
                    "data": {
                        "action": "released",
                        "message": "SOP设备已释放"
                    }
                }
                
            else:
                return {
                    "success": False,
                    "error": f"未知的SOP命令: {command}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def handle_client(self, client_sock: socket.socket, client_addr: tuple):
        """处理客户端连接。
        
        Args:
            client_sock: 客户端socket
            client_addr: 客户端地址
        """
        client_id = f"{client_addr[0]}:{client_addr[1]}"
        self.logger.info(f"客户端连接: {client_id}")
        
        try:
            while True:
                # 接收请求
                data = client_sock.recv(4096)
                if not data:
                    break
                    
                try:
                    # 解析JSON请求
                    request = json.loads(data.decode('utf-8'))
                    command = request.get("command")
                    params = request.get("params", {})
                    
                    self.logger.info(f"收到请求: {client_id} -> {command}")
                    
                    # 处理请求
                    response = self.handle_sop_request(client_id, command, params)
                    
                    # 发送响应
                    client_sock.sendall((response + "\n").encode('utf-8'))
                    
                except json.JSONDecodeError as e:
                    error_response = self.create_response(
                        ResponseCode.ERROR,
                        f"JSON格式错误: {str(e)}",
                        None
                    )
                    client_sock.sendall((error_response + "\n").encode('utf-8'))
                    
        except Exception as e:
            self.logger.error(f"处理客户端 {client_id} 时发生错误: {e}")
        finally:
            # 客户端断开时释放资源
            with self.lock:
                if self.current_client == client_id:
                    self.current_client = None
                    self.logger.info(f"客户端 {client_id} 断开，已释放SOP设备")
            
            client_sock.close()

    def start(self):
        """启动服务器。"""
        server_sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_sock.bind((self.server_ip, self.server_port))
        server_sock.listen(5)
        
        self.logger.info(f"SOP服务器已启动: {self.server_ip}:{self.server_port}")
        
        try:
            while True:
                client_sock, client_addr = server_sock.accept()
                # 为每个客户端创建新线程
                thread = threading.Thread(
                    target=self.handle_client,
                    args=(client_sock, client_addr)
                )
                thread.daemon = True
                thread.start()
                
        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在关闭服务器...")
        finally:
            server_sock.close()


if __name__ == "__main__":
    """主程序入口。"""
    server = SOPServer()
    try:
        server.start()
    except Exception as e:
        logger.error(f"服务器启动失败: {e}", exc_info=True)
