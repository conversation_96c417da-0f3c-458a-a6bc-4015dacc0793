"""自动化SOP控制服务器。

客户端连接后自动执行完整的SOP控制流程：
1. 申请SOP和OSW资源
2. 切换OSW到指定通道
3. 启用SOP三角扰乱
4. 设置扰乱速率
客户端只需连接并传递速率参数，断开时自动清理资源。
"""

import socket
import threading
import json
import time
import sys
import os
from typing import Dict, Any, Optional
from enum import Enum

# 添加父目录到路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Tools.mylog import logger
from Config.config import config
from SOP.sop_controller import sop_controller
from OSW.OSW_Board_1_8 import OSW_Board_1_8


class ResponseCode(Enum):
    """响应状态码枚举。"""
    SUCCESS = 200
    ERROR = 400
    DEVICE_BUSY = 409
    INTERNAL_ERROR = 500


class AutoSOPServer:
    """自动化SOP控制服务器。

    特点：
    - 客户端连接即自动执行完整控制流程
    - 自动申请和管理所有资源（SOP + OSW）
    - 自动切换OSW通道并启用SOP扰乱
    - 客户端断开自动清理所有资源
    """

    def __init__(self):
        """初始化自动化SOP服务器。"""
        self.logger = logger
        self.sop = sop_controller
        self.current_client = None  # 当前占用设备的客户端
        self.lock = threading.Lock()

        # 从配置读取服务器参数
        self.server_ip = config.get_value("server", "server_ip", "localhost")
        self.server_port = int(config.get_value("server", "server_port", "8000"))

        # 初始化OSW设备
        try:
            self.input_osw = OSW_Board_1_8(
                config.get_value('osw', 'input_osw1_ip'),
                int(config.get_value('osw', 'input_osw1_port'))
            )
            self.output_osw = OSW_Board_1_8(
                config.get_value('osw', 'output_osw2_ip'),
                int(config.get_value('osw', 'output_osw2_port'))
            )
            self.logger.info("OSW设备初始化成功")
        except Exception as e:
            self.logger.error(f"OSW设备初始化失败: {e}")
            raise

        # 读取OSW通道配置
        self.input_channel = int(config.get_value('osw', 'input_channel', '1'))
        self.output_channel = int(config.get_value('osw', 'output_channel', '2'))

        self.logger.info(f"自动化SOP服务器初始化完成: {self.server_ip}:{self.server_port}")
        self.logger.info(f"OSW通道配置: 输入通道{self.input_channel}, 输出通道{self.output_channel}")

    def create_response(self, code: ResponseCode, message: str, data: Any = None) -> str:
        """创建JSON格式响应。
        
        Args:
            code: 响应状态码
            message: 响应消息
            data: 响应数据
            
        Returns:
            JSON格式的响应字符串
        """
        response = {
            "code": code.value,
            "message": message,
            "data": data,
            "timestamp": int(time.time())
        }
        return json.dumps(response, ensure_ascii=False)

    def auto_setup_sop(self, client_id: str, tri_rate: int = 500) -> str:
        """自动执行完整的SOP控制流程。

        Args:
            client_id: 客户端标识
            tri_rate: 三角扰乱速率

        Returns:
            JSON格式响应
        """
        with self.lock:
            # 检查设备占用状态
            if self.current_client and self.current_client != client_id:
                return self.create_response(
                    ResponseCode.DEVICE_BUSY,
                    f"设备正被客户端 {self.current_client} 占用",
                    {"current_client": self.current_client}
                )

            # 占用设备
            self.current_client = client_id
            self.logger.info(f"客户端 {client_id} 开始自动化SOP控制流程")

            try:
                # 执行自动化流程
                result = self._execute_auto_flow(tri_rate)

                if result["success"]:
                    return self.create_response(
                        ResponseCode.SUCCESS,
                        "自动化SOP控制流程执行成功",
                        result["data"]
                    )
                else:
                    # 流程失败，释放资源
                    self.current_client = None
                    return self.create_response(
                        ResponseCode.ERROR,
                        f"自动化流程执行失败: {result['error']}",
                        result.get("data")
                    )

            except Exception as e:
                # 异常情况，释放资源
                self.current_client = None
                self.logger.error(f"自动化流程执行异常: {e}", exc_info=True)
                return self.create_response(
                    ResponseCode.INTERNAL_ERROR,
                    f"服务器内部错误: {str(e)}",
                    None
                )

    def _execute_auto_flow(self, tri_rate: int) -> Dict[str, Any]:
        """执行自动化SOP控制流程。

        流程步骤：
        1. 获取SOP设备信息
        2. 切换输入OSW到指定通道
        3. 切换输出OSW到指定通道
        4. 启用SOP远程控制
        5. 设置三角扰乱速率
        6. 启用三角扰乱

        Args:
            tri_rate: 三角扰乱速率

        Returns:
            执行结果字典
        """
        flow_data = {
            "tri_rate": tri_rate,
            "input_channel": self.input_channel,
            "output_channel": self.output_channel,
            "steps": []
        }

        try:
            # 步骤1: 获取SOP设备信息
            self.logger.info("步骤1: 获取SOP设备信息")
            idn = self.sop.get_idn()
            version = self.sop.get_version()
            flow_data["steps"].append({
                "step": 1,
                "action": "get_sop_info",
                "success": True,
                "data": {"idn": idn, "version": version}
            })

            # 步骤2: 切换输入OSW到指定通道
            self.logger.info(f"步骤2: 切换输入OSW到通道{self.input_channel}")
            self.input_osw.switch_channel(self.input_channel)
            flow_data["steps"].append({
                "step": 2,
                "action": "switch_input_osw",
                "success": True,
                "data": {"channel": self.input_channel}
            })
            time.sleep(0.5)  # 等待切换完成

            # 步骤3: 切换输出OSW到指定通道
            self.logger.info(f"步骤3: 切换输出OSW到通道{self.output_channel}")
            self.output_osw.switch_channel(self.output_channel)
            flow_data["steps"].append({
                "step": 3,
                "action": "switch_output_osw",
                "success": True,
                "data": {"channel": self.output_channel}
            })
            time.sleep(0.5)  # 等待切换完成

            # 步骤4: 启用SOP远程控制
            self.logger.info("步骤4: 启用SOP远程控制")
            remote_result = self.sop.set_remote_control_state(True)
            flow_data["steps"].append({
                "step": 4,
                "action": "enable_remote_control",
                "success": remote_result is not None,
                "data": {"response": remote_result}
            })
            time.sleep(0.5)

            # 步骤5: 设置三角扰乱速率
            self.logger.info(f"步骤5: 设置三角扰乱速率为{tri_rate}")
            rate_result = self.sop.set_tri_rate(tri_rate)
            flow_data["steps"].append({
                "step": 5,
                "action": "set_tri_rate",
                "success": rate_result is not None,
                "data": {"rate": tri_rate, "response": rate_result}
            })
            time.sleep(0.5)

            # 步骤6: 启用三角扰乱
            self.logger.info("步骤6: 启用三角扰乱")
            tri_result = self.sop.set_tri_state(True)
            flow_data["steps"].append({
                "step": 6,
                "action": "enable_tri_state",
                "success": tri_result is not None,
                "data": {"response": tri_result}
            })

            # 检查所有步骤是否成功
            all_success = all(step["success"] for step in flow_data["steps"])

            if all_success:
                self.logger.info("自动化SOP控制流程执行成功")
                flow_data["status"] = "active"
                flow_data["message"] = "SOP设备已启动，三角扰乱已激活"
                return {"success": True, "data": flow_data}
            else:
                failed_steps = [step for step in flow_data["steps"] if not step["success"]]
                error_msg = f"部分步骤执行失败: {[step['action'] for step in failed_steps]}"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "data": flow_data}

        except Exception as e:
            error_msg = f"自动化流程执行异常: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            flow_data["steps"].append({
                "step": "error",
                "action": "exception",
                "success": False,
                "data": {"error": str(e)}
            })
            return {"success": False, "error": error_msg, "data": flow_data}

    def handle_client(self, client_sock: socket.socket, client_addr: tuple):
        """处理客户端连接。

        客户端连接后自动执行SOP控制流程，断开时自动清理资源。

        Args:
            client_sock: 客户端socket
            client_addr: 客户端地址
        """
        client_id = f"{client_addr[0]}:{client_addr[1]}"
        self.logger.info(f"客户端连接: {client_id}")

        try:
            # 接收客户端请求（包含扰乱速率）
            data = client_sock.recv(4096)
            if not data:
                self.logger.warning(f"客户端 {client_id} 未发送数据即断开")
                return

            try:
                # 解析JSON请求
                request = json.loads(data.decode('utf-8'))
                tri_rate = request.get("tri_rate", 500)  # 默认500Hz

                self.logger.info(f"客户端 {client_id} 请求启动SOP，扰乱速率: {tri_rate}Hz")

                # 执行自动化SOP控制流程
                response = self.auto_setup_sop(client_id, tri_rate)

                # 发送响应
                client_sock.sendall((response + "\n").encode('utf-8'))

                # 如果设置成功，保持连接直到客户端断开
                if json.loads(response)["code"] == 200:
                    self.logger.info(f"客户端 {client_id} SOP控制流程启动成功，等待断开...")

                    # 保持连接，等待客户端断开
                    while True:
                        try:
                            # 设置较短的超时，定期检查连接状态
                            client_sock.settimeout(5.0)
                            data = client_sock.recv(1024)
                            if not data:
                                break
                        except socket.timeout:
                            # 超时是正常的，继续等待
                            continue
                        except:
                            # 其他异常表示连接断开
                            break

            except json.JSONDecodeError as e:
                error_response = self.create_response(
                    ResponseCode.ERROR,
                    f"JSON格式错误: {str(e)}",
                    None
                )
                client_sock.sendall((error_response + "\n").encode('utf-8'))

        except Exception as e:
            self.logger.error(f"处理客户端 {client_id} 时发生错误: {e}")
        finally:
            # 客户端断开时自动清理资源
            self._cleanup_client_resources(client_id)
            client_sock.close()

    def _cleanup_client_resources(self, client_id: str):
        """清理客户端资源。

        Args:
            client_id: 客户端标识
        """
        with self.lock:
            if self.current_client == client_id:
                self.logger.info(f"客户端 {client_id} 断开，开始清理资源...")

                try:
                    # 禁用三角扰乱
                    self.sop.set_tri_state(False)
                    self.logger.info("已禁用三角扰乱")

                    # 禁用远程控制
                    self.sop.set_remote_control_state(False)
                    self.logger.info("已禁用远程控制")

                    # 释放设备占用
                    self.current_client = None
                    self.logger.info(f"客户端 {client_id} 资源清理完成")

                except Exception as e:
                    self.logger.error(f"清理客户端 {client_id} 资源时出错: {e}")
                    # 即使清理失败也要释放占用
                    self.current_client = None

    def start(self):
        """启动自动化SOP服务器。"""
        server_sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_sock.bind((self.server_ip, self.server_port))
        server_sock.listen(5)

        self.logger.info(f"自动化SOP服务器已启动: {self.server_ip}:{self.server_port}")
        self.logger.info("等待客户端连接...")
        self.logger.info("客户端连接后将自动执行完整的SOP控制流程")

        try:
            while True:
                client_sock, client_addr = server_sock.accept()
                # 为每个客户端创建新线程
                thread = threading.Thread(
                    target=self.handle_client,
                    args=(client_sock, client_addr)
                )
                thread.daemon = True
                thread.start()

        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在关闭服务器...")
        finally:
            # 清理所有资源
            if self.current_client:
                self._cleanup_client_resources(self.current_client)
            server_sock.close()


if __name__ == "__main__":
    """主程序入口。"""
    print("自动化SOP控制服务器")
    print("=" * 40)
    print("功能: 客户端连接即自动执行完整SOP控制流程")
    print("包括: OSW切换 + SOP扰乱启动")
    print("=" * 40)

    server = AutoSOPServer()
    try:
        server.start()
    except Exception as e:
        logger.error(f"服务器启动失败: {e}", exc_info=True)
