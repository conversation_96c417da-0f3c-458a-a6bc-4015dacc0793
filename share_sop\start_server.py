"""SOP服务器启动脚本。

提供简单的服务器启动和管理功能。
"""

import sys
import os
import signal
import time

# 添加父目录到Python路径，以便导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server import SOPServer
from Tools.mylog import logger


class ServerManager:
    """服务器管理器。"""
    
    def __init__(self):
        self.server = None
        self.running = False
    
    def start(self):
        """启动服务器。"""
        try:
            print("正在启动SOP服务器...")
            print("=" * 50)
            
            # 创建服务器实例
            self.server = SOPServer()
            
            # 注册信号处理器
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # 显示服务器信息
            print(f"服务器地址: {self.server.server_ip}:{self.server.server_port}")
            print("按 Ctrl+C 停止服务器")
            print("=" * 50)
            
            self.running = True
            
            # 启动服务器
            self.server.start()
            
        except KeyboardInterrupt:
            print("\n收到中断信号")
        except Exception as e:
            logger.error(f"服务器启动失败: {e}", exc_info=True)
            print(f"服务器启动失败: {e}")
        finally:
            self._cleanup()
    
    def _signal_handler(self, signum, frame):
        """信号处理器。"""
        print(f"\n收到信号 {signum}，正在关闭服务器...")
        self.running = False
    
    def _cleanup(self):
        """清理资源。"""
        print("正在清理资源...")
        self.running = False
        print("服务器已关闭")


def show_help():
    """显示帮助信息。"""
    print("SOP服务器启动脚本")
    print("=" * 30)
    print("用法:")
    print("  python start_server.py        # 启动服务器")
    print("  python start_server.py -h     # 显示帮助")
    print("  python start_server.py --help # 显示帮助")
    print()
    print("功能:")
    print("  - 启动SOP控制服务器")
    print("  - 提供JSON格式的API接口")
    print("  - 自动资源管理")
    print("  - 支持多客户端并发访问")
    print()
    print("配置:")
    print("  编辑 config.ini 文件来修改服务器配置")


def check_dependencies():
    """检查依赖模块。"""
    try:
        from SOP.sop_controller import sop_controller
        from Config.config import config
        from Tools.mylog import logger
        return True
    except ImportError as e:
        print(f"依赖模块导入失败: {e}")
        print("请确保以下模块可用:")
        print("  - SOP.sop_controller")
        print("  - Config.config")
        print("  - Tools.mylog")
        return False


def main():
    """主函数。"""
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            show_help()
            return
    
    # 检查依赖
    if not check_dependencies():
        print("依赖检查失败，无法启动服务器")
        return
    
    # 启动服务器
    manager = ServerManager()
    manager.start()


if __name__ == "__main__":
    main()
